name: Build and Deploy

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  PROJECT_ID: kryptogo-wallet-data
  REGION: asia-northeast1
  REPOSITORY: kg-solana-data
  DB_USER: solana_data
  DB_NAME: solana_data

# Add concurrency configuration
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-and-push-images:
    runs-on: [self-hosted, Linux, c3]
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout code with submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          fetch-depth: 0

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker
        run: gcloud auth configure-docker asia-northeast1-docker.pkg.dev

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # skip because it's too slow
      # - name: Cache Docker layers
      #   uses: actions/cache@v3
      #   with:
      #     path: /tmp/.buildx-cache
      #     key: ${{ runner.os }}-buildx-${{ github.ref_name }}-${{ hashFiles('**/Cargo.toml', '**/go.mod') }}
      #     restore-keys: |
      #       ${{ runner.os }}-buildx-${{ github.ref_name }}-
      #       ${{ runner.os }}-buildx-

      - name: Build and Push Images
        env:
          PROJECT_ID: ${{ env.PROJECT_ID }}
          REPOSITORY: ${{ env.REPOSITORY }}
          GITHUB_SHA: ${{ github.sha }}
        run: |
          ./scripts/build.sh

  update-database:
    needs: [build-and-push-images]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Install Java
        uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "21"

      - name: Run Database Migration
        env:
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          chmod +x ./scripts/deploy-db.sh
          ./scripts/deploy-db.sh

  deploy-to-gke:
    needs: [update-database]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github-actions-pool/providers/github-provider"
          service_account: "<EMAIL>"

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Install gke-gcloud-auth-plugin
        run: |
          sudo apt-get update
          sudo apt-get install -y apt-transport-https ca-certificates gnupg
          echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
          curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
          sudo apt-get update && sudo apt-get install -y google-cloud-sdk-gke-gcloud-auth-plugin

      - name: Get GKE credentials
        id: get-credentials
        uses: "google-github-actions/get-gke-credentials@v2"
        with:
          cluster_name: "solana-data-cluster"
          location: "asia-northeast1"
          use_connect_gateway: "true"

      - name: Deploy to GKE
        run: |
          echo "Current context: $(kubectl config current-context)"
          echo "Deploying application..."
          kubectl apply -f kubernetes/deployment.yaml
          kubectl apply -f kubernetes/hpa.yaml
          kubectl apply -f kubernetes/cronjob.yaml
          kubectl rollout restart deployment -n solana-data
          kubectl rollout status deployment -n solana-data
