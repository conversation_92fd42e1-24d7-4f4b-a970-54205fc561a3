[package]
name = "old-faithful-proto"
version = "0.1.0"
edition = "2021"
publish = false

[dependencies]
anyhow = { workspace = true }
bincode = { workspace = true }
prost = { workspace = true }
prost_011 = { workspace = true }
serde = { workspace = true }
solana-storage-proto = { workspace = true }
solana-transaction-status = { workspace = true }
tonic = { workspace = true }
solana-hash = { workspace = true }
solana-transaction = { workspace = true }
solana-message = { workspace = true }
solana-transaction-error = { workspace = true }
solana-clock = { workspace = true }
[dev-dependencies]
const-hex = { workspace = true }
serde_json = { workspace = true }

[build-dependencies]
anyhow = { workspace = true }
protobuf-src = { workspace = true }
tonic-build = { workspace = true }
