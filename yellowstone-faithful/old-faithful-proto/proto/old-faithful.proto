syntax = "proto3";

package OldFaithful;
option go_package = "github.com/rpcpool/yellowstone-faithful/old-faithful-proto;old_faithful_grpc";

service OldFaithful {
  rpc GetVersion(VersionRequest) returns (VersionResponse);

  rpc GetBlock(BlockRequest) returns (BlockResponse);
  rpc GetBlockTime(BlockTimeRequest) returns (BlockTimeResponse);
  rpc GetTransaction(TransactionRequest) returns (TransactionResponse);

  rpc Get(stream GetRequest) returns (stream GetResponse);

  rpc StreamBlocks(StreamBlocksRequest) returns (stream BlockResponse);
  rpc StreamTransactions(StreamTransactionsRequest) returns (stream TransactionResponse);
}

message VersionRequest {}

message VersionResponse {
  string version = 1;
}

message BlockRequest {
  uint64 slot = 1;
}

message BlockResponse {
  bytes previous_blockhash = 1;
  bytes blockhash = 2;
  uint64 parent_slot = 3;
  uint64 slot = 4;
  int64 block_time = 5;
  uint64 block_height = 6;
  repeated Transaction transactions = 7;
  bytes rewards = 8; // protobuf or bincode (or empty)
  optional uint64 num_partitions = 9;
}

message BlockTimeRequest {
  uint64 slot = 1;
}

message BlockTimeResponse {
  int64 block_time = 1;
}

message TransactionRequest {
  bytes signature = 1;
}

message TransactionResponse {
  Transaction transaction = 1;
  uint64 slot = 2;
  int64 block_time = 3;
  optional uint64 index = 4; // position in the block
}

message Transaction {
  bytes transaction = 1;     // solana native transaction
  bytes meta = 2;            // bincode or protobuf
  optional uint64 index = 4; // position in the block
}

message GetRequest {
  uint64 id = 1;
  oneof request {
    VersionRequest version = 2;
    BlockTimeRequest block_time = 5;
    BlockRequest block = 3;
    TransactionRequest transaction = 4;
  }
}

message GetResponse {
  uint64 id = 1;
  oneof response {
    GetResponseError error = 2;
    VersionResponse version = 3;
    BlockTimeResponse block_time = 6;
    BlockResponse block = 4;
    TransactionResponse transaction = 5;
  }
}

enum GetResponseErrorCode {
  INTERNAL = 0;
  NOT_FOUND = 1;
}

message GetResponseError {
  GetResponseErrorCode code = 1;
  string message = 2;
}


message StreamBlocksRequest {
  uint64 start_slot = 1;
  optional uint64 end_slot = 2;
  optional StreamBlocksFilter filter = 3;
}

message StreamBlocksFilter {
  repeated string account_include = 1;  // Filter blocks/txns mentioning these accounts
}

message StreamTransactionsRequest {
  uint64 start_slot = 1;
  optional uint64 end_slot = 2;
  optional StreamTransactionsFilter filter = 3;
}

message StreamTransactionsFilter {
  optional bool vote = 1;
  optional bool failed = 2;
  repeated string account_include = 3;
  repeated string account_exclude = 4;
  repeated string account_required = 5;
}
