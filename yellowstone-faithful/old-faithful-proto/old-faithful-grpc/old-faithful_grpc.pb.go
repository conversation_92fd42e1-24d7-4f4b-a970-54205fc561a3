// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: old-faithful.proto

package old_faithful_grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	OldFaithful_GetVersion_FullMethodName         = "/OldFaithful.OldFaithful/GetVersion"
	OldFaithful_GetBlock_FullMethodName           = "/OldFaithful.OldFaithful/GetBlock"
	OldFaithful_GetBlockTime_FullMethodName       = "/OldFaithful.OldFaithful/GetBlockTime"
	OldFaithful_GetTransaction_FullMethodName     = "/OldFaithful.OldFaithful/GetTransaction"
	OldFaithful_Get_FullMethodName                = "/OldFaithful.OldFaithful/Get"
	OldFaithful_StreamBlocks_FullMethodName       = "/OldFaithful.OldFaithful/StreamBlocks"
	OldFaithful_StreamTransactions_FullMethodName = "/OldFaithful.OldFaithful/StreamTransactions"
)

// OldFaithfulClient is the client API for OldFaithful service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OldFaithfulClient interface {
	GetVersion(ctx context.Context, in *VersionRequest, opts ...grpc.CallOption) (*VersionResponse, error)
	GetBlock(ctx context.Context, in *BlockRequest, opts ...grpc.CallOption) (*BlockResponse, error)
	GetBlockTime(ctx context.Context, in *BlockTimeRequest, opts ...grpc.CallOption) (*BlockTimeResponse, error)
	GetTransaction(ctx context.Context, in *TransactionRequest, opts ...grpc.CallOption) (*TransactionResponse, error)
	Get(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[GetRequest, GetResponse], error)
	StreamBlocks(ctx context.Context, in *StreamBlocksRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[BlockResponse], error)
	StreamTransactions(ctx context.Context, in *StreamTransactionsRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[TransactionResponse], error)
}

type oldFaithfulClient struct {
	cc grpc.ClientConnInterface
}

func NewOldFaithfulClient(cc grpc.ClientConnInterface) OldFaithfulClient {
	return &oldFaithfulClient{cc}
}

func (c *oldFaithfulClient) GetVersion(ctx context.Context, in *VersionRequest, opts ...grpc.CallOption) (*VersionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VersionResponse)
	err := c.cc.Invoke(ctx, OldFaithful_GetVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oldFaithfulClient) GetBlock(ctx context.Context, in *BlockRequest, opts ...grpc.CallOption) (*BlockResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockResponse)
	err := c.cc.Invoke(ctx, OldFaithful_GetBlock_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oldFaithfulClient) GetBlockTime(ctx context.Context, in *BlockTimeRequest, opts ...grpc.CallOption) (*BlockTimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockTimeResponse)
	err := c.cc.Invoke(ctx, OldFaithful_GetBlockTime_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oldFaithfulClient) GetTransaction(ctx context.Context, in *TransactionRequest, opts ...grpc.CallOption) (*TransactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionResponse)
	err := c.cc.Invoke(ctx, OldFaithful_GetTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oldFaithfulClient) Get(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[GetRequest, GetResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &OldFaithful_ServiceDesc.Streams[0], OldFaithful_Get_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[GetRequest, GetResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type OldFaithful_GetClient = grpc.BidiStreamingClient[GetRequest, GetResponse]

func (c *oldFaithfulClient) StreamBlocks(ctx context.Context, in *StreamBlocksRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[BlockResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &OldFaithful_ServiceDesc.Streams[1], OldFaithful_StreamBlocks_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[StreamBlocksRequest, BlockResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type OldFaithful_StreamBlocksClient = grpc.ServerStreamingClient[BlockResponse]

func (c *oldFaithfulClient) StreamTransactions(ctx context.Context, in *StreamTransactionsRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[TransactionResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &OldFaithful_ServiceDesc.Streams[2], OldFaithful_StreamTransactions_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[StreamTransactionsRequest, TransactionResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type OldFaithful_StreamTransactionsClient = grpc.ServerStreamingClient[TransactionResponse]

// OldFaithfulServer is the server API for OldFaithful service.
// All implementations must embed UnimplementedOldFaithfulServer
// for forward compatibility.
type OldFaithfulServer interface {
	GetVersion(context.Context, *VersionRequest) (*VersionResponse, error)
	GetBlock(context.Context, *BlockRequest) (*BlockResponse, error)
	GetBlockTime(context.Context, *BlockTimeRequest) (*BlockTimeResponse, error)
	GetTransaction(context.Context, *TransactionRequest) (*TransactionResponse, error)
	Get(grpc.BidiStreamingServer[GetRequest, GetResponse]) error
	StreamBlocks(*StreamBlocksRequest, grpc.ServerStreamingServer[BlockResponse]) error
	StreamTransactions(*StreamTransactionsRequest, grpc.ServerStreamingServer[TransactionResponse]) error
	mustEmbedUnimplementedOldFaithfulServer()
}

// UnimplementedOldFaithfulServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOldFaithfulServer struct{}

func (UnimplementedOldFaithfulServer) GetVersion(context.Context, *VersionRequest) (*VersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersion not implemented")
}
func (UnimplementedOldFaithfulServer) GetBlock(context.Context, *BlockRequest) (*BlockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlock not implemented")
}
func (UnimplementedOldFaithfulServer) GetBlockTime(context.Context, *BlockTimeRequest) (*BlockTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlockTime not implemented")
}
func (UnimplementedOldFaithfulServer) GetTransaction(context.Context, *TransactionRequest) (*TransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransaction not implemented")
}
func (UnimplementedOldFaithfulServer) Get(grpc.BidiStreamingServer[GetRequest, GetResponse]) error {
	return status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedOldFaithfulServer) StreamBlocks(*StreamBlocksRequest, grpc.ServerStreamingServer[BlockResponse]) error {
	return status.Errorf(codes.Unimplemented, "method StreamBlocks not implemented")
}
func (UnimplementedOldFaithfulServer) StreamTransactions(*StreamTransactionsRequest, grpc.ServerStreamingServer[TransactionResponse]) error {
	return status.Errorf(codes.Unimplemented, "method StreamTransactions not implemented")
}
func (UnimplementedOldFaithfulServer) mustEmbedUnimplementedOldFaithfulServer() {}
func (UnimplementedOldFaithfulServer) testEmbeddedByValue()                     {}

// UnsafeOldFaithfulServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OldFaithfulServer will
// result in compilation errors.
type UnsafeOldFaithfulServer interface {
	mustEmbedUnimplementedOldFaithfulServer()
}

func RegisterOldFaithfulServer(s grpc.ServiceRegistrar, srv OldFaithfulServer) {
	// If the following call pancis, it indicates UnimplementedOldFaithfulServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OldFaithful_ServiceDesc, srv)
}

func _OldFaithful_GetVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OldFaithfulServer).GetVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OldFaithful_GetVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OldFaithfulServer).GetVersion(ctx, req.(*VersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OldFaithful_GetBlock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OldFaithfulServer).GetBlock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OldFaithful_GetBlock_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OldFaithfulServer).GetBlock(ctx, req.(*BlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OldFaithful_GetBlockTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OldFaithfulServer).GetBlockTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OldFaithful_GetBlockTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OldFaithfulServer).GetBlockTime(ctx, req.(*BlockTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OldFaithful_GetTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OldFaithfulServer).GetTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OldFaithful_GetTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OldFaithfulServer).GetTransaction(ctx, req.(*TransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OldFaithful_Get_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(OldFaithfulServer).Get(&grpc.GenericServerStream[GetRequest, GetResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type OldFaithful_GetServer = grpc.BidiStreamingServer[GetRequest, GetResponse]

func _OldFaithful_StreamBlocks_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(StreamBlocksRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OldFaithfulServer).StreamBlocks(m, &grpc.GenericServerStream[StreamBlocksRequest, BlockResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type OldFaithful_StreamBlocksServer = grpc.ServerStreamingServer[BlockResponse]

func _OldFaithful_StreamTransactions_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(StreamTransactionsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(OldFaithfulServer).StreamTransactions(m, &grpc.GenericServerStream[StreamTransactionsRequest, TransactionResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type OldFaithful_StreamTransactionsServer = grpc.ServerStreamingServer[TransactionResponse]

// OldFaithful_ServiceDesc is the grpc.ServiceDesc for OldFaithful service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OldFaithful_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "OldFaithful.OldFaithful",
	HandlerType: (*OldFaithfulServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVersion",
			Handler:    _OldFaithful_GetVersion_Handler,
		},
		{
			MethodName: "GetBlock",
			Handler:    _OldFaithful_GetBlock_Handler,
		},
		{
			MethodName: "GetBlockTime",
			Handler:    _OldFaithful_GetBlockTime_Handler,
		},
		{
			MethodName: "GetTransaction",
			Handler:    _OldFaithful_GetTransaction_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Get",
			Handler:       _OldFaithful_Get_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "StreamBlocks",
			Handler:       _OldFaithful_StreamBlocks_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "StreamTransactions",
			Handler:       _OldFaithful_StreamTransactions_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "old-faithful.proto",
}
