// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.28.3
// source: old-faithful.proto

package old_faithful_grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetResponseErrorCode int32

const (
	GetResponseErrorCode_INTERNAL  GetResponseErrorCode = 0
	GetResponseErrorCode_NOT_FOUND GetResponseErrorCode = 1
)

// Enum value maps for GetResponseErrorCode.
var (
	GetResponseErrorCode_name = map[int32]string{
		0: "INTERNAL",
		1: "NOT_FOUND",
	}
	GetResponseErrorCode_value = map[string]int32{
		"INTERNAL":  0,
		"NOT_FOUND": 1,
	}
)

func (x GetResponseErrorCode) Enum() *GetResponseErrorCode {
	p := new(GetResponseErrorCode)
	*p = x
	return p
}

func (x GetResponseErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetResponseErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_old_faithful_proto_enumTypes[0].Descriptor()
}

func (GetResponseErrorCode) Type() protoreflect.EnumType {
	return &file_old_faithful_proto_enumTypes[0]
}

func (x GetResponseErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetResponseErrorCode.Descriptor instead.
func (GetResponseErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{0}
}

type VersionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *VersionRequest) Reset() {
	*x = VersionRequest{}
	mi := &file_old_faithful_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionRequest) ProtoMessage() {}

func (x *VersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionRequest.ProtoReflect.Descriptor instead.
func (*VersionRequest) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{0}
}

type VersionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *VersionResponse) Reset() {
	*x = VersionResponse{}
	mi := &file_old_faithful_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionResponse) ProtoMessage() {}

func (x *VersionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionResponse.ProtoReflect.Descriptor instead.
func (*VersionResponse) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{1}
}

func (x *VersionResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type BlockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot uint64 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
}

func (x *BlockRequest) Reset() {
	*x = BlockRequest{}
	mi := &file_old_faithful_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockRequest) ProtoMessage() {}

func (x *BlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockRequest.ProtoReflect.Descriptor instead.
func (*BlockRequest) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{2}
}

func (x *BlockRequest) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

type BlockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreviousBlockhash []byte         `protobuf:"bytes,1,opt,name=previous_blockhash,json=previousBlockhash,proto3" json:"previous_blockhash,omitempty"`
	Blockhash         []byte         `protobuf:"bytes,2,opt,name=blockhash,proto3" json:"blockhash,omitempty"`
	ParentSlot        uint64         `protobuf:"varint,3,opt,name=parent_slot,json=parentSlot,proto3" json:"parent_slot,omitempty"`
	Slot              uint64         `protobuf:"varint,4,opt,name=slot,proto3" json:"slot,omitempty"`
	BlockTime         int64          `protobuf:"varint,5,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	BlockHeight       uint64         `protobuf:"varint,6,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
	Transactions      []*Transaction `protobuf:"bytes,7,rep,name=transactions,proto3" json:"transactions,omitempty"`
	Rewards           []byte         `protobuf:"bytes,8,opt,name=rewards,proto3" json:"rewards,omitempty"` // protobuf or bincode (or empty)
	NumPartitions     *uint64        `protobuf:"varint,9,opt,name=num_partitions,json=numPartitions,proto3,oneof" json:"num_partitions,omitempty"`
}

func (x *BlockResponse) Reset() {
	*x = BlockResponse{}
	mi := &file_old_faithful_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockResponse) ProtoMessage() {}

func (x *BlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockResponse.ProtoReflect.Descriptor instead.
func (*BlockResponse) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{3}
}

func (x *BlockResponse) GetPreviousBlockhash() []byte {
	if x != nil {
		return x.PreviousBlockhash
	}
	return nil
}

func (x *BlockResponse) GetBlockhash() []byte {
	if x != nil {
		return x.Blockhash
	}
	return nil
}

func (x *BlockResponse) GetParentSlot() uint64 {
	if x != nil {
		return x.ParentSlot
	}
	return 0
}

func (x *BlockResponse) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *BlockResponse) GetBlockTime() int64 {
	if x != nil {
		return x.BlockTime
	}
	return 0
}

func (x *BlockResponse) GetBlockHeight() uint64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *BlockResponse) GetTransactions() []*Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *BlockResponse) GetRewards() []byte {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *BlockResponse) GetNumPartitions() uint64 {
	if x != nil && x.NumPartitions != nil {
		return *x.NumPartitions
	}
	return 0
}

type BlockTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot uint64 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
}

func (x *BlockTimeRequest) Reset() {
	*x = BlockTimeRequest{}
	mi := &file_old_faithful_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTimeRequest) ProtoMessage() {}

func (x *BlockTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTimeRequest.ProtoReflect.Descriptor instead.
func (*BlockTimeRequest) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{4}
}

func (x *BlockTimeRequest) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

type BlockTimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockTime int64 `protobuf:"varint,1,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
}

func (x *BlockTimeResponse) Reset() {
	*x = BlockTimeResponse{}
	mi := &file_old_faithful_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTimeResponse) ProtoMessage() {}

func (x *BlockTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTimeResponse.ProtoReflect.Descriptor instead.
func (*BlockTimeResponse) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{5}
}

func (x *BlockTimeResponse) GetBlockTime() int64 {
	if x != nil {
		return x.BlockTime
	}
	return 0
}

type TransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Signature []byte `protobuf:"bytes,1,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *TransactionRequest) Reset() {
	*x = TransactionRequest{}
	mi := &file_old_faithful_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionRequest) ProtoMessage() {}

func (x *TransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionRequest.ProtoReflect.Descriptor instead.
func (*TransactionRequest) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{6}
}

func (x *TransactionRequest) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

type TransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transaction *Transaction `protobuf:"bytes,1,opt,name=transaction,proto3" json:"transaction,omitempty"`
	Slot        uint64       `protobuf:"varint,2,opt,name=slot,proto3" json:"slot,omitempty"`
	BlockTime   int64        `protobuf:"varint,3,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	Index       *uint64      `protobuf:"varint,4,opt,name=index,proto3,oneof" json:"index,omitempty"` // position in the block
}

func (x *TransactionResponse) Reset() {
	*x = TransactionResponse{}
	mi := &file_old_faithful_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionResponse) ProtoMessage() {}

func (x *TransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionResponse.ProtoReflect.Descriptor instead.
func (*TransactionResponse) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{7}
}

func (x *TransactionResponse) GetTransaction() *Transaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *TransactionResponse) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *TransactionResponse) GetBlockTime() int64 {
	if x != nil {
		return x.BlockTime
	}
	return 0
}

func (x *TransactionResponse) GetIndex() uint64 {
	if x != nil && x.Index != nil {
		return *x.Index
	}
	return 0
}

type Transaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transaction []byte  `protobuf:"bytes,1,opt,name=transaction,proto3" json:"transaction,omitempty"` // solana native transaction
	Meta        []byte  `protobuf:"bytes,2,opt,name=meta,proto3" json:"meta,omitempty"`               // bincode or protobuf
	Index       *uint64 `protobuf:"varint,4,opt,name=index,proto3,oneof" json:"index,omitempty"`      // position in the block
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	mi := &file_old_faithful_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{8}
}

func (x *Transaction) GetTransaction() []byte {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *Transaction) GetMeta() []byte {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *Transaction) GetIndex() uint64 {
	if x != nil && x.Index != nil {
		return *x.Index
	}
	return 0
}

type GetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Types that are assignable to Request:
	//
	//	*GetRequest_Version
	//	*GetRequest_BlockTime
	//	*GetRequest_Block
	//	*GetRequest_Transaction
	Request isGetRequest_Request `protobuf_oneof:"request"`
}

func (x *GetRequest) Reset() {
	*x = GetRequest{}
	mi := &file_old_faithful_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequest) ProtoMessage() {}

func (x *GetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequest.ProtoReflect.Descriptor instead.
func (*GetRequest) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{9}
}

func (x *GetRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (m *GetRequest) GetRequest() isGetRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *GetRequest) GetVersion() *VersionRequest {
	if x, ok := x.GetRequest().(*GetRequest_Version); ok {
		return x.Version
	}
	return nil
}

func (x *GetRequest) GetBlockTime() *BlockTimeRequest {
	if x, ok := x.GetRequest().(*GetRequest_BlockTime); ok {
		return x.BlockTime
	}
	return nil
}

func (x *GetRequest) GetBlock() *BlockRequest {
	if x, ok := x.GetRequest().(*GetRequest_Block); ok {
		return x.Block
	}
	return nil
}

func (x *GetRequest) GetTransaction() *TransactionRequest {
	if x, ok := x.GetRequest().(*GetRequest_Transaction); ok {
		return x.Transaction
	}
	return nil
}

type isGetRequest_Request interface {
	isGetRequest_Request()
}

type GetRequest_Version struct {
	Version *VersionRequest `protobuf:"bytes,2,opt,name=version,proto3,oneof"`
}

type GetRequest_BlockTime struct {
	BlockTime *BlockTimeRequest `protobuf:"bytes,5,opt,name=block_time,json=blockTime,proto3,oneof"`
}

type GetRequest_Block struct {
	Block *BlockRequest `protobuf:"bytes,3,opt,name=block,proto3,oneof"`
}

type GetRequest_Transaction struct {
	Transaction *TransactionRequest `protobuf:"bytes,4,opt,name=transaction,proto3,oneof"`
}

func (*GetRequest_Version) isGetRequest_Request() {}

func (*GetRequest_BlockTime) isGetRequest_Request() {}

func (*GetRequest_Block) isGetRequest_Request() {}

func (*GetRequest_Transaction) isGetRequest_Request() {}

type GetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Types that are assignable to Response:
	//
	//	*GetResponse_Error
	//	*GetResponse_Version
	//	*GetResponse_BlockTime
	//	*GetResponse_Block
	//	*GetResponse_Transaction
	Response isGetResponse_Response `protobuf_oneof:"response"`
}

func (x *GetResponse) Reset() {
	*x = GetResponse{}
	mi := &file_old_faithful_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResponse) ProtoMessage() {}

func (x *GetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResponse.ProtoReflect.Descriptor instead.
func (*GetResponse) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{10}
}

func (x *GetResponse) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (m *GetResponse) GetResponse() isGetResponse_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *GetResponse) GetError() *GetResponseError {
	if x, ok := x.GetResponse().(*GetResponse_Error); ok {
		return x.Error
	}
	return nil
}

func (x *GetResponse) GetVersion() *VersionResponse {
	if x, ok := x.GetResponse().(*GetResponse_Version); ok {
		return x.Version
	}
	return nil
}

func (x *GetResponse) GetBlockTime() *BlockTimeResponse {
	if x, ok := x.GetResponse().(*GetResponse_BlockTime); ok {
		return x.BlockTime
	}
	return nil
}

func (x *GetResponse) GetBlock() *BlockResponse {
	if x, ok := x.GetResponse().(*GetResponse_Block); ok {
		return x.Block
	}
	return nil
}

func (x *GetResponse) GetTransaction() *TransactionResponse {
	if x, ok := x.GetResponse().(*GetResponse_Transaction); ok {
		return x.Transaction
	}
	return nil
}

type isGetResponse_Response interface {
	isGetResponse_Response()
}

type GetResponse_Error struct {
	Error *GetResponseError `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

type GetResponse_Version struct {
	Version *VersionResponse `protobuf:"bytes,3,opt,name=version,proto3,oneof"`
}

type GetResponse_BlockTime struct {
	BlockTime *BlockTimeResponse `protobuf:"bytes,6,opt,name=block_time,json=blockTime,proto3,oneof"`
}

type GetResponse_Block struct {
	Block *BlockResponse `protobuf:"bytes,4,opt,name=block,proto3,oneof"`
}

type GetResponse_Transaction struct {
	Transaction *TransactionResponse `protobuf:"bytes,5,opt,name=transaction,proto3,oneof"`
}

func (*GetResponse_Error) isGetResponse_Response() {}

func (*GetResponse_Version) isGetResponse_Response() {}

func (*GetResponse_BlockTime) isGetResponse_Response() {}

func (*GetResponse_Block) isGetResponse_Response() {}

func (*GetResponse_Transaction) isGetResponse_Response() {}

type GetResponseError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    GetResponseErrorCode `protobuf:"varint,1,opt,name=code,proto3,enum=OldFaithful.GetResponseErrorCode" json:"code,omitempty"`
	Message string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *GetResponseError) Reset() {
	*x = GetResponseError{}
	mi := &file_old_faithful_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetResponseError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResponseError) ProtoMessage() {}

func (x *GetResponseError) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResponseError.ProtoReflect.Descriptor instead.
func (*GetResponseError) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{11}
}

func (x *GetResponseError) GetCode() GetResponseErrorCode {
	if x != nil {
		return x.Code
	}
	return GetResponseErrorCode_INTERNAL
}

func (x *GetResponseError) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type StreamBlocksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartSlot uint64              `protobuf:"varint,1,opt,name=start_slot,json=startSlot,proto3" json:"start_slot,omitempty"`
	EndSlot   *uint64             `protobuf:"varint,2,opt,name=end_slot,json=endSlot,proto3,oneof" json:"end_slot,omitempty"`
	Filter    *StreamBlocksFilter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *StreamBlocksRequest) Reset() {
	*x = StreamBlocksRequest{}
	mi := &file_old_faithful_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamBlocksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamBlocksRequest) ProtoMessage() {}

func (x *StreamBlocksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamBlocksRequest.ProtoReflect.Descriptor instead.
func (*StreamBlocksRequest) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{12}
}

func (x *StreamBlocksRequest) GetStartSlot() uint64 {
	if x != nil {
		return x.StartSlot
	}
	return 0
}

func (x *StreamBlocksRequest) GetEndSlot() uint64 {
	if x != nil && x.EndSlot != nil {
		return *x.EndSlot
	}
	return 0
}

func (x *StreamBlocksRequest) GetFilter() *StreamBlocksFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type StreamBlocksFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountInclude []string `protobuf:"bytes,1,rep,name=account_include,json=accountInclude,proto3" json:"account_include,omitempty"` // Filter blocks/txns mentioning these accounts
}

func (x *StreamBlocksFilter) Reset() {
	*x = StreamBlocksFilter{}
	mi := &file_old_faithful_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamBlocksFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamBlocksFilter) ProtoMessage() {}

func (x *StreamBlocksFilter) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamBlocksFilter.ProtoReflect.Descriptor instead.
func (*StreamBlocksFilter) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{13}
}

func (x *StreamBlocksFilter) GetAccountInclude() []string {
	if x != nil {
		return x.AccountInclude
	}
	return nil
}

type StreamTransactionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartSlot uint64                    `protobuf:"varint,1,opt,name=start_slot,json=startSlot,proto3" json:"start_slot,omitempty"`
	EndSlot   *uint64                   `protobuf:"varint,2,opt,name=end_slot,json=endSlot,proto3,oneof" json:"end_slot,omitempty"`
	Filter    *StreamTransactionsFilter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *StreamTransactionsRequest) Reset() {
	*x = StreamTransactionsRequest{}
	mi := &file_old_faithful_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamTransactionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamTransactionsRequest) ProtoMessage() {}

func (x *StreamTransactionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamTransactionsRequest.ProtoReflect.Descriptor instead.
func (*StreamTransactionsRequest) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{14}
}

func (x *StreamTransactionsRequest) GetStartSlot() uint64 {
	if x != nil {
		return x.StartSlot
	}
	return 0
}

func (x *StreamTransactionsRequest) GetEndSlot() uint64 {
	if x != nil && x.EndSlot != nil {
		return *x.EndSlot
	}
	return 0
}

func (x *StreamTransactionsRequest) GetFilter() *StreamTransactionsFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type StreamTransactionsFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vote            *bool    `protobuf:"varint,1,opt,name=vote,proto3,oneof" json:"vote,omitempty"`
	Failed          *bool    `protobuf:"varint,2,opt,name=failed,proto3,oneof" json:"failed,omitempty"`
	AccountInclude  []string `protobuf:"bytes,3,rep,name=account_include,json=accountInclude,proto3" json:"account_include,omitempty"`
	AccountExclude  []string `protobuf:"bytes,4,rep,name=account_exclude,json=accountExclude,proto3" json:"account_exclude,omitempty"`
	AccountRequired []string `protobuf:"bytes,5,rep,name=account_required,json=accountRequired,proto3" json:"account_required,omitempty"`
}

func (x *StreamTransactionsFilter) Reset() {
	*x = StreamTransactionsFilter{}
	mi := &file_old_faithful_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamTransactionsFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamTransactionsFilter) ProtoMessage() {}

func (x *StreamTransactionsFilter) ProtoReflect() protoreflect.Message {
	mi := &file_old_faithful_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamTransactionsFilter.ProtoReflect.Descriptor instead.
func (*StreamTransactionsFilter) Descriptor() ([]byte, []int) {
	return file_old_faithful_proto_rawDescGZIP(), []int{15}
}

func (x *StreamTransactionsFilter) GetVote() bool {
	if x != nil && x.Vote != nil {
		return *x.Vote
	}
	return false
}

func (x *StreamTransactionsFilter) GetFailed() bool {
	if x != nil && x.Failed != nil {
		return *x.Failed
	}
	return false
}

func (x *StreamTransactionsFilter) GetAccountInclude() []string {
	if x != nil {
		return x.AccountInclude
	}
	return nil
}

func (x *StreamTransactionsFilter) GetAccountExclude() []string {
	if x != nil {
		return x.AccountExclude
	}
	return nil
}

func (x *StreamTransactionsFilter) GetAccountRequired() []string {
	if x != nil {
		return x.AccountRequired
	}
	return nil
}

var File_old_faithful_proto protoreflect.FileDescriptor

var file_old_faithful_proto_rawDesc = []byte{
	0x0a, 0x12, 0x6f, 0x6c, 0x64, 0x2d, 0x66, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75,
	0x6c, 0x22, 0x10, 0x0a, 0x0e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x2b, 0x0a, 0x0f, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x22, 0x0a, 0x0c, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x73, 0x6c, 0x6f, 0x74, 0x22, 0xea, 0x02, 0x0a, 0x0d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f,
	0x75, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x11, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x68, 0x61, 0x73, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61,
	0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68,
	0x61, 0x73, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6c,
	0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x53, 0x6c, 0x6f, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x3c, 0x0a, 0x0c, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x2a, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0d, 0x6e, 0x75,
	0x6d, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x26, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x22, 0x32, 0x0a, 0x11, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x32, 0x0a,
	0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x22, 0xa9, 0x01, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x68, 0x0a,
	0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6d, 0x65,
	0x74, 0x61, 0x12, 0x19, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x98, 0x02, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x37, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69,
	0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x3e, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75,
	0x6c, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x31, 0x0a, 0x05, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x12, 0x43, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69,
	0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0xd5, 0x02, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x35, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x4f, 0x6c, 0x64,
	0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69,
	0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75,
	0x6c, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x00, 0x52, 0x05, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x44, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x00, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x63, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x35,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x4f,
	0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0xaa, 0x01, 0x0a, 0x13, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x6c,
	0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x53,
	0x6c, 0x6f, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74,
	0x68, 0x66, 0x75, 0x6c, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x6c, 0x6f,
	0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x3d, 0x0a, 0x12,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x22, 0xb6, 0x01, 0x0a, 0x19,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x73, 0x6c, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x53, 0x6c, 0x6f, 0x74, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61,
	0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48,
	0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x22, 0xe1, 0x01, 0x0a, 0x18, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x17, 0x0a, 0x04, 0x76, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x00, 0x52, 0x04, 0x76, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x06, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x76, 0x6f, 0x74, 0x65, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x2a, 0x33, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x32, 0xad, 0x04,
	0x0a, 0x0b, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x12, 0x47, 0x0a,
	0x0a, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x2e, 0x4f, 0x6c,
	0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61,
	0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x12, 0x19, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c,
	0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x2e, 0x4f, 0x6c, 0x64, 0x46,
	0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61,
	0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x4f, 0x6c, 0x64,
	0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x4f, 0x6c,
	0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a,
	0x03, 0x47, 0x65, 0x74, 0x12, 0x17, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66,
	0x75, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e,
	0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x28, 0x01, 0x30, 0x01, 0x12, 0x4e, 0x0a, 0x0c, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x20, 0x2e, 0x4f, 0x6c,
	0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x30, 0x01, 0x12, 0x60, 0x0a, 0x12, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x26, 0x2e, 0x4f, 0x6c, 0x64, 0x46, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x4f, 0x6c, 0x64, 0x46,
	0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x30, 0x01, 0x42, 0x4e, 0x5a,
	0x4c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x70, 0x63, 0x70,
	0x6f, 0x6f, 0x6c, 0x2f, 0x79, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x74, 0x6f, 0x6e, 0x65, 0x2d,
	0x66, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x2f, 0x6f, 0x6c, 0x64, 0x2d, 0x66, 0x61, 0x69,
	0x74, 0x68, 0x66, 0x75, 0x6c, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x6f, 0x6c, 0x64, 0x5f,
	0x66, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c, 0x5f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_old_faithful_proto_rawDescOnce sync.Once
	file_old_faithful_proto_rawDescData = file_old_faithful_proto_rawDesc
)

func file_old_faithful_proto_rawDescGZIP() []byte {
	file_old_faithful_proto_rawDescOnce.Do(func() {
		file_old_faithful_proto_rawDescData = protoimpl.X.CompressGZIP(file_old_faithful_proto_rawDescData)
	})
	return file_old_faithful_proto_rawDescData
}

var file_old_faithful_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_old_faithful_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_old_faithful_proto_goTypes = []any{
	(GetResponseErrorCode)(0),         // 0: OldFaithful.GetResponseErrorCode
	(*VersionRequest)(nil),            // 1: OldFaithful.VersionRequest
	(*VersionResponse)(nil),           // 2: OldFaithful.VersionResponse
	(*BlockRequest)(nil),              // 3: OldFaithful.BlockRequest
	(*BlockResponse)(nil),             // 4: OldFaithful.BlockResponse
	(*BlockTimeRequest)(nil),          // 5: OldFaithful.BlockTimeRequest
	(*BlockTimeResponse)(nil),         // 6: OldFaithful.BlockTimeResponse
	(*TransactionRequest)(nil),        // 7: OldFaithful.TransactionRequest
	(*TransactionResponse)(nil),       // 8: OldFaithful.TransactionResponse
	(*Transaction)(nil),               // 9: OldFaithful.Transaction
	(*GetRequest)(nil),                // 10: OldFaithful.GetRequest
	(*GetResponse)(nil),               // 11: OldFaithful.GetResponse
	(*GetResponseError)(nil),          // 12: OldFaithful.GetResponseError
	(*StreamBlocksRequest)(nil),       // 13: OldFaithful.StreamBlocksRequest
	(*StreamBlocksFilter)(nil),        // 14: OldFaithful.StreamBlocksFilter
	(*StreamTransactionsRequest)(nil), // 15: OldFaithful.StreamTransactionsRequest
	(*StreamTransactionsFilter)(nil),  // 16: OldFaithful.StreamTransactionsFilter
}
var file_old_faithful_proto_depIdxs = []int32{
	9,  // 0: OldFaithful.BlockResponse.transactions:type_name -> OldFaithful.Transaction
	9,  // 1: OldFaithful.TransactionResponse.transaction:type_name -> OldFaithful.Transaction
	1,  // 2: OldFaithful.GetRequest.version:type_name -> OldFaithful.VersionRequest
	5,  // 3: OldFaithful.GetRequest.block_time:type_name -> OldFaithful.BlockTimeRequest
	3,  // 4: OldFaithful.GetRequest.block:type_name -> OldFaithful.BlockRequest
	7,  // 5: OldFaithful.GetRequest.transaction:type_name -> OldFaithful.TransactionRequest
	12, // 6: OldFaithful.GetResponse.error:type_name -> OldFaithful.GetResponseError
	2,  // 7: OldFaithful.GetResponse.version:type_name -> OldFaithful.VersionResponse
	6,  // 8: OldFaithful.GetResponse.block_time:type_name -> OldFaithful.BlockTimeResponse
	4,  // 9: OldFaithful.GetResponse.block:type_name -> OldFaithful.BlockResponse
	8,  // 10: OldFaithful.GetResponse.transaction:type_name -> OldFaithful.TransactionResponse
	0,  // 11: OldFaithful.GetResponseError.code:type_name -> OldFaithful.GetResponseErrorCode
	14, // 12: OldFaithful.StreamBlocksRequest.filter:type_name -> OldFaithful.StreamBlocksFilter
	16, // 13: OldFaithful.StreamTransactionsRequest.filter:type_name -> OldFaithful.StreamTransactionsFilter
	1,  // 14: OldFaithful.OldFaithful.GetVersion:input_type -> OldFaithful.VersionRequest
	3,  // 15: OldFaithful.OldFaithful.GetBlock:input_type -> OldFaithful.BlockRequest
	5,  // 16: OldFaithful.OldFaithful.GetBlockTime:input_type -> OldFaithful.BlockTimeRequest
	7,  // 17: OldFaithful.OldFaithful.GetTransaction:input_type -> OldFaithful.TransactionRequest
	10, // 18: OldFaithful.OldFaithful.Get:input_type -> OldFaithful.GetRequest
	13, // 19: OldFaithful.OldFaithful.StreamBlocks:input_type -> OldFaithful.StreamBlocksRequest
	15, // 20: OldFaithful.OldFaithful.StreamTransactions:input_type -> OldFaithful.StreamTransactionsRequest
	2,  // 21: OldFaithful.OldFaithful.GetVersion:output_type -> OldFaithful.VersionResponse
	4,  // 22: OldFaithful.OldFaithful.GetBlock:output_type -> OldFaithful.BlockResponse
	6,  // 23: OldFaithful.OldFaithful.GetBlockTime:output_type -> OldFaithful.BlockTimeResponse
	8,  // 24: OldFaithful.OldFaithful.GetTransaction:output_type -> OldFaithful.TransactionResponse
	11, // 25: OldFaithful.OldFaithful.Get:output_type -> OldFaithful.GetResponse
	4,  // 26: OldFaithful.OldFaithful.StreamBlocks:output_type -> OldFaithful.BlockResponse
	8,  // 27: OldFaithful.OldFaithful.StreamTransactions:output_type -> OldFaithful.TransactionResponse
	21, // [21:28] is the sub-list for method output_type
	14, // [14:21] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_old_faithful_proto_init() }
func file_old_faithful_proto_init() {
	if File_old_faithful_proto != nil {
		return
	}
	file_old_faithful_proto_msgTypes[3].OneofWrappers = []any{}
	file_old_faithful_proto_msgTypes[7].OneofWrappers = []any{}
	file_old_faithful_proto_msgTypes[8].OneofWrappers = []any{}
	file_old_faithful_proto_msgTypes[9].OneofWrappers = []any{
		(*GetRequest_Version)(nil),
		(*GetRequest_BlockTime)(nil),
		(*GetRequest_Block)(nil),
		(*GetRequest_Transaction)(nil),
	}
	file_old_faithful_proto_msgTypes[10].OneofWrappers = []any{
		(*GetResponse_Error)(nil),
		(*GetResponse_Version)(nil),
		(*GetResponse_BlockTime)(nil),
		(*GetResponse_Block)(nil),
		(*GetResponse_Transaction)(nil),
	}
	file_old_faithful_proto_msgTypes[12].OneofWrappers = []any{}
	file_old_faithful_proto_msgTypes[14].OneofWrappers = []any{}
	file_old_faithful_proto_msgTypes[15].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_old_faithful_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_old_faithful_proto_goTypes,
		DependencyIndexes: file_old_faithful_proto_depIdxs,
		EnumInfos:         file_old_faithful_proto_enumTypes,
		MessageInfos:      file_old_faithful_proto_msgTypes,
	}.Build()
	File_old_faithful_proto = out.File
	file_old_faithful_proto_rawDesc = nil
	file_old_faithful_proto_goTypes = nil
	file_old_faithful_proto_depIdxs = nil
}
