use {
    crate::node::Kind,
    cid::Cid,
    std::{error::Error, vec::Vec},
};

// type (
// 	List__Shredding []Shredding
// 	Block           struct {
// 		Kind      int
// 		Slot      int
// 		Shredding List__Shredding
// 		Entries   List__Link
// 		Meta      SlotMeta
// 		Rewards   datamodel.Link
// 	}
// )
#[derive(Clone, PartialEq, Eq, Hash, Debug)]
pub struct Block {
    pub kind: u64,
    pub slot: u64,
    pub shredding: Vec<Shredding>,
    pub entries: Vec<Cid>,
    pub meta: SlotMeta,
    pub rewards: Cid,
}

impl Block {
    pub fn from_bytes(data: Vec<u8>) -> Result<Block, Box<dyn Error>> {
        let decoded_data: serde_cbor::Value = serde_cbor::from_slice(&data).unwrap();
        let block = Block::from_cbor(decoded_data)?;
        Ok(block)
    }

    // from serde_cbor::Value
    pub fn from_cbor(val: serde_cbor::Value) -> Result<Block, Box<dyn Error>> {
        let mut block = Block {
            kind: 0,
            slot: 0,
            shredding: vec![],
            entries: vec![],
            meta: SlotMeta {
                parent_slot: 0,
                blocktime: 0,
                block_height: None,
            },
            rewards: Cid::try_from(
                vec![
                    1, 113, 18, 32, 56, 148, 167, 251, 237, 117, 200, 226, 181, 134, 79, 115, 131,
                    220, 232, 143, 20, 67, 224, 179, 48, 130, 197, 123, 226, 85, 85, 56, 38, 84,
                    106, 225,
                ]
                .as_slice(),
            )
            .unwrap(),
        };

        if let serde_cbor::Value::Array(array) = val {
            // println!("Kind: {:?}", array[0]);
            if let Some(serde_cbor::Value::Integer(kind)) = array.first() {
                // println!("Kind: {:?}", Kind::from_u64(kind as u64).unwrap().to_string());
                block.kind = *kind as u64;

                if *kind as u64 != Kind::Block as u64 {
                    return Err(Box::new(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        std::format!(
                            "Wrong kind for Block. Expected {:?}, got {:?}",
                            Kind::Block,
                            kind
                        ),
                    )));
                }
            }
            if let Some(serde_cbor::Value::Integer(slot)) = array.get(1) {
                block.slot = *slot as u64;
            }

            if let Some(serde_cbor::Value::Array(shredding)) = &array.get(2) {
                for shred in shredding {
                    if let serde_cbor::Value::Array(shred) = shred {
                        block
                            .shredding
                            .push(Shredding::from_cbor(serde_cbor::Value::Array(
                                shred.clone(),
                            )));
                    }
                }
            }

            if let Some(serde_cbor::Value::Array(entries)) = &array.get(3) {
                for entry in entries {
                    if let serde_cbor::Value::Bytes(entry) = entry {
                        block
                            .entries
                            .push(Cid::try_from(entry[1..].to_vec()).unwrap());
                    }
                }
            }

            if let Some(serde_cbor::Value::Array(meta)) = &array.get(4) {
                block.meta = SlotMeta::from_cbor(serde_cbor::Value::Array(meta.clone()));
            }

            if let Some(serde_cbor::Value::Bytes(rewards)) = &array.get(5) {
                block.rewards = Cid::try_from(rewards[1..].to_vec()).unwrap();
            }
        }
        Ok(block)
    }

    pub fn to_json(&self) -> serde_json::Value {
        let mut shredding = vec![];
        for shred in &self.shredding {
            shredding.push(shred.to_json());
        }

        let mut entries = vec![];
        for entry in &self.entries {
            entries.push(serde_json::json!({
                "/": entry.to_string()
            }));
        }

        let mut map = serde_json::Map::new();
        map.insert("kind".to_string(), serde_json::Value::from(self.kind));
        map.insert("slot".to_string(), serde_json::Value::from(self.slot));
        map.insert("shredding".to_string(), serde_json::Value::from(shredding));
        map.insert("entries".to_string(), serde_json::Value::from(entries));
        map.insert("meta".to_string(), self.meta.to_json());
        map.insert(
            "rewards".to_string(),
            serde_json::json!({
                "/": self.rewards.to_string()
            }),
        );

        serde_json::Value::from(map)
    }
}

#[cfg(test)]
mod block_tests {
    use super::*;

    #[test]
    fn test_block() {
        let block = Block {
            kind: 2,
            slot: 1,
            shredding: vec![Shredding {
                entry_end_idx: 1,
                shred_end_idx: 1,
            }],
            entries: vec![Cid::try_from(
                vec![
                    1, 113, 18, 32, 56, 148, 167, 251, 237, 117, 200, 226, 181, 134, 79, 115, 131,
                    220, 232, 143, 20, 67, 224, 179, 48, 130, 197, 123, 226, 85, 85, 56, 38, 84,
                    106, 225,
                ]
                .as_slice(),
            )
            .unwrap()],
            meta: SlotMeta {
                parent_slot: 1,
                blocktime: 1,
                block_height: Some(1),
            },
            rewards: Cid::try_from(
                vec![
                    1, 113, 18, 32, 56, 148, 167, 251, 237, 117, 200, 226, 181, 134, 79, 115, 131,
                    220, 232, 143, 20, 67, 224, 179, 48, 130, 197, 123, 226, 85, 85, 56, 38, 84,
                    106, 225,
                ]
                .as_slice(),
            )
            .unwrap(),
        };
        let json = block.to_json();

        let wanted_json = serde_json::json!({
            "kind": 2,
            "slot": 1,
            "shredding": [
                {
                    "entry_end_idx": 1,
                    "shred_end_idx": 1
                }
            ],
            "entries": [
                {
                    "/":"bafyreibysst7x3lvzdrllbspoob5z2epcrb6bmzqqlcxxysvku4cmvdk4e"
                }
            ],
            "meta": {
                "parent_slot": 1,
                "blocktime": 1,
                "block_height": 1
            },
            "rewards":{
                "/":"bafyreibysst7x3lvzdrllbspoob5z2epcrb6bmzqqlcxxysvku4cmvdk4e"
            }
        });

        assert_eq!(json, wanted_json);
    }

    #[test]
    fn test_decoding() {
        {
            let raw = vec![
                134, 2, 9, 152, 67, 130, 0, 0, 130, 1, 1, 130, 2, 2, 130, 3, 3, 130, 4, 4, 130, 5,
                5, 130, 6, 6, 130, 7, 7, 130, 8, 8, 130, 9, 9, 130, 10, 10, 130, 11, 11, 130, 12,
                12, 130, 13, 13, 130, 14, 14, 130, 15, 15, 130, 16, 16, 130, 17, 17, 130, 18, 18,
                130, 19, 19, 130, 20, 20, 130, 21, 21, 130, 22, 22, 130, 23, 23, 130, 24, 24, 24,
                24, 130, 24, 25, 24, 25, 130, 24, 26, 24, 26, 130, 24, 27, 24, 27, 130, 24, 28, 24,
                28, 130, 24, 29, 24, 29, 130, 24, 30, 24, 30, 130, 24, 31, 24, 31, 130, 24, 32, 24,
                32, 130, 24, 33, 24, 33, 130, 24, 34, 24, 34, 130, 24, 35, 24, 35, 130, 24, 36, 24,
                36, 130, 24, 37, 24, 37, 130, 24, 38, 24, 38, 130, 24, 39, 24, 39, 130, 24, 40, 24,
                40, 130, 24, 41, 24, 41, 130, 24, 42, 24, 42, 130, 24, 43, 24, 43, 130, 24, 44, 24,
                44, 130, 24, 45, 24, 45, 130, 24, 46, 24, 46, 130, 24, 47, 24, 47, 130, 24, 48, 24,
                48, 130, 24, 49, 24, 49, 130, 24, 50, 24, 50, 130, 24, 51, 24, 51, 130, 24, 52, 24,
                52, 130, 24, 53, 24, 53, 130, 24, 54, 24, 54, 130, 24, 55, 24, 55, 130, 24, 56, 24,
                56, 130, 24, 57, 24, 57, 130, 24, 58, 24, 58, 130, 24, 59, 24, 59, 130, 24, 60, 24,
                60, 130, 24, 61, 24, 61, 130, 24, 62, 24, 62, 130, 24, 63, 24, 63, 130, 24, 64, 24,
                64, 130, 24, 65, 24, 65, 130, 24, 66, 24, 66, 152, 67, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 142, 112, 223, 218, 72, 167, 75, 214, 119, 159, 221, 221, 95, 85, 63, 18,
                215, 24, 68, 99, 59, 85, 68, 224, 9, 62, 238, 241, 7, 64, 192, 109, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 222, 44, 58, 205, 116, 63, 223, 136, 103, 18, 244, 73, 229,
                58, 156, 137, 74, 122, 206, 161, 158, 71, 170, 178, 214, 209, 227, 140, 42, 40,
                172, 110, 216, 42, 88, 37, 0, 1, 113, 18, 32, 191, 90, 248, 22, 27, 109, 170, 180,
                221, 154, 210, 250, 216, 65, 14, 204, 165, 180, 73, 97, 83, 8, 89, 55, 33, 52, 166,
                139, 162, 230, 13, 30, 216, 42, 88, 37, 0, 1, 113, 18, 32, 230, 237, 139, 160, 103,
                51, 163, 5, 237, 132, 47, 164, 232, 13, 60, 202, 168, 244, 154, 75, 226, 4, 201,
                106, 33, 126, 74, 69, 243, 62, 49, 2, 216, 42, 88, 37, 0, 1, 113, 18, 32, 38, 243,
                249, 67, 34, 234, 14, 98, 245, 145, 15, 160, 22, 73, 104, 213, 81, 184, 99, 161,
                131, 82, 170, 37, 123, 209, 243, 135, 145, 51, 65, 178, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 41, 157, 189, 75, 207, 215, 80, 168, 245, 87, 178, 171, 45, 31, 15, 8, 215,
                15, 120, 125, 176, 241, 30, 157, 154, 200, 197, 10, 8, 158, 129, 207, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 187, 213, 183, 163, 192, 193, 211, 142, 109, 46, 56, 41,
                181, 145, 13, 103, 212, 186, 30, 19, 149, 216, 120, 136, 208, 209, 125, 178, 84,
                252, 80, 190, 216, 42, 88, 37, 0, 1, 113, 18, 32, 126, 145, 212, 103, 128, 117, 35,
                120, 73, 130, 186, 30, 12, 87, 12, 112, 66, 232, 141, 83, 173, 119, 86, 37, 40, 44,
                136, 163, 217, 238, 73, 1, 216, 42, 88, 37, 0, 1, 113, 18, 32, 253, 123, 185, 68,
                243, 220, 77, 248, 192, 137, 161, 48, 150, 231, 241, 51, 15, 91, 75, 5, 214, 39,
                69, 1, 190, 31, 137, 66, 138, 16, 157, 9, 216, 42, 88, 37, 0, 1, 113, 18, 32, 222,
                42, 204, 102, 8, 90, 149, 178, 78, 3, 136, 116, 98, 29, 193, 195, 30, 106, 237, 83,
                201, 13, 125, 7, 150, 16, 151, 4, 215, 15, 129, 60, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 129, 19, 114, 187, 189, 37, 48, 79, 64, 88, 50, 65, 20, 1, 184, 132, 104, 23,
                45, 47, 199, 85, 12, 169, 85, 11, 61, 227, 166, 8, 172, 54, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 100, 89, 51, 50, 44, 192, 88, 117, 164, 141, 191, 64, 185, 174, 3,
                226, 60, 146, 104, 125, 107, 151, 42, 8, 46, 182, 38, 212, 7, 110, 169, 220, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 167, 186, 233, 29, 120, 195, 145, 42, 32, 164, 3,
                249, 242, 107, 252, 36, 113, 183, 12, 117, 11, 166, 97, 1, 110, 75, 82, 2, 44, 127,
                103, 186, 216, 42, 88, 37, 0, 1, 113, 18, 32, 57, 23, 172, 97, 230, 155, 0, 168,
                90, 59, 232, 55, 56, 63, 253, 46, 66, 132, 143, 135, 129, 177, 195, 203, 123, 102,
                71, 18, 201, 209, 87, 170, 216, 42, 88, 37, 0, 1, 113, 18, 32, 52, 49, 202, 86, 44,
                134, 175, 19, 161, 64, 87, 138, 213, 236, 236, 49, 69, 68, 205, 80, 222, 42, 48,
                229, 243, 135, 80, 21, 94, 119, 184, 239, 216, 42, 88, 37, 0, 1, 113, 18, 32, 5,
                247, 30, 254, 223, 250, 48, 234, 133, 205, 238, 162, 210, 123, 59, 125, 20, 213,
                83, 150, 181, 2, 67, 215, 252, 137, 64, 22, 110, 241, 71, 30, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 149, 253, 132, 66, 75, 81, 106, 175, 36, 84, 140, 115, 50, 168,
                202, 64, 209, 38, 168, 158, 152, 3, 187, 224, 60, 42, 183, 113, 143, 197, 62, 67,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 180, 228, 42, 116, 143, 156, 86, 53, 148, 144,
                14, 233, 128, 192, 54, 45, 43, 48, 20, 178, 23, 44, 88, 35, 148, 203, 88, 113, 38,
                94, 185, 75, 216, 42, 88, 37, 0, 1, 113, 18, 32, 150, 76, 138, 71, 216, 10, 225,
                103, 247, 147, 140, 71, 102, 50, 20, 95, 72, 215, 217, 72, 141, 241, 11, 255, 71,
                20, 180, 23, 230, 252, 157, 211, 216, 42, 88, 37, 0, 1, 113, 18, 32, 224, 97, 149,
                29, 111, 43, 27, 8, 128, 96, 200, 95, 219, 223, 70, 55, 120, 3, 42, 22, 94, 58,
                160, 69, 195, 185, 72, 205, 18, 152, 205, 176, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                57, 112, 68, 7, 123, 67, 97, 103, 227, 160, 214, 81, 206, 255, 123, 156, 187, 177,
                33, 101, 191, 134, 63, 200, 41, 40, 209, 118, 204, 55, 144, 67, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 238, 69, 207, 7, 233, 117, 207, 4, 225, 248, 215, 162, 254, 45, 8,
                131, 19, 20, 152, 155, 159, 62, 142, 208, 164, 246, 221, 25, 28, 97, 97, 209, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 111, 64, 66, 31, 214, 131, 232, 53, 66, 112, 1, 115,
                58, 201, 41, 108, 213, 252, 253, 239, 128, 67, 238, 14, 21, 115, 42, 200, 102, 110,
                7, 35, 216, 42, 88, 37, 0, 1, 113, 18, 32, 188, 174, 54, 151, 21, 158, 226, 199,
                27, 109, 3, 32, 232, 13, 88, 131, 36, 243, 91, 95, 220, 123, 186, 122, 64, 111,
                142, 235, 139, 228, 4, 78, 216, 42, 88, 37, 0, 1, 113, 18, 32, 187, 152, 69, 125,
                29, 74, 232, 247, 97, 175, 164, 181, 253, 66, 208, 216, 90, 40, 61, 201, 135, 9,
                27, 165, 69, 142, 218, 226, 51, 123, 158, 97, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                234, 98, 199, 95, 219, 46, 130, 100, 75, 180, 228, 145, 130, 237, 78, 191, 209, 9,
                3, 193, 124, 78, 119, 240, 0, 189, 143, 92, 223, 119, 236, 0, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 200, 117, 71, 254, 24, 62, 146, 234, 45, 109, 34, 155, 39, 215,
                139, 234, 78, 183, 137, 178, 32, 125, 50, 161, 170, 119, 217, 201, 220, 195, 210,
                96, 216, 42, 88, 37, 0, 1, 113, 18, 32, 73, 235, 53, 110, 131, 8, 102, 2, 127, 230,
                137, 52, 137, 85, 169, 8, 188, 185, 193, 137, 161, 183, 134, 118, 198, 232, 213,
                69, 100, 72, 110, 241, 216, 42, 88, 37, 0, 1, 113, 18, 32, 70, 203, 245, 218, 177,
                27, 128, 241, 138, 84, 175, 184, 35, 18, 4, 147, 84, 62, 193, 31, 149, 255, 11,
                181, 192, 110, 130, 233, 153, 145, 48, 223, 216, 42, 88, 37, 0, 1, 113, 18, 32, 58,
                235, 107, 195, 10, 103, 106, 255, 156, 196, 8, 220, 219, 141, 155, 170, 160, 68,
                19, 167, 19, 225, 219, 59, 21, 156, 203, 0, 210, 44, 201, 128, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 42, 254, 9, 4, 50, 84, 189, 205, 235, 223, 174, 117, 75, 254, 189,
                223, 229, 193, 181, 202, 84, 115, 131, 98, 92, 185, 21, 12, 128, 91, 247, 43, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 174, 47, 130, 64, 103, 169, 126, 186, 24, 110, 243,
                43, 169, 150, 87, 109, 74, 56, 163, 209, 14, 89, 244, 174, 102, 164, 93, 80, 11,
                162, 127, 13, 216, 42, 88, 37, 0, 1, 113, 18, 32, 4, 110, 4, 201, 102, 75, 173,
                233, 189, 245, 49, 27, 41, 97, 21, 122, 95, 123, 193, 190, 249, 52, 177, 182, 206,
                73, 202, 35, 79, 208, 131, 204, 216, 42, 88, 37, 0, 1, 113, 18, 32, 251, 150, 119,
                179, 54, 215, 18, 141, 45, 55, 253, 46, 40, 60, 6, 168, 156, 40, 81, 97, 224, 23,
                107, 17, 176, 113, 217, 198, 12, 110, 218, 229, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                27, 56, 24, 31, 64, 167, 101, 145, 43, 99, 198, 176, 105, 170, 148, 179, 13, 116,
                112, 161, 129, 125, 128, 200, 192, 52, 191, 13, 126, 100, 112, 148, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 17, 10, 10, 215, 134, 196, 216, 102, 56, 205, 69, 82, 130,
                163, 251, 128, 169, 173, 3, 211, 33, 3, 238, 181, 122, 238, 120, 113, 97, 70, 199,
                99, 216, 42, 88, 37, 0, 1, 113, 18, 32, 237, 123, 204, 246, 34, 231, 16, 91, 211,
                15, 208, 97, 180, 11, 52, 189, 245, 41, 240, 50, 2, 12, 125, 120, 53, 186, 233, 91,
                54, 17, 38, 149, 216, 42, 88, 37, 0, 1, 113, 18, 32, 71, 142, 165, 172, 99, 10, 26,
                145, 252, 179, 149, 202, 37, 0, 172, 86, 124, 111, 231, 84, 117, 227, 246, 7, 25,
                161, 94, 209, 145, 59, 56, 92, 216, 42, 88, 37, 0, 1, 113, 18, 32, 166, 215, 77,
                118, 191, 128, 29, 103, 204, 171, 89, 172, 154, 190, 11, 61, 236, 65, 174, 175,
                154, 183, 98, 248, 24, 105, 28, 14, 102, 52, 154, 48, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 61, 113, 170, 240, 141, 6, 123, 196, 179, 199, 85, 202, 244, 164, 240, 73,
                214, 239, 120, 220, 190, 170, 246, 237, 21, 181, 165, 243, 52, 145, 50, 31, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 210, 6, 19, 252, 30, 228, 240, 216, 151, 30, 238,
                110, 232, 138, 226, 161, 13, 76, 16, 112, 92, 225, 77, 112, 147, 219, 80, 157, 179,
                166, 32, 46, 216, 42, 88, 37, 0, 1, 113, 18, 32, 52, 187, 78, 14, 51, 155, 212,
                239, 28, 10, 109, 255, 248, 151, 42, 194, 126, 23, 219, 193, 74, 83, 187, 173, 79,
                65, 77, 246, 166, 8, 115, 174, 216, 42, 88, 37, 0, 1, 113, 18, 32, 178, 156, 206,
                197, 235, 161, 91, 255, 135, 191, 3, 55, 182, 181, 195, 35, 35, 148, 173, 90, 2,
                214, 25, 199, 116, 114, 166, 70, 195, 23, 12, 166, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 123, 139, 182, 186, 35, 12, 35, 2, 128, 170, 49, 41, 206, 192, 65, 40, 155, 16,
                26, 230, 215, 43, 47, 172, 235, 74, 198, 248, 20, 163, 132, 60, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 167, 176, 224, 66, 172, 151, 119, 165, 11, 96, 114, 201, 45, 206,
                176, 18, 164, 125, 108, 166, 25, 157, 91, 143, 194, 196, 163, 148, 132, 17, 132,
                89, 216, 42, 88, 37, 0, 1, 113, 18, 32, 203, 127, 128, 210, 101, 109, 57, 82, 7,
                114, 7, 91, 37, 254, 29, 197, 147, 246, 10, 240, 200, 31, 105, 2, 66, 145, 216,
                143, 225, 126, 138, 141, 216, 42, 88, 37, 0, 1, 113, 18, 32, 18, 13, 7, 205, 8,
                128, 9, 231, 61, 187, 118, 223, 154, 75, 189, 205, 156, 44, 148, 254, 102, 227,
                205, 184, 159, 122, 29, 142, 12, 223, 8, 206, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                5, 170, 131, 140, 66, 52, 31, 153, 17, 139, 250, 148, 178, 145, 79, 213, 249, 219,
                120, 154, 240, 81, 20, 156, 206, 95, 110, 42, 140, 180, 237, 125, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 214, 157, 222, 158, 17, 235, 127, 230, 232, 60, 224, 107, 250,
                118, 138, 76, 173, 78, 59, 53, 27, 60, 201, 221, 24, 15, 9, 176, 213, 87, 170, 254,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 156, 137, 136, 61, 164, 161, 151, 133, 2, 65,
                181, 28, 250, 233, 85, 71, 103, 225, 136, 245, 202, 81, 205, 42, 185, 146, 223, 13,
                207, 36, 153, 89, 216, 42, 88, 37, 0, 1, 113, 18, 32, 2, 120, 83, 19, 218, 40, 184,
                108, 244, 186, 234, 225, 21, 15, 1, 24, 179, 238, 198, 133, 187, 25, 234, 213, 202,
                36, 233, 23, 42, 158, 95, 244, 216, 42, 88, 37, 0, 1, 113, 18, 32, 250, 18, 165,
                159, 37, 23, 14, 27, 105, 174, 76, 62, 29, 146, 188, 87, 190, 61, 139, 141, 173,
                255, 233, 23, 184, 116, 122, 174, 225, 162, 188, 220, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 76, 174, 108, 234, 137, 243, 117, 254, 234, 82, 251, 101, 44, 233, 88, 31,
                249, 104, 13, 213, 165, 231, 87, 24, 192, 176, 139, 247, 1, 130, 204, 253, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 111, 99, 204, 160, 107, 77, 144, 34, 129, 148, 225, 75,
                1, 233, 33, 158, 127, 16, 53, 149, 207, 113, 153, 157, 174, 180, 160, 18, 37, 22,
                229, 12, 216, 42, 88, 37, 0, 1, 113, 18, 32, 115, 178, 238, 147, 6, 170, 64, 14,
                91, 37, 140, 110, 211, 187, 157, 189, 162, 191, 198, 198, 193, 124, 71, 154, 36,
                122, 36, 244, 189, 120, 93, 148, 216, 42, 88, 37, 0, 1, 113, 18, 32, 33, 131, 47,
                150, 208, 179, 218, 31, 40, 77, 141, 70, 134, 83, 59, 6, 111, 36, 43, 252, 97, 218,
                77, 149, 27, 39, 122, 91, 190, 148, 60, 18, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                227, 83, 22, 175, 13, 39, 47, 83, 7, 248, 45, 60, 165, 133, 7, 169, 35, 110, 131,
                239, 133, 213, 94, 195, 203, 161, 173, 160, 162, 18, 74, 134, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 120, 254, 54, 81, 56, 46, 139, 240, 186, 184, 114, 38, 36, 38, 23,
                172, 69, 223, 238, 175, 59, 162, 225, 242, 64, 198, 175, 131, 132, 220, 156, 118,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 23, 138, 118, 75, 69, 204, 52, 177, 68, 125,
                170, 58, 246, 63, 72, 184, 200, 117, 177, 51, 40, 163, 36, 214, 71, 102, 64, 172,
                162, 117, 23, 55, 216, 42, 88, 37, 0, 1, 113, 18, 32, 130, 168, 161, 182, 131, 200,
                99, 15, 197, 125, 233, 217, 200, 203, 246, 39, 201, 115, 173, 229, 128, 127, 224,
                143, 73, 49, 239, 145, 71, 54, 24, 9, 216, 42, 88, 37, 0, 1, 113, 18, 32, 108, 202,
                24, 19, 2, 115, 66, 161, 228, 224, 141, 239, 184, 170, 55, 175, 141, 184, 93, 172,
                194, 223, 242, 11, 186, 53, 156, 89, 130, 125, 127, 34, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 176, 122, 175, 15, 112, 202, 24, 10, 220, 236, 188, 59, 185, 1, 155, 174,
                168, 121, 81, 183, 106, 236, 252, 30, 124, 194, 4, 24, 133, 115, 9, 239, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 44, 242, 54, 30, 20, 235, 97, 110, 45, 103, 237, 31,
                111, 220, 51, 2, 7, 29, 251, 12, 231, 137, 44, 3, 53, 167, 190, 212, 248, 21, 32,
                20, 216, 42, 88, 37, 0, 1, 113, 18, 32, 155, 19, 144, 21, 40, 30, 161, 117, 209,
                52, 207, 22, 157, 89, 140, 151, 26, 0, 6, 173, 2, 53, 219, 153, 146, 42, 45, 222,
                75, 0, 254, 116, 216, 42, 88, 37, 0, 1, 113, 18, 32, 167, 159, 144, 28, 221, 200,
                5, 102, 79, 55, 233, 5, 176, 167, 205, 152, 70, 50, 32, 82, 232, 224, 36, 80, 234,
                203, 205, 213, 169, 108, 113, 167, 216, 42, 88, 37, 0, 1, 113, 18, 32, 232, 44,
                255, 23, 170, 107, 235, 86, 160, 91, 215, 88, 189, 111, 57, 241, 149, 58, 74, 104,
                143, 65, 92, 40, 247, 22, 214, 98, 40, 172, 233, 96, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 56, 133, 167, 54, 128, 82, 53, 103, 203, 183, 120, 225, 62, 216, 196, 16,
                104, 6, 236, 116, 93, 133, 170, 228, 44, 66, 64, 120, 152, 240, 189, 74, 131, 8, 0,
                246, 216, 42, 69, 0, 1, 85, 0, 0,
            ];
            let as_json_raw = serde_json::json!({"kind":2,"slot":9,"shredding":[{"entry_end_idx":0,"shred_end_idx":0},{"entry_end_idx":1,"shred_end_idx":1},{"entry_end_idx":2,"shred_end_idx":2},{"entry_end_idx":3,"shred_end_idx":3},{"entry_end_idx":4,"shred_end_idx":4},{"entry_end_idx":5,"shred_end_idx":5},{"entry_end_idx":6,"shred_end_idx":6},{"entry_end_idx":7,"shred_end_idx":7},{"entry_end_idx":8,"shred_end_idx":8},{"entry_end_idx":9,"shred_end_idx":9},{"entry_end_idx":10,"shred_end_idx":10},{"entry_end_idx":11,"shred_end_idx":11},{"entry_end_idx":12,"shred_end_idx":12},{"entry_end_idx":13,"shred_end_idx":13},{"entry_end_idx":14,"shred_end_idx":14},{"entry_end_idx":15,"shred_end_idx":15},{"entry_end_idx":16,"shred_end_idx":16},{"entry_end_idx":17,"shred_end_idx":17},{"entry_end_idx":18,"shred_end_idx":18},{"entry_end_idx":19,"shred_end_idx":19},{"entry_end_idx":20,"shred_end_idx":20},{"entry_end_idx":21,"shred_end_idx":21},{"entry_end_idx":22,"shred_end_idx":22},{"entry_end_idx":23,"shred_end_idx":23},{"entry_end_idx":24,"shred_end_idx":24},{"entry_end_idx":25,"shred_end_idx":25},{"entry_end_idx":26,"shred_end_idx":26},{"entry_end_idx":27,"shred_end_idx":27},{"entry_end_idx":28,"shred_end_idx":28},{"entry_end_idx":29,"shred_end_idx":29},{"entry_end_idx":30,"shred_end_idx":30},{"entry_end_idx":31,"shred_end_idx":31},{"entry_end_idx":32,"shred_end_idx":32},{"entry_end_idx":33,"shred_end_idx":33},{"entry_end_idx":34,"shred_end_idx":34},{"entry_end_idx":35,"shred_end_idx":35},{"entry_end_idx":36,"shred_end_idx":36},{"entry_end_idx":37,"shred_end_idx":37},{"entry_end_idx":38,"shred_end_idx":38},{"entry_end_idx":39,"shred_end_idx":39},{"entry_end_idx":40,"shred_end_idx":40},{"entry_end_idx":41,"shred_end_idx":41},{"entry_end_idx":42,"shred_end_idx":42},{"entry_end_idx":43,"shred_end_idx":43},{"entry_end_idx":44,"shred_end_idx":44},{"entry_end_idx":45,"shred_end_idx":45},{"entry_end_idx":46,"shred_end_idx":46},{"entry_end_idx":47,"shred_end_idx":47},{"entry_end_idx":48,"shred_end_idx":48},{"entry_end_idx":49,"shred_end_idx":49},{"entry_end_idx":50,"shred_end_idx":50},{"entry_end_idx":51,"shred_end_idx":51},{"entry_end_idx":52,"shred_end_idx":52},{"entry_end_idx":53,"shred_end_idx":53},{"entry_end_idx":54,"shred_end_idx":54},{"entry_end_idx":55,"shred_end_idx":55},{"entry_end_idx":56,"shred_end_idx":56},{"entry_end_idx":57,"shred_end_idx":57},{"entry_end_idx":58,"shred_end_idx":58},{"entry_end_idx":59,"shred_end_idx":59},{"entry_end_idx":60,"shred_end_idx":60},{"entry_end_idx":61,"shred_end_idx":61},{"entry_end_idx":62,"shred_end_idx":62},{"entry_end_idx":63,"shred_end_idx":63},{"entry_end_idx":64,"shred_end_idx":64},{"entry_end_idx":65,"shred_end_idx":65},{"entry_end_idx":66,"shred_end_idx":66}],"entries":[{"/":"bafyreieoodp5usfhjplhph653vpvkpys24meiyz3kvcoacj653yqoqganu"},{"/":"bafyreig6fq5m25b736egoexujhstvhejjj5m5im6i6vlfvwr4ogcukfmny"},{"/":"bafyreif7ll4bmg3nvk2n3gws7lmecdwmuw2esyktbbmtoijuu2f2fzqndy"},{"/":"bafyreihg5wf2azztumc63bbputua2pgkvd2jus7catewuil6jjc7gprrai"},{"/":"bafyreibg6p4ugixkbzrpleipuales2gvkg4ghimdkkvck66r6odzcm2bwi"},{"/":"bafyreibjtw6uxt6xkcupkv5svmwr6dyi24hxq7nq6epj3gwiyufarhubz4"},{"/":"bafyreif32w32hqgb2ohg2lryfg2zcdlh2s5b4e4v3b4irugrpwzfj7cqxy"},{"/":"bafyreid6shkgpadven4etav2dygfoddqilui2u5no5lckkbmrcr5t3sjae"},{"/":"bafyreih5po4uj464jx4mbcnbgclop4jtb5nuwbowe5cqdpq7rfbiuee5be"},{"/":"bafyreig6flggmcc2swze4a4iorrb3qoddzvo2u6jbv6qpfqqs4cnod4bhq"},{"/":"bafyreiebcnzlxpjfgbhuawbsiekadoeenals2l6hkugksvilhxr2mcfmgy"},{"/":"bafyreideleztelgalb22jdn7ic424a7chsjgq7lls4vaqlvwe3kao3vj3q"},{"/":"bafyreifhxlur26gdsevcbjad7hzgx7beog3qy5iluzqqc3slkibcy73hxi"},{"/":"bafyreibzc6wgdzu3acufuo7ig44d77joikci7b4bwhb4w63gi4jmtukxvi"},{"/":"bafyreibughffmlegv4j2cqcxrlk6z3brivcm2ug6fiyol44hkakv455y54"},{"/":"bafyreiaf64pp5x72gdviltpouljhwo35ctkvhfvvajb5p7ejialg54khdy"},{"/":"bafyreiev7wcees2rnkxsivemomzkrssa2etkrhuyao56apbkw5yy7rj6im"},{"/":"bafyreifu4qvhjd44ky2zjeao5gamanrnfmybjmqxfrmchfgllbysmxvzjm"},{"/":"bafyreiewjsfepwak4ft7pe4mi5tdefc7jdl5ssen6ef76ryuwql6n7e52m"},{"/":"bafyreihamgkr23zldmeiaygil7n56rrxpabsufs6hkqelq5zjdgrfggnwa"},{"/":"bafyreibzobcao62dmft6higwkhhp6644xoysczn7qy74qkji2f3myn4qim"},{"/":"bafyreihoixhqp2lvz4cod6gxul7c2cedcmkjrg47h2hnbjhw3umryylb2e"},{"/":"bafyreidpibbb7vud5a2ue4abom5msklm2x6p334aipxa4fltflegm3qhem"},{"/":"bafyreif4vy3jofm64ldrw3idedua2wedetzvwx64po5huqdpr3vyxzaejy"},{"/":"bafyreif3tbcx2hkk5d3wdl5ewx6ufugyliud3smhben2krmo3lrdg646me"},{"/":"bafyreihkmldv7wzoqjsexnhesgbo2tv72eeqhql4jz37aaf5r5on657maa"},{"/":"bafyreigiovd74gb6slvc23jctmt5pc7kj23ytmrapuzkdktx3he5zq6sma"},{"/":"bafyreicj5m2w5ayimybh7zujgsevlkiixs44dcnbw6dhnrxi2vcwisdo6e"},{"/":"bafyreicgzp25vmi3qdyyuvfpxarrebetkq7mch4v74f3lqdoqluztejq34"},{"/":"bafyreib25nv4gcthnl7zzrai3tny3g5kubcbhjyt4hntwfm4zmanelgjqa"},{"/":"bafyreibk7yeqimsuxxg6xx5oovf75po74xa3lssuoobwexfzcugiaw7xfm"},{"/":"bafyreifof6beaz5jp25bq3xtfouzmv3nji4khuiolh2k4zvelviaxit7bu"},{"/":"bafyreiaenycmszslvxu335jrdmuwcfl2l554dpxzgsy3ntsjziru7uedzq"},{"/":"bafyreih3sz33gnwxckgs2n75fyudybvitqufcypac5vrdmdr3hday3w24u"},{"/":"bafyreia3hamb6qfhmwiswy6gwbu2vfftbv2hbimbpwamrqbux4gx4zdqsq"},{"/":"bafyreiarbifnpbwe3btdrtkfkkbkh64avgwqhuzbapxlk6xopbywcrwhmm"},{"/":"bafyreihnppgpmixhcbn5gd6qmg2awnf56uu7amqcbr6xqnn25fntmejgsu"},{"/":"bafyreichr2s2yyykdki7zm4vzisqblcwprx6ovdv4p3aognbl3izcozylq"},{"/":"bafyreifg25gxnp4advt4zk2zvsnl4cz55ra25l42w5rpqgdjdqhgmne2ga"},{"/":"bafyreib5ogvpbdigppclhr2vzl2kj4cj23xxrxf6vl3o2fnvuxztjejsd4"},{"/":"bafyreigsayj7yhxe6dmjohxon3uivyvbbvgba4c44fgxbe63kco3hjrafy"},{"/":"bafyreibuxnha4m432txryctn774jokwcpyl5xqkkko522t2bjx3kmcdtvy"},{"/":"bafyreifstthml25blp7yppydg63llqzdeokk2wqc2ym4o5dsuzdmgfymuy"},{"/":"bafyreid3ro3luiymembibkrrfhhmaqjitmibvzwxfmx2z22ky34bji4ehq"},{"/":"bafyreifhwdqeflexo6sqwydszew45masur6wzjqztvny7qweuokiiemele"},{"/":"bafyreiglp6anezlnhfjao4qhlms74hofsp3av4gid5uqequr3ch6c7ukru"},{"/":"bafyreiasbud42ceabhtt3o3w36nexpontqwjj7tg4pg3rh32dwhazxyizy"},{"/":"bafyreiafvkbyyqrud6mrdc72sszjct6v7hnxrgxqkekjzts7nyviznhnpu"},{"/":"bafyreigwtxpj4eplp7toqphanp5hncsmvvhdwni3hte52gapbgynkv5k7y"},{"/":"bafyreie4rged3jfbs6cqeqnvdt5osvkhm7qyr5okkhgsvoms34g46jezle"},{"/":"bafyreiacpbjrhwrixbwpjoxk4ekq6aiywpxmnbn3dhvnlsre5elsvhs76q"},{"/":"bafyreih2cksz6jixbynwtlsmhyozfpcxxy6yxdnn77urpodupkxodiv43q"},{"/":"bafyreicmvzwovcptox7ouux3muwoswa77fua3vnf45lrrqfqrp3qdawm7u"},{"/":"bafyreidpmpgka22nsaridfhbjma6sim6p4idlfopogmz3lvuuajckfxfbq"},{"/":"bafyreidtwlxjgbvkiahfwjmmn3j3xhn5uk74nrwbprdzujd2et2l26c5sq"},{"/":"bafyreibbqmxznuft3ipsqtmni2dfgoygn4scx7db3jgzkgzhpjn35fb4ci"},{"/":"bafyreihdkmlk6djhf5jqp6bnhssykb5jenxih34f2vpmhs5bvwqkeeskqy"},{"/":"bafyreidy7y3fcoborpylvodseyscmf5mixp65lz3ulq7eqggv6byjxe4oy"},{"/":"bafyreiaxrj3ewromgsyui7nkhl3d6sfyzb23cmziumsnmr3gicwke5ixg4"},{"/":"bafyreiecvcq3na6immh4k7pj3hemx5rhzfz23zmap7qi6sjr56iuonqybe"},{"/":"bafyreidmzimbgattikq6jyen564kun5prw4f3lgc37zaxorvtrmye7l7ei"},{"/":"bafyreifqpkxq64gkdafnz3f4ho4qdg5ovb4vdn3k5t6b47gcaqmik4yj54"},{"/":"bafyreibm6i3b4fhlmfxc2z7nd5x5ymyca4o7wdhhrewagnnhx3kpqfjacq"},{"/":"bafyreie3coibkka6uf25cngpc2ovtdexdiaanlicgxnzterkfxpewah6oq"},{"/":"bafyreifht6ibzxoiavte6n7jawykptmyiyzcauxi4asfb2wlzxk2s3dru4"},{"/":"bafyreihift7rpktl5nlkaw6xlc6w6oprsu5eu2epifocr5yw2zrcrlhjma"},{"/":"bafyreibyqwttnacsgvt4xn3y4e7nrraqnadoy5c5qwvoilccib4jr4f5ji"}],"meta":{"parent_slot":8,"blocktime":0,"block_height":null},"rewards":{"/":"bafkqaaa"}});

            let block = Block::from_bytes(raw).unwrap();
            let as_json = block.to_json();
            assert_eq!(as_json, as_json_raw);
        }
        {
            let raw = vec![
                134, 2, 26, 1, 1, 20, 146, 152, 85, 130, 0, 0, 130, 1, 1, 130, 2, 2, 130, 3, 3,
                130, 4, 4, 130, 5, 5, 130, 6, 6, 130, 7, 7, 130, 8, 8, 130, 9, 9, 130, 10, 10, 130,
                11, 11, 130, 12, 12, 130, 13, 13, 130, 14, 14, 130, 15, 32, 130, 16, 32, 130, 17,
                32, 130, 18, 32, 130, 19, 15, 130, 20, 16, 130, 21, 17, 130, 22, 18, 130, 23, 19,
                130, 24, 24, 20, 130, 24, 25, 21, 130, 24, 26, 22, 130, 24, 27, 23, 130, 24, 28,
                24, 24, 130, 24, 29, 24, 25, 130, 24, 30, 24, 26, 130, 24, 31, 24, 27, 130, 24, 32,
                24, 28, 130, 24, 33, 24, 29, 130, 24, 34, 24, 30, 130, 24, 35, 32, 130, 24, 36, 24,
                31, 130, 24, 37, 32, 130, 24, 38, 32, 130, 24, 39, 32, 130, 24, 40, 32, 130, 24,
                41, 32, 130, 24, 42, 32, 130, 24, 43, 32, 130, 24, 44, 24, 34, 130, 24, 45, 32,
                130, 24, 46, 32, 130, 24, 47, 32, 130, 24, 48, 32, 130, 24, 49, 32, 130, 24, 50,
                32, 130, 24, 51, 32, 130, 24, 52, 24, 37, 130, 24, 53, 32, 130, 24, 54, 32, 130,
                24, 55, 32, 130, 24, 56, 24, 39, 130, 24, 57, 24, 40, 130, 24, 58, 24, 41, 130, 24,
                59, 24, 42, 130, 24, 60, 24, 43, 130, 24, 61, 24, 44, 130, 24, 62, 24, 45, 130, 24,
                63, 32, 130, 24, 64, 24, 46, 130, 24, 65, 24, 47, 130, 24, 66, 24, 48, 130, 24, 67,
                24, 49, 130, 24, 68, 24, 50, 130, 24, 69, 24, 51, 130, 24, 70, 24, 52, 130, 24, 71,
                24, 53, 130, 24, 72, 24, 54, 130, 24, 73, 24, 55, 130, 24, 74, 24, 56, 130, 24, 75,
                24, 57, 130, 24, 76, 24, 58, 130, 24, 77, 24, 59, 130, 24, 78, 24, 60, 130, 24, 79,
                24, 61, 130, 24, 80, 24, 62, 130, 24, 81, 24, 63, 130, 24, 82, 24, 64, 130, 24, 83,
                24, 65, 130, 24, 84, 24, 66, 152, 85, 216, 42, 88, 37, 0, 1, 113, 18, 32, 122, 123,
                74, 114, 57, 157, 37, 129, 246, 3, 37, 54, 199, 66, 64, 223, 46, 192, 12, 76, 145,
                226, 95, 242, 254, 34, 224, 171, 156, 18, 2, 233, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 223, 199, 135, 101, 178, 25, 226, 104, 168, 209, 103, 229, 141, 67, 216, 208,
                255, 99, 47, 109, 77, 223, 252, 100, 185, 186, 239, 80, 220, 95, 146, 75, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 150, 251, 13, 166, 191, 42, 166, 60, 199, 181, 89, 0,
                31, 220, 176, 217, 123, 31, 161, 57, 71, 104, 88, 119, 27, 78, 98, 219, 195, 220,
                53, 130, 216, 42, 88, 37, 0, 1, 113, 18, 32, 92, 120, 74, 237, 100, 136, 205, 207,
                193, 211, 53, 4, 94, 254, 150, 5, 167, 142, 164, 60, 100, 134, 83, 67, 11, 111, 76,
                36, 23, 248, 224, 166, 216, 42, 88, 37, 0, 1, 113, 18, 32, 15, 61, 88, 170, 3, 206,
                63, 187, 57, 101, 144, 170, 35, 28, 230, 0, 1, 6, 220, 192, 211, 79, 231, 173, 116,
                252, 4, 57, 164, 233, 117, 220, 216, 42, 88, 37, 0, 1, 113, 18, 32, 72, 131, 60,
                44, 209, 228, 113, 149, 133, 145, 72, 230, 161, 143, 61, 25, 121, 217, 248, 12, 67,
                8, 122, 207, 186, 67, 222, 27, 171, 211, 158, 115, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 6, 70, 152, 201, 230, 211, 113, 169, 240, 40, 163, 190, 198, 224, 137, 76, 101,
                7, 27, 90, 99, 168, 57, 95, 205, 255, 30, 141, 96, 203, 44, 156, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 51, 37, 164, 233, 163, 68, 185, 117, 61, 39, 122, 155, 226, 190,
                131, 14, 92, 72, 145, 109, 6, 241, 4, 30, 161, 88, 147, 54, 92, 106, 43, 93, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 59, 98, 242, 71, 81, 238, 140, 194, 240, 173, 66, 8,
                150, 243, 80, 228, 191, 254, 253, 75, 253, 226, 220, 107, 250, 108, 210, 1, 241,
                164, 203, 161, 216, 42, 88, 37, 0, 1, 113, 18, 32, 13, 111, 41, 100, 249, 98, 104,
                237, 62, 131, 155, 179, 218, 203, 178, 89, 242, 19, 88, 14, 48, 191, 126, 52, 173,
                36, 238, 7, 125, 148, 57, 234, 216, 42, 88, 37, 0, 1, 113, 18, 32, 98, 206, 136,
                134, 130, 183, 33, 62, 237, 241, 10, 218, 139, 239, 6, 100, 95, 104, 100, 161, 138,
                164, 192, 13, 70, 89, 72, 162, 237, 14, 19, 218, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 213, 182, 141, 33, 245, 141, 39, 215, 187, 36, 50, 17, 26, 85, 30, 146, 193,
                83, 181, 54, 73, 114, 254, 251, 22, 133, 63, 84, 202, 212, 84, 208, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 26, 128, 103, 145, 201, 39, 91, 85, 224, 54, 243, 145, 206,
                244, 78, 104, 143, 174, 235, 221, 186, 211, 43, 242, 238, 61, 105, 239, 216, 247,
                193, 142, 216, 42, 88, 37, 0, 1, 113, 18, 32, 82, 15, 83, 226, 178, 231, 60, 112,
                115, 72, 229, 118, 243, 181, 233, 130, 50, 60, 80, 248, 128, 121, 254, 6, 150, 163,
                56, 21, 12, 89, 67, 179, 216, 42, 88, 37, 0, 1, 113, 18, 32, 120, 82, 20, 126, 60,
                21, 10, 110, 187, 194, 85, 233, 104, 241, 186, 33, 251, 99, 10, 242, 228, 38, 14,
                112, 36, 89, 251, 89, 176, 254, 60, 95, 216, 42, 88, 37, 0, 1, 113, 18, 32, 50,
                133, 226, 75, 251, 29, 236, 250, 208, 216, 38, 149, 158, 173, 79, 119, 65, 157,
                249, 182, 92, 77, 109, 211, 4, 175, 20, 22, 37, 108, 252, 86, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 58, 34, 228, 117, 98, 189, 226, 210, 222, 247, 186, 146, 222, 166,
                108, 85, 236, 122, 216, 212, 195, 103, 84, 155, 125, 35, 1, 217, 172, 241, 221,
                225, 216, 42, 88, 37, 0, 1, 113, 18, 32, 101, 207, 104, 6, 46, 78, 208, 118, 40,
                250, 44, 80, 107, 56, 213, 174, 169, 36, 206, 149, 0, 253, 32, 215, 156, 233, 48,
                7, 74, 217, 156, 105, 216, 42, 88, 37, 0, 1, 113, 18, 32, 0, 158, 219, 39, 72, 69,
                8, 234, 88, 170, 13, 178, 17, 161, 122, 70, 88, 177, 233, 21, 142, 117, 207, 75,
                32, 233, 84, 115, 156, 137, 110, 41, 216, 42, 88, 37, 0, 1, 113, 18, 32, 89, 55,
                39, 33, 78, 157, 63, 167, 145, 68, 44, 165, 50, 65, 160, 54, 188, 90, 118, 182,
                180, 212, 253, 152, 34, 187, 208, 12, 62, 160, 236, 165, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 29, 170, 117, 212, 168, 89, 62, 142, 241, 19, 148, 165, 109, 86, 96,
                180, 170, 72, 35, 69, 143, 171, 171, 219, 193, 21, 193, 92, 139, 95, 139, 43, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 227, 153, 37, 185, 38, 148, 174, 246, 39, 8, 211,
                49, 25, 143, 183, 221, 207, 169, 8, 76, 52, 212, 232, 156, 188, 69, 132, 237, 47,
                65, 100, 253, 216, 42, 88, 37, 0, 1, 113, 18, 32, 144, 23, 245, 3, 236, 85, 113,
                143, 85, 186, 11, 50, 37, 130, 45, 179, 136, 248, 233, 123, 146, 82, 127, 32, 92,
                36, 239, 95, 242, 220, 43, 195, 216, 42, 88, 37, 0, 1, 113, 18, 32, 96, 106, 105,
                36, 234, 1, 252, 92, 96, 141, 173, 247, 62, 186, 195, 63, 0, 150, 149, 99, 47, 72,
                63, 227, 212, 83, 105, 47, 250, 149, 88, 174, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                171, 172, 93, 167, 224, 207, 180, 39, 215, 54, 166, 250, 120, 16, 244, 132, 28, 9,
                59, 7, 216, 64, 94, 155, 107, 58, 111, 168, 249, 252, 55, 230, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 31, 142, 97, 73, 145, 219, 203, 177, 203, 179, 3, 54, 78, 99, 59,
                151, 71, 173, 65, 10, 80, 121, 56, 32, 249, 38, 156, 121, 174, 162, 109, 116, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 51, 247, 187, 90, 97, 11, 71, 69, 246, 160, 27, 26,
                236, 154, 62, 83, 139, 28, 123, 194, 233, 168, 140, 183, 103, 244, 160, 41, 203,
                182, 105, 144, 216, 42, 88, 37, 0, 1, 113, 18, 32, 167, 106, 72, 148, 160, 245, 11,
                82, 117, 72, 248, 89, 61, 25, 85, 1, 228, 63, 41, 121, 40, 150, 254, 236, 160, 136,
                172, 220, 188, 91, 208, 119, 216, 42, 88, 37, 0, 1, 113, 18, 32, 134, 18, 85, 138,
                177, 67, 114, 167, 246, 37, 254, 162, 137, 47, 178, 61, 14, 51, 185, 92, 34, 199,
                85, 85, 113, 89, 135, 108, 100, 157, 148, 181, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                201, 163, 8, 169, 94, 149, 229, 55, 41, 178, 187, 162, 37, 172, 164, 70, 159, 199,
                105, 46, 102, 39, 107, 39, 140, 125, 212, 145, 52, 249, 123, 233, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 173, 237, 245, 0, 199, 33, 18, 164, 184, 142, 121, 138, 122, 57,
                114, 186, 217, 65, 243, 7, 132, 60, 62, 147, 152, 176, 18, 160, 170, 215, 5, 15,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 149, 70, 110, 103, 201, 239, 40, 100, 16, 48,
                79, 114, 10, 42, 28, 41, 53, 219, 181, 116, 57, 24, 39, 82, 188, 78, 176, 222, 70,
                174, 215, 47, 216, 42, 88, 37, 0, 1, 113, 18, 32, 127, 99, 35, 88, 213, 72, 7, 245,
                49, 240, 184, 88, 215, 202, 83, 105, 31, 249, 86, 130, 177, 0, 135, 45, 188, 69,
                172, 226, 63, 223, 66, 247, 216, 42, 88, 37, 0, 1, 113, 18, 32, 38, 88, 200, 51,
                45, 26, 150, 229, 51, 24, 80, 113, 10, 117, 204, 131, 117, 29, 83, 152, 236, 42,
                81, 182, 45, 127, 160, 255, 4, 157, 24, 180, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                53, 150, 217, 80, 13, 116, 254, 233, 125, 55, 245, 158, 173, 100, 89, 88, 75, 40,
                243, 196, 115, 247, 54, 208, 93, 189, 231, 84, 64, 190, 254, 204, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 135, 207, 251, 143, 122, 222, 191, 20, 105, 133, 40, 170, 204,
                32, 183, 11, 28, 133, 70, 126, 251, 78, 195, 126, 223, 192, 6, 92, 147, 14, 172,
                190, 216, 42, 88, 37, 0, 1, 113, 18, 32, 162, 71, 235, 16, 77, 230, 9, 12, 180,
                216, 116, 92, 67, 140, 234, 20, 189, 123, 201, 139, 95, 140, 146, 134, 145, 125,
                187, 94, 178, 152, 52, 50, 216, 42, 88, 37, 0, 1, 113, 18, 32, 187, 251, 2, 188,
                71, 58, 90, 46, 11, 165, 103, 240, 103, 38, 100, 148, 101, 27, 167, 170, 56, 127,
                6, 158, 114, 104, 188, 157, 253, 216, 83, 186, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                83, 2, 123, 140, 22, 74, 99, 54, 46, 82, 169, 163, 99, 250, 5, 195, 77, 168, 3,
                146, 16, 59, 19, 27, 38, 85, 138, 9, 195, 122, 82, 206, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 214, 88, 25, 42, 68, 59, 249, 156, 153, 212, 39, 192, 182, 119, 214, 228,
                151, 34, 184, 100, 242, 149, 220, 30, 253, 187, 157, 201, 191, 94, 145, 183, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 226, 43, 149, 125, 121, 88, 14, 69, 197, 225, 81,
                95, 140, 119, 14, 125, 252, 18, 64, 207, 49, 114, 177, 190, 117, 168, 17, 160, 50,
                148, 57, 93, 216, 42, 88, 37, 0, 1, 113, 18, 32, 60, 32, 6, 130, 227, 124, 97, 180,
                127, 180, 201, 217, 219, 122, 88, 125, 139, 243, 227, 52, 163, 113, 21, 165, 212,
                183, 41, 134, 215, 216, 129, 82, 216, 42, 88, 37, 0, 1, 113, 18, 32, 86, 147, 140,
                244, 205, 153, 245, 93, 211, 135, 232, 53, 175, 112, 184, 138, 16, 125, 40, 92,
                165, 1, 161, 28, 67, 176, 43, 242, 163, 120, 218, 109, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 156, 79, 8, 116, 99, 52, 194, 70, 82, 110, 235, 59, 109, 32, 243, 17, 143,
                245, 55, 28, 55, 176, 77, 204, 199, 17, 119, 161, 253, 174, 213, 191, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 34, 72, 121, 167, 211, 53, 118, 58, 79, 222, 13, 246, 26,
                44, 43, 226, 14, 178, 35, 251, 239, 209, 74, 180, 50, 177, 200, 150, 240, 248, 24,
                15, 216, 42, 88, 37, 0, 1, 113, 18, 32, 218, 155, 101, 88, 140, 62, 175, 150, 121,
                145, 63, 32, 113, 248, 154, 87, 73, 30, 53, 236, 31, 147, 207, 99, 184, 137, 1, 36,
                157, 185, 157, 73, 216, 42, 88, 37, 0, 1, 113, 18, 32, 247, 107, 40, 50, 239, 187,
                154, 141, 100, 132, 195, 216, 228, 26, 184, 172, 81, 81, 220, 121, 153, 147, 191,
                31, 14, 78, 75, 18, 246, 72, 177, 190, 216, 42, 88, 37, 0, 1, 113, 18, 32, 155, 92,
                19, 54, 224, 110, 253, 79, 64, 59, 40, 42, 152, 18, 13, 74, 199, 26, 52, 213, 215,
                203, 27, 161, 22, 49, 151, 249, 6, 87, 86, 210, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                3, 208, 125, 0, 70, 115, 123, 243, 70, 65, 224, 234, 104, 172, 252, 125, 78, 6,
                148, 187, 38, 77, 168, 59, 230, 234, 123, 157, 87, 204, 240, 158, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 52, 16, 34, 49, 198, 23, 45, 135, 44, 79, 31, 12, 158, 62, 129,
                222, 42, 150, 12, 22, 3, 171, 110, 17, 71, 96, 17, 21, 155, 46, 239, 1, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 206, 213, 118, 64, 248, 128, 49, 185, 36, 23, 151, 230,
                2, 94, 140, 137, 147, 108, 32, 134, 233, 223, 108, 183, 8, 15, 64, 201, 71, 130,
                119, 111, 216, 42, 88, 37, 0, 1, 113, 18, 32, 15, 248, 26, 72, 52, 41, 91, 197, 22,
                168, 207, 148, 146, 79, 161, 114, 220, 250, 251, 173, 103, 86, 130, 179, 223, 42,
                253, 187, 148, 131, 126, 229, 216, 42, 88, 37, 0, 1, 113, 18, 32, 237, 44, 110, 52,
                166, 167, 161, 147, 18, 178, 86, 164, 129, 46, 2, 99, 113, 117, 54, 238, 110, 66,
                26, 130, 12, 145, 70, 74, 138, 66, 170, 169, 216, 42, 88, 37, 0, 1, 113, 18, 32, 8,
                168, 83, 32, 16, 214, 72, 37, 137, 2, 230, 146, 195, 141, 137, 92, 252, 32, 87,
                115, 106, 145, 6, 14, 98, 42, 253, 29, 119, 211, 86, 248, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 134, 61, 208, 23, 174, 89, 190, 96, 141, 220, 31, 223, 65, 188, 253,
                168, 147, 127, 239, 226, 45, 209, 29, 161, 155, 103, 186, 8, 25, 30, 193, 176, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 97, 84, 196, 115, 197, 152, 154, 152, 254, 13, 128,
                12, 101, 0, 231, 17, 132, 230, 165, 228, 245, 113, 192, 46, 246, 55, 243, 224, 199,
                65, 45, 172, 216, 42, 88, 37, 0, 1, 113, 18, 32, 0, 11, 140, 225, 11, 199, 240, 2,
                142, 58, 114, 10, 212, 77, 113, 127, 175, 85, 11, 160, 129, 250, 181, 100, 202,
                135, 159, 248, 149, 219, 6, 194, 216, 42, 88, 37, 0, 1, 113, 18, 32, 172, 216, 141,
                170, 42, 151, 99, 192, 16, 153, 175, 183, 181, 147, 82, 57, 101, 29, 238, 61, 112,
                186, 188, 54, 132, 102, 150, 46, 160, 112, 79, 24, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 108, 100, 30, 50, 183, 158, 20, 65, 91, 17, 250, 127, 47, 73, 87, 64, 211, 252,
                208, 29, 250, 254, 103, 32, 93, 197, 182, 223, 113, 108, 135, 205, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 19, 160, 4, 201, 167, 133, 197, 92, 198, 16, 88, 70, 6, 129,
                173, 73, 228, 196, 89, 221, 238, 40, 8, 120, 228, 138, 162, 5, 161, 116, 220, 40,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 102, 168, 30, 1, 178, 198, 210, 235, 92, 7, 64,
                16, 7, 238, 161, 0, 174, 170, 250, 230, 33, 189, 97, 128, 72, 5, 217, 90, 50, 230,
                22, 185, 216, 42, 88, 37, 0, 1, 113, 18, 32, 25, 112, 166, 249, 17, 140, 58, 221,
                42, 242, 207, 181, 42, 37, 133, 203, 71, 242, 246, 168, 204, 4, 178, 0, 228, 250,
                172, 235, 134, 248, 97, 9, 216, 42, 88, 37, 0, 1, 113, 18, 32, 13, 203, 191, 226,
                38, 174, 196, 63, 120, 26, 10, 197, 63, 208, 197, 235, 59, 31, 254, 165, 115, 27,
                105, 64, 224, 150, 138, 3, 145, 21, 97, 10, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                234, 245, 91, 72, 14, 61, 10, 199, 168, 7, 247, 174, 74, 142, 73, 33, 62, 36, 132,
                98, 143, 200, 167, 181, 63, 244, 120, 105, 38, 34, 149, 24, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 8, 188, 225, 135, 95, 184, 97, 213, 195, 41, 87, 49, 227, 22, 5, 248,
                249, 74, 134, 69, 139, 105, 129, 155, 33, 83, 119, 146, 57, 97, 236, 245, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 24, 61, 177, 172, 55, 70, 252, 76, 47, 117, 237, 110,
                225, 5, 83, 129, 149, 39, 19, 54, 12, 171, 76, 68, 113, 214, 21, 33, 209, 40, 175,
                239, 216, 42, 88, 37, 0, 1, 113, 18, 32, 111, 22, 71, 60, 72, 33, 160, 169, 93, 42,
                158, 72, 31, 8, 137, 170, 99, 225, 123, 174, 175, 85, 200, 65, 81, 175, 220, 61,
                179, 180, 31, 227, 216, 42, 88, 37, 0, 1, 113, 18, 32, 110, 250, 62, 222, 165, 71,
                73, 111, 211, 68, 96, 0, 216, 194, 85, 239, 210, 11, 123, 223, 216, 224, 43, 84,
                180, 78, 184, 248, 162, 86, 82, 62, 216, 42, 88, 37, 0, 1, 113, 18, 32, 117, 175,
                216, 37, 55, 130, 113, 232, 40, 12, 30, 18, 162, 207, 123, 244, 160, 185, 123, 67,
                169, 94, 110, 147, 28, 20, 91, 247, 185, 26, 87, 205, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 101, 53, 32, 23, 192, 109, 108, 166, 245, 50, 238, 130, 81, 251, 26, 185,
                184, 203, 202, 93, 6, 189, 36, 241, 156, 70, 15, 107, 8, 14, 86, 27, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 121, 169, 16, 70, 166, 44, 47, 14, 102, 23, 15, 245, 179,
                18, 3, 34, 159, 82, 217, 111, 168, 33, 228, 84, 128, 69, 97, 15, 40, 111, 67, 7,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 63, 120, 160, 25, 148, 180, 206, 235, 217, 48,
                103, 253, 40, 237, 57, 51, 228, 196, 171, 92, 186, 49, 13, 179, 252, 93, 98, 249,
                49, 29, 30, 231, 216, 42, 88, 37, 0, 1, 113, 18, 32, 194, 228, 8, 192, 110, 90,
                230, 116, 11, 197, 23, 136, 46, 201, 217, 69, 202, 227, 222, 74, 18, 165, 160, 220,
                161, 47, 77, 247, 37, 3, 68, 27, 216, 42, 88, 37, 0, 1, 113, 18, 32, 157, 244, 42,
                44, 26, 161, 194, 120, 127, 27, 211, 222, 42, 224, 71, 67, 243, 139, 169, 245, 114,
                149, 197, 87, 73, 241, 214, 217, 50, 59, 147, 17, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 245, 216, 51, 181, 228, 138, 120, 6, 67, 48, 5, 17, 155, 228, 157, 226, 183, 1,
                99, 27, 248, 122, 162, 74, 33, 30, 76, 229, 161, 151, 90, 199, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 8, 110, 132, 86, 70, 161, 22, 110, 23, 246, 188, 96, 29, 14, 190,
                40, 202, 107, 151, 215, 191, 111, 216, 42, 108, 27, 244, 218, 220, 197, 13, 171,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 149, 9, 201, 150, 180, 187, 143, 135, 198, 130,
                86, 46, 44, 51, 83, 186, 71, 160, 98, 49, 197, 101, 75, 169, 177, 27, 87, 156, 115,
                222, 151, 4, 216, 42, 88, 37, 0, 1, 113, 18, 32, 245, 7, 50, 88, 35, 9, 87, 232,
                127, 106, 219, 96, 61, 230, 152, 131, 164, 251, 190, 54, 105, 10, 251, 142, 195,
                144, 115, 215, 225, 108, 109, 216, 216, 42, 88, 37, 0, 1, 113, 18, 32, 67, 133, 63,
                186, 212, 152, 36, 251, 92, 232, 172, 110, 230, 208, 238, 27, 144, 255, 216, 249,
                51, 117, 111, 249, 60, 221, 147, 195, 128, 134, 95, 89, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 55, 92, 100, 149, 54, 128, 187, 76, 1, 173, 166, 170, 75, 220, 152, 22,
                122, 158, 244, 7, 128, 84, 158, 237, 187, 58, 184, 100, 206, 216, 214, 178, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 243, 69, 37, 41, 217, 99, 190, 142, 226, 43, 229,
                204, 62, 229, 216, 102, 61, 212, 12, 50, 173, 24, 17, 221, 54, 51, 197, 96, 127,
                132, 30, 182, 216, 42, 88, 37, 0, 1, 113, 18, 32, 77, 172, 104, 205, 81, 196, 208,
                227, 180, 26, 60, 45, 204, 252, 205, 112, 121, 129, 160, 158, 50, 194, 94, 63, 124,
                207, 215, 247, 38, 6, 122, 40, 216, 42, 88, 37, 0, 1, 113, 18, 32, 121, 148, 200,
                145, 213, 156, 30, 39, 148, 132, 40, 125, 91, 199, 36, 216, 173, 187, 160, 56, 57,
                197, 105, 218, 88, 44, 38, 52, 100, 106, 112, 160, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 165, 43, 186, 23, 97, 54, 197, 248, 248, 251, 136, 163, 52, 103, 252, 184, 173,
                193, 87, 7, 133, 76, 120, 221, 63, 33, 231, 181, 189, 76, 88, 97, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 204, 125, 16, 158, 191, 16, 122, 0, 179, 91, 31, 163, 26, 230,
                3, 81, 154, 115, 32, 50, 7, 79, 175, 150, 104, 113, 188, 128, 240, 99, 49, 79, 131,
                26, 1, 1, 20, 145, 0, 246, 216, 42, 69, 0, 1, 85, 0, 0,
            ];
            let as_json_raw = serde_json::json!({"kind":2,"slot":16848018,"shredding":[{"entry_end_idx":0,"shred_end_idx":0},{"entry_end_idx":1,"shred_end_idx":1},{"entry_end_idx":2,"shred_end_idx":2},{"entry_end_idx":3,"shred_end_idx":3},{"entry_end_idx":4,"shred_end_idx":4},{"entry_end_idx":5,"shred_end_idx":5},{"entry_end_idx":6,"shred_end_idx":6},{"entry_end_idx":7,"shred_end_idx":7},{"entry_end_idx":8,"shred_end_idx":8},{"entry_end_idx":9,"shred_end_idx":9},{"entry_end_idx":10,"shred_end_idx":10},{"entry_end_idx":11,"shred_end_idx":11},{"entry_end_idx":12,"shred_end_idx":12},{"entry_end_idx":13,"shred_end_idx":13},{"entry_end_idx":14,"shred_end_idx":14},{"entry_end_idx":15,"shred_end_idx":-1},{"entry_end_idx":16,"shred_end_idx":-1},{"entry_end_idx":17,"shred_end_idx":-1},{"entry_end_idx":18,"shred_end_idx":-1},{"entry_end_idx":19,"shred_end_idx":15},{"entry_end_idx":20,"shred_end_idx":16},{"entry_end_idx":21,"shred_end_idx":17},{"entry_end_idx":22,"shred_end_idx":18},{"entry_end_idx":23,"shred_end_idx":19},{"entry_end_idx":24,"shred_end_idx":20},{"entry_end_idx":25,"shred_end_idx":21},{"entry_end_idx":26,"shred_end_idx":22},{"entry_end_idx":27,"shred_end_idx":23},{"entry_end_idx":28,"shred_end_idx":24},{"entry_end_idx":29,"shred_end_idx":25},{"entry_end_idx":30,"shred_end_idx":26},{"entry_end_idx":31,"shred_end_idx":27},{"entry_end_idx":32,"shred_end_idx":28},{"entry_end_idx":33,"shred_end_idx":29},{"entry_end_idx":34,"shred_end_idx":30},{"entry_end_idx":35,"shred_end_idx":-1},{"entry_end_idx":36,"shred_end_idx":31},{"entry_end_idx":37,"shred_end_idx":-1},{"entry_end_idx":38,"shred_end_idx":-1},{"entry_end_idx":39,"shred_end_idx":-1},{"entry_end_idx":40,"shred_end_idx":-1},{"entry_end_idx":41,"shred_end_idx":-1},{"entry_end_idx":42,"shred_end_idx":-1},{"entry_end_idx":43,"shred_end_idx":-1},{"entry_end_idx":44,"shred_end_idx":34},{"entry_end_idx":45,"shred_end_idx":-1},{"entry_end_idx":46,"shred_end_idx":-1},{"entry_end_idx":47,"shred_end_idx":-1},{"entry_end_idx":48,"shred_end_idx":-1},{"entry_end_idx":49,"shred_end_idx":-1},{"entry_end_idx":50,"shred_end_idx":-1},{"entry_end_idx":51,"shred_end_idx":-1},{"entry_end_idx":52,"shred_end_idx":37},{"entry_end_idx":53,"shred_end_idx":-1},{"entry_end_idx":54,"shred_end_idx":-1},{"entry_end_idx":55,"shred_end_idx":-1},{"entry_end_idx":56,"shred_end_idx":39},{"entry_end_idx":57,"shred_end_idx":40},{"entry_end_idx":58,"shred_end_idx":41},{"entry_end_idx":59,"shred_end_idx":42},{"entry_end_idx":60,"shred_end_idx":43},{"entry_end_idx":61,"shred_end_idx":44},{"entry_end_idx":62,"shred_end_idx":45},{"entry_end_idx":63,"shred_end_idx":-1},{"entry_end_idx":64,"shred_end_idx":46},{"entry_end_idx":65,"shred_end_idx":47},{"entry_end_idx":66,"shred_end_idx":48},{"entry_end_idx":67,"shred_end_idx":49},{"entry_end_idx":68,"shred_end_idx":50},{"entry_end_idx":69,"shred_end_idx":51},{"entry_end_idx":70,"shred_end_idx":52},{"entry_end_idx":71,"shred_end_idx":53},{"entry_end_idx":72,"shred_end_idx":54},{"entry_end_idx":73,"shred_end_idx":55},{"entry_end_idx":74,"shred_end_idx":56},{"entry_end_idx":75,"shred_end_idx":57},{"entry_end_idx":76,"shred_end_idx":58},{"entry_end_idx":77,"shred_end_idx":59},{"entry_end_idx":78,"shred_end_idx":60},{"entry_end_idx":79,"shred_end_idx":61},{"entry_end_idx":80,"shred_end_idx":62},{"entry_end_idx":81,"shred_end_idx":63},{"entry_end_idx":82,"shred_end_idx":64},{"entry_end_idx":83,"shred_end_idx":65},{"entry_end_idx":84,"shred_end_idx":66}],"entries":[{"/":"bafyreid2pnfheom5ewa7mazfg3dueqg7f3aayter4jp7f7rc4cvzyeqc5e"},{"/":"bafyreig7y6dwlmqz4jukrulh4wguhwgq75rs63kn376gjon255inyx4sjm"},{"/":"bafyreiew7mg2npzkuy6mpnkzaap5zmgzpmp2cokhnbmhog2omln4hxbvqi"},{"/":"bafyreic4pbfo2zeizxh4duzvarpp5fqfu6hkipdeqzjugc3pjqsbp6hauy"},{"/":"bafyreiaphvmkua6oh65tszmqvirrzzqaaednzqgtj7t225h4aq42j2lv3q"},{"/":"bafyreiciqm6czupeogkyleki42qy6pizphm7qdcdbb5m7osd3yn2xu46om"},{"/":"bafyreiagi2mmtzwtogu7akfdx3dobckmmudrwwtdva4v7tp7d2gwbszmtq"},{"/":"bafyreibtewsoti2exf2t2j32tprl5ayolrejc3ig6ecb5ikysm3fy2rllu"},{"/":"bafyreib3mlzeouportbpblkcbclpguhex77p2s754logx6tm2ia7djglue"},{"/":"bafyreiann4uwj6lcndwt5a43wpnmxmsz6ijvqdrqx57djlje5ydx3fbz5i"},{"/":"bafyreidcz2einavxee7o34ik3kf66btel5ugjimkutaa2rszjcro2dqt3i"},{"/":"bafyreigvw2gsd5mne7l3wjbscenfkhusyfj3knsjol7pwfufh5kmvvcu2a"},{"/":"bafyreia2qbtzdsjhlnk6anxtshhpittir6xoxxn22mv7f3r5nhx5r56bry"},{"/":"bafyreicsb5j6fmxhhryhgshfo3z3l2mcgi6fb6eaph7anfvdhakqywkdwm"},{"/":"bafyreidykikh4pavbjxlxqsv5fupdorb7nrqv4xeeyhhajcz7nm3b7r4l4"},{"/":"bafyreibsqxrex6y55t5nbwbgswpk2t3xigo7tns4jvw5gbfpcqlck3h4ky"},{"/":"bafyreib2elshkyv54ljn5552slpkm3cv5r5nrvgdm5kjw7jdahm2z4o54e"},{"/":"bafyreidfz5uamlso2b3cr6rmkbvtrvnovesm5fia7uqnphhjgaduvwm4ne"},{"/":"bafyreiaat3nsoscfbdvfrkqnwii2c6sglcy6sfmooxhuwihjkrzzzclofe"},{"/":"bafyreiczg4tsctu5h6tzcrbmuuzedibwxrnhnnvu2t6zqiv32agd5ihmuu"},{"/":"bafyreia5vj25jkczh2hpce4uuvwvmyfuvjecgrmpvov5xqivyfoiwx4lfm"},{"/":"bafyreihdtes3sjuuv33cocgtgemy7n65z6uqqtbu2tujzpcfqtws6qle7u"},{"/":"bafyreieqc72qh3cvoghvloqlgisyelntrd4os64skj7saxbe55p7fxblym"},{"/":"bafyreidanjusj2qb7rogbdnn647lvqz7acljkyzpja76hvctnex7vfkyvy"},{"/":"bafyreiflvro2pygpwqt5onvg7j4bb5eedqetwb6yibpjw2z2n6upt7bx4y"},{"/":"bafyreia7rzquteo3zoy4xmydgzhggo4xi6wuccsqpe4cb6jgtr425itnoq"},{"/":"bafyreibt665vuyili5c7nia3dlwjupstrmohxqxjvcgloz7uuau4xntjsa"},{"/":"bafyreifhnjejjihvbnjhkshyle6rsvib4q7ss6jis37ozieivtolyw6qo4"},{"/":"bafyreiegcjkyvmkdokt7mjp6ukes7mr5byz3sxbcy5kvk4kzq5wgjhmuwu"},{"/":"bafyreigjumeksxuv4u3stmv3uis2zjcgt7dwsltge5vspdd52sitj6l35e"},{"/":"bafyreifn5x2qbrzbckslrdtzrj5ds4v23fa7gb4ehq7jhgfqckqkvvyfb4"},{"/":"bafyreievizxgpsppfbsbamcpoifcuhbjgxn3k5bzdatvfpcowdpenlwxf4"},{"/":"bafyreid7mmrvrvkia72td4fyldl4uu3jd74vnavracds3pcfvtrd7x2c64"},{"/":"bafyreibgldedgli2s3stggcqoefhltedouovhghmfji3mll7ud7qjhiywq"},{"/":"bafyreibvs3mvadlu73ux2n7vt2wwiwkyjmuphrdt643naxn545kebpx6zq"},{"/":"bafyreiehz75y66w6x4kgtbjivlgcbnyldscum7x3j3bx5x6aazojgdvmxy"},{"/":"bafyreifci7vratpgbegljwdulrbyz2quxv54tc27rsjinel5xnplfgbugi"},{"/":"bafyreif37mblyrz2lixaxjlh6btsmzeumun2pkryp4dj44tixso73wctxi"},{"/":"bafyreictaj5yyfskmm3c4uvjunr7ubodjwuaheqqhmjrwjsvrie4g6sszy"},{"/":"bafyreigwlamsurb37gojtvbhyc3hpvxes4rlqzhssxob57n3txe36xurw4"},{"/":"bafyreihcfokx26kybzc4lykrl6ghodt57qjebtzroky345nicgqdffbzlu"},{"/":"bafyreib4eadify34mg2h7ngj3hnxuwd5rpz6gnfdoek2lvfxfgdnpwebki"},{"/":"bafyreicwsogpjtmz6vo5hb7igwxxboekcb6sqxffagqryq5qfpzkg6g2nu"},{"/":"bafyreie4j4ehiyzuyjdfe3xlhnwsb4yrr72tohbxwbg4zryro6q73lwvx4"},{"/":"bafyreibcjb42puzvoy5e7xqn6yncyk7cb2zch67p2fflimvrzclpb6ayb4"},{"/":"bafyreig2tnsvrdb6v6lhtej7eby7rgsxjepdl3a7sphwhoejaesj3om5je"},{"/":"bafyreihxnmudf353tkgwjbgd3dsbvofmkfi5y6mzso7r6dsojmjpmsfrxy"},{"/":"bafyreie3lqjtnydo7vhuaozifkmbedkky4ndjvoxzmn2cfrrs74qmv2w2i"},{"/":"bafyreiad2b6qarttppzumqpa5jukz7d5jydjjozgjwudxzxkpoovpthqty"},{"/":"bafyreibucarddrqxfwdsyty7bspd5ao6fklayfqdvnxbcr3acekzwlxpae"},{"/":"bafyreigo2v3eb6eagg4sif4x4ybf5dejsnwcbbxj35wlocapideupatxn4"},{"/":"bafyreiap7aneqnbjlpcrnkgpssje7ils3t5pxllhk2blhxzk7w5zja364u"},{"/":"bafyreihnfrxdjjvhugjrfmswusas4atdof2tn3toiiniederizfiuqvkve"},{"/":"bafyreiaivbjsaegwjasysaxgslby3ck47qqfo43kseda4yrk7uoxpu2w7a"},{"/":"bafyreieghxibplszxzqi3xa735a3z7nisn767yrn2eo2dg3hxiebshwbwa"},{"/":"bafyreidbktchhrmytkmp4dmabrsqbzyrqttklzhvohac55rx6pqmoqjnvq"},{"/":"bafyreiaabogocc6h6abi4otsblke24l7v5kqxieb7k2wjsuht74jlwygyi"},{"/":"bafyreifm3cg2ukuxmpabbgnpw62zgurzmuo64plqxk6dnbdgsyxka4cpda"},{"/":"bafyreidmmqpdfn46cravwep2p4xusv2a2p6nahp27ztsaxofw3pxc3ehzu"},{"/":"bafyreiatuacmtj4fyvommecyiydidlkj4tcftxpofaehrzekuic2c5g4fa"},{"/":"bafyreidgvapadmwg2lvvyb2acad65iiav2vpvzrbxvqyasaf3fndfzqwxe"},{"/":"bafyreiazoctpsemmhlosv4wpwuvclboli7zpnkgmaszabzh2vtvyn6dbbe"},{"/":"bafyreianzo76ejvoyq7xqgqkyu75brplhmp75jltdnuubyewribzcflbbi"},{"/":"bafyreihk6vnuqdr5bld2qb7xvzfi4sjbhysiiyupzct3kp7upbusmiuvda"},{"/":"bafyreiaixtqyox5ymhk4gkkxghrrmbpy7ffimrmlngazwikto6jdsypm6u"},{"/":"bafyreiayhwy2yn2g7rgc65pnn3qqku4bsutrgnqmvngei4owcuq5ckfp54"},{"/":"bafyreidpczdtysbbucuv2ku6japqrcnkmpqxxlvpkxeecunp3q63hna74m"},{"/":"bafyreido7i7n5jkhjfx5grdaadmmevpp2ifxxx6y4avvjncoxd4kevsshy"},{"/":"bafyreidvv7mckn4cohucqda6ckrm667uuc4xwq5jlzxjghaulp33sgsxzu"},{"/":"bafyreidfguqbpqdnnstpkmxoqji7wgvzxdf4uxigxuspdhcgb5vqqdswdm"},{"/":"bafyreidzveienjrmf4hgmfyp6wzreazct5jns35iehsfjacfmehsq32da4"},{"/":"bafyreib7pcqbtffuz3v5smdh7uuo2ojt4tckwxf2geg3h7c5ml4tchi644"},{"/":"bafyreigc4qema3s24z2axrixraxmtwkfzlr54sqsuwqnzijpjx3ska2edm"},{"/":"bafyreie56qvcygvbyj4h6g6t3yvoar2d6of2t5lssxcvospr23mteo4tce"},{"/":"bafyreihv3az3lzekpadegmafcgn6jhpcw4awgg7ypkreuii6jts2df22y4"},{"/":"bafyreiain2cfmrvbczxbp5v4maoq5prizjvzpv57n7mcu3a36tnnzrinvm"},{"/":"bafyreievbheznnf3r6d4naswfywdgu52i6qgemofmvf2tmi3k6ohhxuxaq"},{"/":"bafyreihva4zfqiyjk7uh62w3ma66ngedut534ntjbl5y5q4qopl6c3dn3a"},{"/":"bafyreicdqu73vveyet5vz2fmn3tnb3q3sd75r6jtovx7spg5spbybbs7le"},{"/":"bafyreibxlrsjknuaxngadlngvjf5zgawpkppib4akspo3oz2xbsm5wgwwi"},{"/":"bafyreihtiusstwldx2hoek7fzq7olwdghxkaymvndai52nrtyvqh7ba6wy"},{"/":"bafyreicnvrum2uoe2dr3igr4fxgpztlqpga2bhrsyjpd67gp273smbt2fa"},{"/":"bafyreidzstejdvm4dytzjbbipvn4ojgyvw52aobzyvu5uwbmey2gi2tqua"},{"/":"bafyreifffo5boyjwyx4pr64ium2gp7fyvxavob4fjr4n2pzb46232tcyme"},{"/":"bafyreigmpuij5pyqpialgwy7umnoma2rtjzsamqhj6xzm2drxsapayzrj4"}],"meta":{"parent_slot":16848017,"blocktime":0,"block_height":null},"rewards":{"/":"bafkqaaa"}});

            let block = Block::from_bytes(raw).unwrap();
            let as_json = block.to_json();
            assert_eq!(as_json, as_json_raw);
        }
        {
            let raw = vec![
                134, 2, 26, 1, 1, 20, 145, 152, 71, 130, 0, 0, 130, 1, 1, 130, 2, 2, 130, 3, 3,
                130, 4, 4, 130, 5, 5, 130, 6, 6, 130, 7, 7, 130, 8, 8, 130, 9, 9, 130, 10, 10, 130,
                11, 11, 130, 12, 12, 130, 13, 13, 130, 14, 14, 130, 15, 15, 130, 16, 16, 130, 17,
                17, 130, 18, 18, 130, 19, 19, 130, 20, 20, 130, 21, 21, 130, 22, 22, 130, 23, 23,
                130, 24, 24, 24, 24, 130, 24, 25, 24, 25, 130, 24, 26, 24, 26, 130, 24, 27, 24, 27,
                130, 24, 28, 24, 28, 130, 24, 29, 24, 29, 130, 24, 30, 24, 30, 130, 24, 31, 24, 31,
                130, 24, 32, 24, 32, 130, 24, 33, 24, 33, 130, 24, 34, 24, 34, 130, 24, 35, 24, 35,
                130, 24, 36, 24, 36, 130, 24, 37, 24, 37, 130, 24, 38, 24, 38, 130, 24, 39, 24, 39,
                130, 24, 40, 24, 40, 130, 24, 41, 24, 41, 130, 24, 42, 24, 42, 130, 24, 43, 32,
                130, 24, 44, 24, 43, 130, 24, 45, 24, 44, 130, 24, 46, 24, 45, 130, 24, 47, 24, 46,
                130, 24, 48, 24, 47, 130, 24, 49, 24, 48, 130, 24, 50, 24, 49, 130, 24, 51, 24, 50,
                130, 24, 52, 24, 51, 130, 24, 53, 24, 52, 130, 24, 54, 24, 53, 130, 24, 55, 24, 54,
                130, 24, 56, 24, 55, 130, 24, 57, 24, 56, 130, 24, 58, 24, 57, 130, 24, 59, 24, 58,
                130, 24, 60, 24, 59, 130, 24, 61, 24, 60, 130, 24, 62, 24, 61, 130, 24, 63, 24, 62,
                130, 24, 64, 24, 63, 130, 24, 65, 24, 64, 130, 24, 66, 32, 130, 24, 67, 32, 130,
                24, 68, 24, 66, 130, 24, 69, 32, 130, 24, 70, 24, 67, 152, 71, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 107, 252, 222, 154, 111, 35, 208, 59, 51, 242, 164, 209, 19, 93,
                195, 151, 8, 21, 113, 155, 9, 169, 86, 184, 131, 179, 29, 78, 184, 80, 35, 225,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 3, 171, 25, 222, 40, 222, 210, 215, 214, 101,
                160, 177, 137, 107, 237, 223, 179, 12, 168, 138, 239, 112, 169, 173, 207, 107, 28,
                197, 179, 20, 195, 138, 216, 42, 88, 37, 0, 1, 113, 18, 32, 75, 228, 66, 84, 61,
                73, 66, 46, 131, 238, 68, 236, 81, 97, 19, 89, 166, 36, 253, 110, 64, 73, 212, 32,
                214, 159, 81, 188, 115, 62, 207, 159, 216, 42, 88, 37, 0, 1, 113, 18, 32, 17, 132,
                44, 89, 230, 253, 72, 110, 89, 138, 24, 237, 67, 178, 57, 176, 176, 44, 6, 6, 121,
                122, 160, 132, 115, 242, 169, 46, 5, 44, 211, 57, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 160, 191, 212, 184, 148, 79, 224, 18, 105, 189, 60, 42, 198, 85, 227, 123, 232,
                113, 32, 79, 123, 68, 1, 14, 72, 165, 138, 87, 42, 129, 170, 34, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 210, 121, 100, 244, 231, 209, 39, 196, 34, 23, 46, 77, 223, 107,
                8, 43, 237, 165, 133, 235, 4, 37, 61, 74, 218, 192, 208, 225, 73, 227, 160, 148,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 113, 54, 73, 45, 146, 105, 171, 213, 222, 240,
                168, 115, 214, 165, 255, 63, 142, 41, 37, 211, 241, 200, 113, 193, 7, 2, 244, 244,
                85, 20, 204, 234, 216, 42, 88, 37, 0, 1, 113, 18, 32, 61, 128, 39, 21, 174, 130,
                209, 67, 213, 227, 166, 226, 151, 16, 213, 130, 113, 36, 64, 13, 115, 148, 199, 57,
                255, 86, 61, 201, 74, 60, 228, 201, 216, 42, 88, 37, 0, 1, 113, 18, 32, 18, 194,
                173, 195, 240, 52, 93, 24, 103, 176, 41, 96, 97, 141, 216, 48, 67, 83, 27, 14, 88,
                141, 217, 55, 98, 100, 2, 104, 13, 251, 4, 118, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                133, 19, 111, 12, 239, 83, 78, 251, 154, 221, 123, 64, 201, 127, 215, 206, 182,
                199, 233, 201, 31, 184, 84, 90, 116, 88, 205, 227, 204, 232, 239, 66, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 6, 152, 238, 9, 148, 161, 124, 80, 185, 41, 75, 142, 197,
                59, 232, 16, 35, 203, 97, 125, 188, 132, 240, 188, 20, 16, 24, 29, 212, 207, 0,
                146, 216, 42, 88, 37, 0, 1, 113, 18, 32, 86, 72, 222, 206, 54, 198, 0, 254, 13, 55,
                50, 95, 97, 142, 23, 184, 194, 244, 100, 69, 63, 64, 71, 177, 208, 245, 39, 112,
                231, 161, 44, 155, 216, 42, 88, 37, 0, 1, 113, 18, 32, 212, 202, 79, 77, 154, 238,
                160, 20, 67, 140, 191, 223, 29, 201, 201, 237, 211, 15, 203, 38, 87, 149, 154, 39,
                135, 87, 28, 139, 51, 145, 107, 237, 216, 42, 88, 37, 0, 1, 113, 18, 32, 42, 91,
                105, 231, 33, 144, 186, 211, 34, 220, 247, 250, 132, 170, 28, 52, 182, 81, 150, 19,
                73, 138, 154, 49, 204, 23, 162, 27, 242, 139, 254, 243, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 15, 109, 161, 89, 99, 214, 164, 101, 91, 179, 49, 152, 31, 218, 152, 116,
                246, 142, 61, 68, 123, 43, 182, 129, 199, 194, 1, 40, 142, 87, 224, 117, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 167, 163, 224, 246, 152, 137, 116, 51, 197, 237, 223,
                106, 26, 125, 80, 183, 253, 0, 65, 112, 70, 250, 45, 220, 81, 120, 191, 159, 42,
                49, 179, 199, 216, 42, 88, 37, 0, 1, 113, 18, 32, 102, 112, 182, 218, 234, 240, 88,
                198, 112, 241, 117, 223, 28, 224, 78, 52, 204, 159, 118, 33, 81, 99, 84, 159, 150,
                45, 199, 202, 183, 28, 212, 104, 216, 42, 88, 37, 0, 1, 113, 18, 32, 97, 4, 122,
                35, 45, 165, 154, 152, 38, 42, 89, 185, 168, 198, 59, 13, 107, 46, 162, 143, 119,
                27, 12, 170, 201, 181, 163, 129, 181, 39, 247, 149, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 106, 235, 11, 52, 211, 189, 226, 123, 107, 90, 53, 128, 69, 77, 59, 40, 222,
                100, 217, 71, 93, 205, 39, 3, 230, 107, 46, 100, 75, 51, 175, 161, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 216, 253, 139, 219, 231, 67, 111, 245, 47, 135, 191, 100, 230,
                209, 180, 24, 21, 105, 133, 10, 48, 43, 78, 98, 13, 107, 55, 35, 108, 102, 227, 52,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 127, 166, 21, 216, 72, 137, 183, 166, 240, 232,
                115, 94, 243, 179, 181, 211, 56, 220, 107, 45, 145, 244, 70, 203, 182, 51, 241, 49,
                3, 72, 84, 228, 216, 42, 88, 37, 0, 1, 113, 18, 32, 139, 253, 171, 228, 222, 205,
                246, 158, 68, 131, 173, 78, 143, 90, 52, 18, 169, 21, 252, 111, 244, 155, 182, 72,
                14, 197, 91, 179, 122, 157, 16, 151, 216, 42, 88, 37, 0, 1, 113, 18, 32, 18, 177,
                245, 208, 225, 13, 137, 109, 122, 42, 246, 107, 203, 41, 234, 58, 67, 183, 232,
                200, 152, 123, 141, 30, 66, 172, 210, 73, 37, 196, 240, 118, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 180, 158, 72, 213, 233, 193, 35, 87, 71, 188, 75, 156, 245, 87, 154,
                55, 220, 73, 154, 69, 221, 186, 10, 226, 124, 126, 167, 214, 199, 185, 32, 92, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 65, 136, 38, 74, 95, 230, 158, 11, 9, 67, 9, 103,
                244, 194, 120, 96, 175, 132, 77, 103, 170, 17, 141, 243, 179, 70, 62, 144, 163,
                232, 29, 99, 216, 42, 88, 37, 0, 1, 113, 18, 32, 192, 55, 92, 235, 203, 52, 85,
                154, 167, 18, 28, 40, 14, 41, 100, 228, 8, 91, 187, 129, 208, 145, 224, 128, 136,
                80, 194, 55, 81, 7, 52, 200, 216, 42, 88, 37, 0, 1, 113, 18, 32, 214, 214, 109, 94,
                100, 240, 86, 6, 162, 251, 234, 66, 223, 160, 186, 47, 114, 225, 208, 145, 129, 9,
                5, 193, 242, 250, 80, 102, 245, 237, 118, 181, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                85, 243, 183, 201, 57, 81, 131, 12, 230, 162, 194, 102, 187, 0, 169, 73, 29, 50,
                112, 249, 26, 158, 46, 252, 239, 228, 193, 217, 59, 49, 224, 75, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 53, 45, 54, 21, 94, 37, 66, 152, 40, 240, 188, 135, 73, 193,
                103, 214, 2, 118, 225, 31, 119, 14, 168, 167, 212, 28, 228, 137, 190, 53, 152, 72,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 249, 57, 100, 166, 186, 203, 208, 173, 56, 131,
                89, 43, 196, 201, 210, 61, 8, 138, 252, 81, 173, 107, 140, 160, 165, 49, 140, 166,
                36, 27, 249, 68, 216, 42, 88, 37, 0, 1, 113, 18, 32, 57, 187, 192, 199, 176, 248,
                207, 112, 157, 63, 58, 122, 49, 48, 194, 187, 11, 168, 221, 9, 104, 110, 197, 196,
                43, 120, 172, 182, 106, 175, 148, 195, 216, 42, 88, 37, 0, 1, 113, 18, 32, 249, 71,
                28, 84, 93, 255, 105, 45, 28, 197, 221, 85, 218, 109, 222, 198, 194, 237, 56, 169,
                56, 55, 121, 179, 169, 105, 172, 144, 169, 113, 150, 146, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 183, 46, 96, 185, 74, 143, 6, 236, 211, 28, 65, 65, 244, 126, 236,
                199, 211, 111, 163, 82, 58, 121, 118, 78, 64, 221, 238, 189, 17, 130, 91, 224, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 168, 58, 236, 198, 186, 213, 77, 70, 85, 205, 211,
                185, 8, 131, 144, 2, 6, 74, 45, 82, 210, 51, 209, 81, 108, 196, 179, 73, 233, 191,
                184, 104, 216, 42, 88, 37, 0, 1, 113, 18, 32, 211, 156, 98, 214, 121, 20, 191, 242,
                95, 185, 186, 19, 224, 6, 232, 72, 164, 243, 228, 186, 146, 236, 170, 229, 241, 30,
                190, 106, 207, 97, 173, 147, 216, 42, 88, 37, 0, 1, 113, 18, 32, 4, 10, 230, 30,
                83, 114, 84, 188, 113, 159, 27, 56, 34, 183, 159, 196, 93, 7, 108, 241, 67, 153,
                108, 122, 138, 190, 1, 229, 46, 1, 96, 210, 216, 42, 88, 37, 0, 1, 113, 18, 32, 65,
                17, 1, 175, 210, 132, 132, 120, 164, 45, 172, 214, 211, 64, 214, 77, 67, 195, 184,
                1, 170, 105, 240, 78, 217, 133, 233, 180, 153, 161, 160, 24, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 187, 21, 27, 143, 162, 205, 13, 112, 235, 209, 48, 203, 186, 32, 14,
                194, 205, 252, 232, 180, 214, 76, 238, 77, 206, 198, 105, 216, 18, 208, 189, 99,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 218, 245, 42, 212, 140, 0, 18, 175, 120, 159,
                163, 178, 228, 32, 131, 240, 48, 214, 55, 166, 135, 122, 33, 189, 74, 208, 241,
                162, 112, 9, 75, 3, 216, 42, 88, 37, 0, 1, 113, 18, 32, 217, 40, 182, 185, 124,
                151, 131, 33, 168, 13, 104, 51, 80, 92, 78, 52, 46, 108, 52, 142, 52, 119, 35, 92,
                138, 49, 207, 91, 144, 183, 86, 218, 216, 42, 88, 37, 0, 1, 113, 18, 32, 1, 105,
                111, 61, 213, 126, 126, 107, 148, 172, 155, 145, 112, 89, 26, 244, 255, 34, 80, 81,
                110, 159, 35, 151, 182, 120, 156, 6, 26, 214, 255, 27, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 255, 240, 227, 199, 124, 121, 116, 19, 213, 194, 138, 153, 126, 177, 25,
                87, 15, 209, 118, 250, 42, 156, 192, 213, 126, 115, 3, 231, 85, 234, 35, 98, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 222, 236, 232, 155, 62, 245, 183, 121, 97, 110, 114,
                81, 235, 124, 200, 39, 228, 214, 117, 220, 12, 98, 12, 131, 148, 58, 187, 13, 203,
                8, 108, 175, 216, 42, 88, 37, 0, 1, 113, 18, 32, 79, 149, 212, 92, 183, 183, 109,
                136, 206, 85, 160, 57, 235, 4, 131, 98, 66, 181, 126, 194, 84, 158, 50, 185, 182,
                12, 148, 106, 130, 105, 23, 102, 216, 42, 88, 37, 0, 1, 113, 18, 32, 87, 156, 51,
                39, 67, 129, 57, 181, 238, 125, 188, 86, 156, 188, 104, 193, 59, 89, 180, 141, 46,
                123, 63, 228, 105, 17, 36, 172, 138, 58, 224, 34, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 179, 122, 138, 124, 237, 124, 113, 65, 233, 212, 222, 176, 79, 127, 76, 143,
                29, 143, 106, 190, 95, 145, 194, 14, 224, 150, 8, 30, 83, 198, 253, 21, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 192, 2, 217, 158, 73, 39, 33, 111, 94, 222, 37, 160,
                165, 239, 27, 165, 80, 151, 17, 0, 104, 37, 82, 212, 141, 242, 253, 68, 250, 255,
                132, 187, 216, 42, 88, 37, 0, 1, 113, 18, 32, 47, 166, 152, 189, 43, 243, 69, 85,
                78, 227, 139, 37, 83, 208, 180, 26, 246, 23, 118, 27, 222, 95, 175, 43, 150, 13,
                222, 178, 143, 70, 162, 170, 216, 42, 88, 37, 0, 1, 113, 18, 32, 201, 122, 116, 49,
                182, 202, 55, 211, 123, 161, 58, 145, 110, 0, 168, 178, 108, 153, 214, 40, 172, 10,
                80, 229, 183, 182, 24, 4, 113, 36, 245, 85, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                215, 11, 36, 198, 68, 102, 19, 45, 18, 92, 58, 5, 188, 159, 114, 242, 112, 32, 239,
                159, 244, 152, 236, 47, 170, 192, 105, 172, 182, 153, 131, 149, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 250, 14, 184, 132, 127, 91, 45, 10, 165, 76, 204, 6, 4, 227, 105,
                141, 236, 121, 152, 23, 217, 96, 13, 25, 115, 18, 89, 221, 38, 15, 109, 218, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 130, 106, 3, 0, 30, 62, 101, 116, 88, 241, 74, 105,
                76, 243, 246, 27, 68, 91, 125, 160, 187, 18, 159, 137, 212, 43, 173, 240, 83, 20,
                138, 216, 216, 42, 88, 37, 0, 1, 113, 18, 32, 178, 27, 13, 79, 67, 124, 217, 124,
                6, 105, 128, 187, 141, 39, 155, 103, 222, 167, 130, 49, 210, 252, 132, 53, 178,
                239, 95, 241, 73, 180, 12, 188, 216, 42, 88, 37, 0, 1, 113, 18, 32, 32, 53, 250,
                54, 186, 119, 212, 131, 102, 41, 223, 25, 225, 18, 152, 34, 238, 4, 27, 231, 7,
                155, 4, 8, 148, 49, 250, 138, 45, 195, 189, 191, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 105, 95, 254, 194, 29, 242, 71, 15, 226, 53, 42, 233, 18, 112, 54, 31, 159,
                175, 207, 55, 212, 75, 167, 93, 11, 150, 149, 35, 242, 106, 214, 44, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 235, 70, 135, 167, 105, 125, 103, 82, 255, 80, 14, 51, 173,
                99, 56, 226, 171, 59, 136, 87, 83, 169, 210, 233, 106, 244, 243, 175, 194, 22, 235,
                222, 216, 42, 88, 37, 0, 1, 113, 18, 32, 101, 68, 189, 131, 179, 135, 171, 189,
                211, 51, 71, 86, 50, 65, 94, 29, 202, 244, 208, 245, 183, 7, 17, 118, 170, 38, 116,
                108, 200, 208, 168, 139, 216, 42, 88, 37, 0, 1, 113, 18, 32, 58, 116, 115, 165,
                222, 224, 155, 192, 22, 206, 56, 59, 213, 243, 216, 221, 111, 133, 203, 129, 146,
                0, 94, 253, 87, 144, 212, 138, 186, 178, 71, 143, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 219, 255, 86, 133, 250, 1, 8, 14, 82, 99, 168, 227, 70, 88, 101, 4, 48, 176,
                226, 9, 236, 177, 153, 49, 186, 12, 108, 102, 173, 117, 232, 206, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 159, 39, 86, 38, 227, 25, 177, 39, 63, 49, 104, 199, 45, 220,
                95, 80, 131, 108, 255, 30, 186, 228, 103, 116, 159, 11, 212, 240, 84, 67, 196, 206,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 55, 35, 228, 18, 125, 175, 109, 213, 134, 194,
                29, 48, 32, 64, 141, 101, 237, 223, 26, 21, 62, 143, 202, 94, 197, 161, 101, 193,
                57, 137, 87, 210, 216, 42, 88, 37, 0, 1, 113, 18, 32, 218, 215, 230, 182, 25, 179,
                163, 166, 244, 59, 62, 193, 243, 128, 82, 18, 30, 86, 121, 124, 159, 208, 156, 238,
                170, 16, 247, 253, 162, 101, 49, 57, 216, 42, 88, 37, 0, 1, 113, 18, 32, 43, 203,
                122, 214, 61, 210, 1, 203, 134, 235, 144, 213, 139, 226, 142, 193, 231, 194, 56,
                50, 33, 144, 24, 254, 39, 199, 223, 107, 113, 179, 95, 10, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 99, 189, 219, 0, 65, 165, 50, 130, 155, 145, 223, 113, 74, 192, 179,
                178, 127, 214, 247, 127, 84, 133, 99, 236, 202, 209, 20, 17, 137, 134, 106, 28,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 48, 199, 183, 142, 158, 89, 189, 81, 17, 58,
                154, 68, 150, 33, 101, 25, 111, 5, 103, 120, 172, 104, 179, 58, 132, 144, 172, 193,
                105, 209, 43, 108, 216, 42, 88, 37, 0, 1, 113, 18, 32, 47, 230, 201, 236, 32, 173,
                83, 93, 186, 13, 23, 55, 6, 41, 96, 151, 51, 229, 139, 98, 50, 33, 194, 26, 173,
                215, 247, 84, 143, 241, 251, 112, 216, 42, 88, 37, 0, 1, 113, 18, 32, 222, 243,
                209, 213, 13, 112, 138, 32, 233, 147, 109, 154, 225, 100, 95, 171, 218, 179, 177,
                51, 192, 57, 83, 90, 104, 49, 143, 52, 119, 33, 44, 148, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 185, 94, 193, 214, 59, 75, 191, 79, 29, 162, 155, 89, 5, 9, 66, 178,
                252, 248, 74, 149, 114, 0, 82, 33, 139, 125, 37, 115, 240, 145, 3, 230, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 28, 45, 35, 112, 249, 244, 79, 11, 6, 223, 209, 63, 107,
                71, 111, 13, 78, 208, 17, 40, 80, 193, 212, 178, 137, 254, 105, 28, 81, 111, 254,
                228, 216, 42, 88, 37, 0, 1, 113, 18, 32, 7, 51, 122, 191, 60, 43, 3, 167, 75, 242,
                242, 189, 252, 159, 91, 44, 143, 41, 156, 187, 2, 34, 66, 37, 197, 150, 67, 121,
                137, 83, 70, 60, 216, 42, 88, 37, 0, 1, 113, 18, 32, 176, 20, 97, 203, 228, 164,
                173, 213, 20, 74, 112, 200, 247, 84, 230, 46, 207, 236, 194, 172, 90, 14, 54, 26,
                228, 62, 76, 229, 64, 42, 99, 107, 131, 26, 1, 1, 20, 144, 0, 246, 216, 42, 69, 0,
                1, 85, 0, 0,
            ];
            let as_json_raw = serde_json::json!({"kind":2,"slot":16848017,"shredding":[{"entry_end_idx":0,"shred_end_idx":0},{"entry_end_idx":1,"shred_end_idx":1},{"entry_end_idx":2,"shred_end_idx":2},{"entry_end_idx":3,"shred_end_idx":3},{"entry_end_idx":4,"shred_end_idx":4},{"entry_end_idx":5,"shred_end_idx":5},{"entry_end_idx":6,"shred_end_idx":6},{"entry_end_idx":7,"shred_end_idx":7},{"entry_end_idx":8,"shred_end_idx":8},{"entry_end_idx":9,"shred_end_idx":9},{"entry_end_idx":10,"shred_end_idx":10},{"entry_end_idx":11,"shred_end_idx":11},{"entry_end_idx":12,"shred_end_idx":12},{"entry_end_idx":13,"shred_end_idx":13},{"entry_end_idx":14,"shred_end_idx":14},{"entry_end_idx":15,"shred_end_idx":15},{"entry_end_idx":16,"shred_end_idx":16},{"entry_end_idx":17,"shred_end_idx":17},{"entry_end_idx":18,"shred_end_idx":18},{"entry_end_idx":19,"shred_end_idx":19},{"entry_end_idx":20,"shred_end_idx":20},{"entry_end_idx":21,"shred_end_idx":21},{"entry_end_idx":22,"shred_end_idx":22},{"entry_end_idx":23,"shred_end_idx":23},{"entry_end_idx":24,"shred_end_idx":24},{"entry_end_idx":25,"shred_end_idx":25},{"entry_end_idx":26,"shred_end_idx":26},{"entry_end_idx":27,"shred_end_idx":27},{"entry_end_idx":28,"shred_end_idx":28},{"entry_end_idx":29,"shred_end_idx":29},{"entry_end_idx":30,"shred_end_idx":30},{"entry_end_idx":31,"shred_end_idx":31},{"entry_end_idx":32,"shred_end_idx":32},{"entry_end_idx":33,"shred_end_idx":33},{"entry_end_idx":34,"shred_end_idx":34},{"entry_end_idx":35,"shred_end_idx":35},{"entry_end_idx":36,"shred_end_idx":36},{"entry_end_idx":37,"shred_end_idx":37},{"entry_end_idx":38,"shred_end_idx":38},{"entry_end_idx":39,"shred_end_idx":39},{"entry_end_idx":40,"shred_end_idx":40},{"entry_end_idx":41,"shred_end_idx":41},{"entry_end_idx":42,"shred_end_idx":42},{"entry_end_idx":43,"shred_end_idx":-1},{"entry_end_idx":44,"shred_end_idx":43},{"entry_end_idx":45,"shred_end_idx":44},{"entry_end_idx":46,"shred_end_idx":45},{"entry_end_idx":47,"shred_end_idx":46},{"entry_end_idx":48,"shred_end_idx":47},{"entry_end_idx":49,"shred_end_idx":48},{"entry_end_idx":50,"shred_end_idx":49},{"entry_end_idx":51,"shred_end_idx":50},{"entry_end_idx":52,"shred_end_idx":51},{"entry_end_idx":53,"shred_end_idx":52},{"entry_end_idx":54,"shred_end_idx":53},{"entry_end_idx":55,"shred_end_idx":54},{"entry_end_idx":56,"shred_end_idx":55},{"entry_end_idx":57,"shred_end_idx":56},{"entry_end_idx":58,"shred_end_idx":57},{"entry_end_idx":59,"shred_end_idx":58},{"entry_end_idx":60,"shred_end_idx":59},{"entry_end_idx":61,"shred_end_idx":60},{"entry_end_idx":62,"shred_end_idx":61},{"entry_end_idx":63,"shred_end_idx":62},{"entry_end_idx":64,"shred_end_idx":63},{"entry_end_idx":65,"shred_end_idx":64},{"entry_end_idx":66,"shred_end_idx":-1},{"entry_end_idx":67,"shred_end_idx":-1},{"entry_end_idx":68,"shred_end_idx":66},{"entry_end_idx":69,"shred_end_idx":-1},{"entry_end_idx":70,"shred_end_idx":67}],"entries":[{"/":"bafyreidl7tpju3zd2a5th4ve2ejv3q4xbakxdgyjvfllra5tdvhlqubd4e"},{"/":"bafyreiadvmm54kg62ll5mznawgewx3o7wmgkrcxpocu23t3ldtc3gfgdri"},{"/":"bafyreicl4rbfipkjiixih3se5riwce2zuysp23sajhkcbvu7kg6hgpwpt4"},{"/":"bafyreiarqqwftzx5jbxftcqy5vb3eonqwawambtzpkqii47svexaklgthe"},{"/":"bafyreifax7klrfcp4ajgtpj4fldfly335bysat33iqaq4sffrjlsvankei"},{"/":"bafyreigspfspjz6re7ccefzojxpwwcbl5wsyl2yeeu6uvwwa2dquty5asq"},{"/":"bafyreidrgzes3etjvpk554fioplkl7z7ryuslu7rzby4cbyc6t2fkfgm5i"},{"/":"bafyreib5qatrlluc2fb5ly5g4klrbvmcoeseadltstdtt72whxeuupheze"},{"/":"bafyreiasykw4h4bulumgpmbjmbqy3wbqinjrwdsyrxmtoyteajua36yeoy"},{"/":"bafyreiefcnxqz32tj35zvxl3idex7v6ow3d6tsi7xbkfu5cyzxr4z2hpii"},{"/":"bafyreiagtdxatffbprilskklr3ctx2aqepfwc7n4qtylyfaqdao5jtyasi"},{"/":"bafyreicwjdpm4nwgad7a2nzsl5qy4f5yyl2girj7ibd3duhve5yopijmtm"},{"/":"bafyreiguzjhu3gxouakehdf734o4tspn2mh4wjsxswncpb2xdsfthell5u"},{"/":"bafyreibklnu6oimqxljsfxhx7kckuhbuwzizme2jrknddtaxuin7fc766m"},{"/":"bafyreiapnwqvsy6wursvxmzrtap5vgdu62hd2rd3fo3idr6caeui4v7aou"},{"/":"bafyreifhupqpngejoqz4l3o7ninh2ufx7uaec4cg7iw5yulyx6psumnty4"},{"/":"bafyreidgoc3nv2xqlddhb4lv34ooatruzspxmikrmnkj7frny7flohguna"},{"/":"bafyreidbar5cglnftkmcmkszxgummoynnmxkfd3xdmgkvsnvuoa3kj7xsu"},{"/":"bafyreidk5mftju554j5wwwrvqbcu2ozi3zsnsr25zutqhztlfzsewm5pue"},{"/":"bafyreigy7wf5xz2dn72s7b57mttndnaycvuykcrqfnhgedllg4rwyzxdgq"},{"/":"bafyreid7uyk5qsejw6tpb2dtl3z3hnothdogwlmr6rdmxnrt6eyqgscu4q"},{"/":"bafyreiel7wv6jxwn62peja5nj2hvunasvek7y37uto3eqdwflozxvhiqs4"},{"/":"bafyreiaswh25byinrfwxukxwnpfst2r2io36rseypogr4qvm2jeslrhqoy"},{"/":"bafyreifutzenl2obenluppcltt2vpgrx3rezuro5xifoe7d6u7lmpojalq"},{"/":"bafyreicbrateux7gtyfqsqyjm72me6dav6ce2z5kcgg7hm2gh2ikh2a5mm"},{"/":"bafyreigag5ooxszukwnkoeq4fahcszhebbn3xaoqshqibccqyi3vcbzuza"},{"/":"bafyreigw2zwv4zhqkydkf67kilp2borpolq5bembbec4d4x2kbtpl3lwwu"},{"/":"bafyreicv6o34sokrqmgoniwcm25qbkkjduzhb6i2tyxpz37eyhmtwmpajm"},{"/":"bafyreibvfu3bkxrfikmcr4f4q5e4cz6waj3och3xb2ukpva44se34nmyja"},{"/":"bafyreihzhfsknowl2cwtra2zfpcmtur5bcfpyunnnogkbjjrrstcig7ziq"},{"/":"bafyreibzxpampmhyz5yj2pz2piytbqv3boun2clin3c4ik3yvs3gvl4uym"},{"/":"bafyreihzi4ofixp7newrzro5kxng3xwgylwtrkjyg543hkljvsiks4mwsi"},{"/":"bafyreifxfzqlssupa3wnghcbih2h53gh2nx2gur2pf3e4qg5526rdas34a"},{"/":"bafyreifihlwmnowvjvdfltotxeeiheacazfc2uwsgpivc3gewne6tp5yna"},{"/":"bafyreigttrrnm6iux7zf7on2cpqan2ciutz6jous5svol4i6xzvm6ynnsm"},{"/":"bafyreiaebltb4u3sks6hdhy3harlph6eludwz4kdtfwhvcv6ahss4ala2i"},{"/":"bafyreicbcea27uueqr4kilnm23jubvsnipb3qanknhye5wmf5g2jtinada"},{"/":"bafyreif3cuny7iwnbvyoxujqzo5cadwczx6orngwjtxe3twgnhmbfuf5mm"},{"/":"bafyreig26uvnjdaackxxrh5dwlscba7qgdldpjuhpiq32swq6grhacklam"},{"/":"bafyreigzfc3ls7exqmq2qdlignifytrufzwdjdruo4rvzcrrz5nzbn2w3i"},{"/":"bafyreiabnfxt3vl6pzvzjle3sfyfsgxu74rfaulot4rzpntytqdbvvx7dm"},{"/":"bafyreih76dr4o7dzoqj5lquktf7lcgkxb7ixn6rkttank7ttaptvl2rdmi"},{"/":"bafyreig65tujwpxvw54wc3tskhvxzsbh4tlhlxammigihfb2xmg4wcdmv4"},{"/":"bafyreicpsxkfzn5xnwem4vnahhvqja3cik2x5qsutyzltnqmsrvie2ixmy"},{"/":"bafyreicxtqzsoq4bhg2647n4k2oly2gbhnm3jdjopm76i2ireswiuoxaei"},{"/":"bafyreiftpkfhz3l4ofa6tvg6wbhx6tepdwhwvps7shba5yewbapfhrx5cu"},{"/":"bafyreigaalmz4sjhefxv5xrfucs66g5fkclrcadievjnjdps7vcpv74exm"},{"/":"bafyreibpu2ml2k7tivku5y4levj5bna26ylxmg66l6xsxfqn32zi6rvcvi"},{"/":"bafyreigjpj2ddnwkg7jxxij2sfxabkfsnsm5mkfmbjioln5wdachcjhvku"},{"/":"bafyreigxbmsmmrdgcmwrexb2aw6j64xsoaqo7h7utdwc7kwangwlngmdsu"},{"/":"bafyreih2b24ii723fufkktgmaycog2mn5r4zqf6zmagrs4yslhosmd3n3i"},{"/":"bafyreiecnibqahr6mv2fr4kknfgph5q3irnx3if3ckpytvblvxyfgfek3a"},{"/":"bafyreifsdmgu6q343f6am2maxogspg3h32tyemos7scdlmxpl7yutnamxq"},{"/":"bafyreibagx5dnotx2sbwmko7dhqrfgbc5ycbxzyhtmcarfbr7kfc3q55x4"},{"/":"bafyreidjl77mehpsi4h6enjk5ejhanq7t6x46n6ujotv2c4wsur7e2wwfq"},{"/":"bafyreihli2d2o2l5m5jp6uaogowwgohcvm5yqv2tvhjos2xu6ox4efxl3y"},{"/":"bafyreidfis6yhm4hvo65gm2hkyzecxq5zl2nb5nxa4ixnkrgorwmrufirm"},{"/":"bafyreib2orz2lxxatpabntryhpk7hwg5n6c4xamsabpp2v4q2sflvmshr4"},{"/":"bafyreig375lil6qbbahfey5i4ndfqziegcyoecpmwgmtdoqmnrtk25pizy"},{"/":"bafyreie7e5lcnyyzwett6mliy4w5yx2qqnwp6hv24rtxjhyl2tyfiq6ezy"},{"/":"bafyreibxepsbe7npnxkynqq5gaqebdlf5xprufj6r7ff5rnbmxattckx2i"},{"/":"bafyreig227tlmgntuotpioz6yhzyauqsdzlhs7e72coo5kqq6762ezjrhe"},{"/":"bafyreiblzn5nmposahfyn24q2wf6fdwb47bdqmrbsamp4j6h35vxdm27bi"},{"/":"bafyreiddxxnqaqnfgkbjxeo7offmbm5sp7lpo72uqvr6zswrcqiytbtkdq"},{"/":"bafyreibqy63y5hszxvircou2islcczizn4cwo6fmncztvbeqvtawtujlnq"},{"/":"bafyreibp43e6yifnkno3udixg4dcsyexgpsywyrsehbbvlox65ki74p3oa"},{"/":"bafyreig66pi5kdlqriqote3ntlqwix5l3kz3cm6ahfjvu2brr42hoijmsq"},{"/":"bafyreifzl3a5mo2lx5hr3iu3lecqsqvs7t4evflsabjcdc35evz7beid4y"},{"/":"bafyreia4furxb6puj4fqnx6rh5vuo3ynj3ibckcqyhklfcp6neofc3764q"},{"/":"bafyreiahgn5l6pblaotux4xsxx6j6wzmr4uzzoycejbclrmwin4ysu2ghq"},{"/":"bafyreifqcrq4xzfevxkristqzd3vjzroz7wmflc2by3bvzb6jtsuaktdnm"}],"meta":{"parent_slot":16848016,"blocktime":0,"block_height":null},"rewards":{"/":"bafkqaaa"}});

            let block = Block::from_bytes(raw).unwrap();
            let as_json = block.to_json();
            assert_eq!(as_json, as_json_raw);
        }
        {
            let raw = vec![
                134, 2, 26, 1, 1, 20, 139, 152, 125, 130, 0, 0, 130, 1, 1, 130, 2, 2, 130, 3, 3,
                130, 4, 4, 130, 5, 5, 130, 6, 6, 130, 7, 7, 130, 8, 8, 130, 9, 9, 130, 10, 10, 130,
                11, 11, 130, 12, 32, 130, 13, 32, 130, 14, 13, 130, 15, 32, 130, 16, 14, 130, 17,
                32, 130, 18, 32, 130, 19, 15, 130, 20, 32, 130, 21, 32, 130, 22, 17, 130, 23, 32,
                130, 24, 24, 32, 130, 24, 25, 32, 130, 24, 26, 19, 130, 24, 27, 32, 130, 24, 28,
                32, 130, 24, 29, 32, 130, 24, 30, 32, 130, 24, 31, 21, 130, 24, 32, 32, 130, 24,
                33, 22, 130, 24, 34, 23, 130, 24, 35, 24, 24, 130, 24, 36, 24, 25, 130, 24, 37, 24,
                26, 130, 24, 38, 24, 27, 130, 24, 39, 24, 28, 130, 24, 40, 24, 29, 130, 24, 41, 24,
                30, 130, 24, 42, 24, 31, 130, 24, 43, 24, 32, 130, 24, 44, 24, 33, 130, 24, 45, 24,
                34, 130, 24, 46, 24, 35, 130, 24, 47, 24, 36, 130, 24, 48, 24, 37, 130, 24, 49, 24,
                38, 130, 24, 50, 24, 39, 130, 24, 51, 24, 40, 130, 24, 52, 24, 41, 130, 24, 53, 24,
                42, 130, 24, 54, 24, 43, 130, 24, 55, 24, 44, 130, 24, 56, 24, 45, 130, 24, 57, 24,
                46, 130, 24, 58, 24, 47, 130, 24, 59, 24, 48, 130, 24, 60, 24, 49, 130, 24, 61, 24,
                50, 130, 24, 62, 24, 51, 130, 24, 63, 24, 52, 130, 24, 64, 24, 53, 130, 24, 65, 24,
                54, 130, 24, 66, 24, 55, 130, 24, 67, 24, 56, 130, 24, 68, 24, 57, 130, 24, 69, 24,
                58, 130, 24, 70, 24, 59, 130, 24, 71, 24, 60, 130, 24, 72, 24, 61, 130, 24, 73, 24,
                62, 130, 24, 74, 24, 63, 130, 24, 75, 24, 64, 130, 24, 76, 32, 130, 24, 77, 32,
                130, 24, 78, 24, 65, 130, 24, 79, 32, 130, 24, 80, 32, 130, 24, 81, 32, 130, 24,
                82, 24, 67, 130, 24, 83, 32, 130, 24, 84, 24, 68, 130, 24, 85, 24, 69, 130, 24, 86,
                32, 130, 24, 87, 32, 130, 24, 88, 32, 130, 24, 89, 32, 130, 24, 90, 32, 130, 24,
                91, 32, 130, 24, 92, 32, 130, 24, 93, 24, 72, 130, 24, 94, 32, 130, 24, 95, 32,
                130, 24, 96, 32, 130, 24, 97, 32, 130, 24, 98, 32, 130, 24, 99, 32, 130, 24, 100,
                32, 130, 24, 101, 24, 75, 130, 24, 102, 32, 130, 24, 103, 32, 130, 24, 104, 32,
                130, 24, 105, 32, 130, 24, 106, 32, 130, 24, 107, 32, 130, 24, 108, 32, 130, 24,
                109, 24, 78, 130, 24, 110, 32, 130, 24, 111, 32, 130, 24, 112, 32, 130, 24, 113,
                32, 130, 24, 114, 32, 130, 24, 115, 32, 130, 24, 116, 32, 130, 24, 117, 24, 81,
                130, 24, 118, 32, 130, 24, 119, 24, 82, 130, 24, 120, 24, 83, 130, 24, 121, 24, 84,
                130, 24, 122, 24, 85, 130, 24, 123, 24, 86, 130, 24, 124, 24, 87, 152, 125, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 168, 90, 96, 28, 166, 12, 75, 77, 65, 77, 199, 68,
                24, 166, 121, 198, 97, 190, 248, 223, 175, 135, 251, 2, 225, 126, 194, 106, 3, 194,
                190, 107, 216, 42, 88, 37, 0, 1, 113, 18, 32, 125, 42, 117, 221, 162, 250, 9, 249,
                34, 127, 175, 51, 16, 136, 129, 10, 81, 243, 209, 164, 157, 36, 252, 133, 254, 189,
                200, 157, 74, 144, 135, 156, 216, 42, 88, 37, 0, 1, 113, 18, 32, 172, 3, 110, 57,
                14, 244, 207, 13, 248, 56, 72, 218, 30, 215, 228, 119, 140, 32, 237, 40, 26, 93,
                19, 122, 30, 245, 154, 57, 50, 190, 208, 144, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                212, 226, 197, 124, 122, 31, 148, 10, 237, 142, 179, 52, 165, 130, 134, 117, 249,
                2, 84, 63, 177, 69, 228, 105, 10, 93, 186, 174, 101, 56, 227, 131, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 174, 72, 188, 5, 89, 161, 3, 57, 11, 29, 166, 209, 20, 220, 6,
                198, 61, 45, 224, 136, 228, 40, 158, 240, 255, 227, 178, 185, 13, 223, 219, 229,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 18, 36, 88, 186, 233, 215, 61, 131, 6, 198, 83,
                190, 174, 92, 138, 246, 33, 174, 33, 132, 58, 239, 213, 59, 92, 55, 126, 177, 225,
                197, 116, 65, 216, 42, 88, 37, 0, 1, 113, 18, 32, 188, 209, 113, 209, 17, 115, 153,
                142, 38, 83, 190, 72, 174, 184, 75, 55, 34, 8, 220, 18, 28, 111, 122, 221, 24, 43,
                103, 66, 2, 45, 137, 158, 216, 42, 88, 37, 0, 1, 113, 18, 32, 209, 35, 105, 206,
                42, 65, 3, 209, 235, 166, 179, 101, 43, 206, 4, 219, 143, 160, 226, 100, 163, 153,
                61, 43, 15, 92, 122, 126, 23, 84, 62, 78, 216, 42, 88, 37, 0, 1, 113, 18, 32, 73,
                80, 130, 98, 46, 40, 36, 102, 108, 125, 215, 170, 233, 5, 227, 128, 201, 250, 195,
                116, 190, 181, 183, 168, 142, 125, 108, 138, 212, 219, 179, 127, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 95, 244, 6, 124, 253, 138, 205, 204, 5, 4, 188, 170, 23, 109,
                36, 174, 191, 81, 230, 252, 68, 107, 121, 32, 108, 81, 207, 187, 239, 178, 146,
                192, 216, 42, 88, 37, 0, 1, 113, 18, 32, 144, 68, 18, 59, 223, 33, 202, 103, 56,
                99, 197, 77, 127, 23, 208, 69, 105, 69, 247, 105, 249, 19, 71, 255, 205, 131, 169,
                104, 28, 99, 197, 178, 216, 42, 88, 37, 0, 1, 113, 18, 32, 152, 239, 78, 106, 187,
                210, 165, 195, 54, 1, 228, 230, 251, 204, 159, 116, 158, 184, 211, 122, 161, 24,
                178, 208, 136, 0, 7, 188, 35, 196, 164, 41, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                161, 232, 17, 191, 192, 200, 170, 174, 78, 37, 71, 188, 159, 252, 95, 233, 31, 119,
                167, 237, 60, 110, 19, 165, 223, 2, 95, 180, 17, 138, 5, 9, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 172, 77, 75, 83, 197, 75, 136, 132, 88, 38, 191, 220, 13, 243, 171,
                33, 203, 174, 183, 234, 235, 15, 226, 31, 91, 214, 49, 149, 101, 106, 223, 132,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 220, 44, 4, 21, 175, 68, 11, 104, 229, 203, 6,
                198, 197, 166, 48, 241, 213, 214, 67, 19, 13, 200, 52, 38, 149, 136, 170, 20, 156,
                180, 187, 28, 216, 42, 88, 37, 0, 1, 113, 18, 32, 227, 197, 176, 153, 24, 85, 43,
                195, 20, 109, 101, 168, 63, 158, 184, 53, 84, 20, 153, 244, 114, 31, 71, 227, 183,
                230, 202, 217, 28, 18, 120, 211, 216, 42, 88, 37, 0, 1, 113, 18, 32, 36, 18, 143,
                109, 77, 102, 226, 161, 183, 98, 218, 62, 67, 33, 23, 185, 135, 28, 1, 54, 226,
                148, 96, 173, 27, 202, 120, 166, 229, 251, 57, 85, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 43, 208, 181, 155, 225, 254, 67, 158, 231, 193, 43, 21, 104, 63, 96, 113, 82,
                64, 228, 193, 13, 177, 110, 242, 42, 69, 110, 58, 80, 124, 70, 188, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 238, 86, 7, 23, 233, 34, 22, 192, 134, 57, 90, 53, 37, 28,
                119, 66, 134, 201, 184, 67, 46, 224, 119, 0, 111, 92, 208, 76, 38, 129, 105, 208,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 180, 164, 200, 211, 45, 99, 81, 41, 50, 151,
                182, 86, 45, 11, 116, 173, 118, 140, 240, 138, 7, 75, 160, 174, 38, 234, 36, 89,
                88, 228, 14, 100, 216, 42, 88, 37, 0, 1, 113, 18, 32, 83, 25, 110, 20, 220, 82,
                135, 201, 242, 15, 10, 184, 78, 67, 100, 199, 126, 24, 200, 8, 7, 176, 102, 194,
                139, 196, 36, 101, 207, 213, 128, 57, 216, 42, 88, 37, 0, 1, 113, 18, 32, 136, 69,
                192, 116, 72, 119, 45, 43, 244, 166, 99, 39, 52, 141, 32, 64, 128, 202, 66, 115,
                94, 67, 58, 55, 64, 155, 218, 45, 108, 87, 190, 38, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 148, 25, 210, 153, 130, 154, 193, 89, 68, 23, 108, 235, 137, 147, 146, 236, 70,
                61, 172, 75, 104, 128, 48, 254, 166, 6, 168, 145, 232, 209, 214, 235, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 255, 149, 191, 225, 110, 170, 130, 253, 219, 105, 255, 233,
                44, 145, 198, 55, 39, 199, 161, 161, 39, 62, 76, 110, 174, 37, 85, 131, 120, 182,
                25, 16, 216, 42, 88, 37, 0, 1, 113, 18, 32, 160, 84, 188, 48, 49, 134, 5, 95, 225,
                169, 28, 123, 151, 110, 91, 171, 92, 96, 189, 19, 17, 146, 70, 37, 55, 147, 206,
                207, 194, 251, 44, 116, 216, 42, 88, 37, 0, 1, 113, 18, 32, 82, 154, 188, 118, 97,
                29, 80, 186, 130, 123, 28, 179, 109, 139, 80, 216, 105, 88, 185, 49, 208, 253, 93,
                223, 50, 211, 14, 224, 10, 37, 35, 50, 216, 42, 88, 37, 0, 1, 113, 18, 32, 34, 226,
                73, 91, 0, 223, 23, 133, 161, 73, 174, 182, 180, 250, 98, 143, 22, 236, 251, 138,
                179, 190, 179, 174, 254, 0, 52, 167, 66, 63, 217, 102, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 18, 80, 27, 167, 101, 249, 102, 98, 37, 94, 39, 196, 192, 82, 31, 153, 106,
                252, 152, 58, 205, 199, 21, 89, 11, 14, 188, 131, 205, 38, 227, 84, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 85, 29, 137, 255, 201, 192, 204, 45, 197, 4, 222, 37, 222,
                252, 129, 208, 56, 180, 25, 125, 100, 44, 72, 116, 5, 66, 130, 207, 9, 133, 202,
                95, 216, 42, 88, 37, 0, 1, 113, 18, 32, 44, 221, 110, 155, 75, 238, 244, 26, 245,
                46, 200, 133, 61, 76, 47, 68, 55, 179, 87, 197, 174, 157, 7, 251, 52, 28, 156, 12,
                85, 48, 237, 86, 216, 42, 88, 37, 0, 1, 113, 18, 32, 203, 18, 46, 47, 176, 155,
                166, 251, 219, 10, 63, 189, 73, 63, 244, 70, 182, 150, 175, 218, 116, 70, 180, 181,
                3, 133, 192, 103, 195, 61, 52, 167, 216, 42, 88, 37, 0, 1, 113, 18, 32, 62, 40,
                115, 20, 69, 239, 225, 166, 28, 233, 169, 36, 219, 196, 29, 193, 33, 15, 84, 56,
                19, 72, 220, 12, 123, 194, 181, 115, 202, 27, 213, 149, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 219, 174, 215, 54, 91, 186, 180, 147, 73, 22, 159, 198, 232, 78, 148, 146,
                29, 72, 94, 247, 223, 138, 109, 44, 123, 228, 93, 114, 127, 216, 237, 27, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 225, 227, 50, 74, 252, 37, 43, 47, 221, 23, 10, 66, 239,
                120, 209, 74, 66, 240, 158, 187, 69, 181, 193, 213, 141, 144, 113, 95, 206, 236,
                12, 14, 216, 42, 88, 37, 0, 1, 113, 18, 32, 67, 141, 206, 50, 124, 160, 195, 109,
                164, 239, 59, 104, 55, 120, 39, 122, 125, 146, 203, 243, 106, 183, 14, 23, 6, 150,
                55, 99, 16, 92, 235, 123, 216, 42, 88, 37, 0, 1, 113, 18, 32, 108, 189, 181, 6,
                219, 188, 161, 228, 217, 94, 250, 181, 226, 105, 79, 75, 209, 96, 196, 107, 176,
                88, 17, 50, 166, 169, 135, 82, 61, 176, 205, 102, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 67, 69, 58, 110, 31, 121, 190, 164, 217, 218, 84, 31, 250, 189, 181, 212, 96,
                67, 22, 218, 27, 139, 157, 253, 239, 241, 193, 27, 152, 71, 129, 207, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 34, 56, 202, 95, 207, 140, 114, 48, 48, 233, 140, 183, 81,
                49, 144, 203, 123, 222, 126, 138, 176, 73, 28, 181, 119, 103, 245, 208, 145, 204,
                244, 105, 216, 42, 88, 37, 0, 1, 113, 18, 32, 86, 5, 60, 38, 196, 140, 240, 41,
                249, 56, 80, 106, 39, 147, 17, 18, 200, 98, 103, 186, 155, 171, 87, 52, 112, 22,
                120, 229, 141, 111, 105, 222, 216, 42, 88, 37, 0, 1, 113, 18, 32, 121, 196, 231,
                226, 165, 93, 61, 28, 179, 195, 130, 228, 95, 147, 184, 94, 70, 20, 79, 81, 214,
                94, 188, 122, 53, 21, 199, 52, 221, 161, 17, 41, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 146, 61, 165, 230, 166, 199, 188, 185, 120, 159, 185, 219, 201, 79, 35, 121,
                38, 203, 36, 4, 241, 26, 7, 47, 206, 160, 68, 172, 209, 218, 56, 239, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 244, 198, 40, 185, 188, 195, 231, 229, 113, 79, 154, 215,
                211, 238, 176, 72, 212, 107, 253, 29, 104, 155, 117, 127, 199, 10, 106, 49, 201,
                136, 160, 13, 216, 42, 88, 37, 0, 1, 113, 18, 32, 158, 201, 72, 217, 23, 209, 84,
                74, 103, 91, 120, 142, 43, 81, 33, 210, 159, 67, 18, 186, 197, 105, 10, 47, 37,
                215, 100, 86, 235, 138, 26, 233, 216, 42, 88, 37, 0, 1, 113, 18, 32, 179, 65, 175,
                132, 109, 145, 111, 112, 8, 238, 207, 142, 73, 230, 119, 214, 92, 110, 82, 13, 44,
                236, 104, 102, 99, 64, 8, 202, 64, 207, 98, 135, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 121, 226, 92, 148, 183, 218, 73, 68, 3, 29, 75, 167, 14, 189, 124, 43, 18, 193,
                164, 160, 27, 191, 77, 0, 0, 9, 28, 162, 226, 18, 227, 194, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 25, 41, 183, 237, 185, 81, 32, 141, 83, 51, 130, 170, 38, 221, 160,
                104, 205, 200, 106, 201, 47, 19, 112, 217, 160, 5, 50, 8, 250, 215, 113, 76, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 69, 167, 112, 152, 12, 33, 164, 233, 151, 193, 116,
                50, 141, 14, 45, 239, 216, 78, 224, 180, 77, 55, 204, 131, 253, 176, 38, 193, 60,
                87, 161, 106, 216, 42, 88, 37, 0, 1, 113, 18, 32, 158, 8, 109, 5, 221, 13, 190, 87,
                159, 228, 58, 109, 26, 80, 92, 0, 249, 34, 18, 232, 60, 63, 97, 212, 97, 9, 87,
                109, 52, 156, 166, 125, 216, 42, 88, 37, 0, 1, 113, 18, 32, 17, 129, 110, 239, 11,
                54, 215, 219, 249, 251, 232, 161, 164, 231, 17, 50, 201, 11, 243, 32, 42, 143, 131,
                8, 92, 15, 245, 218, 233, 106, 161, 57, 216, 42, 88, 37, 0, 1, 113, 18, 32, 22, 7,
                51, 25, 194, 200, 128, 41, 38, 123, 166, 55, 48, 247, 231, 194, 149, 241, 147, 135,
                156, 143, 166, 48, 1, 25, 245, 227, 101, 251, 207, 1, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 149, 18, 208, 177, 153, 113, 140, 219, 55, 182, 165, 8, 121, 35, 216, 227,
                112, 135, 159, 142, 185, 214, 233, 204, 213, 228, 235, 140, 155, 53, 71, 141, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 32, 37, 37, 127, 175, 217, 101, 27, 143, 47, 192,
                40, 223, 232, 131, 140, 138, 240, 61, 150, 210, 192, 74, 116, 68, 235, 68, 58, 61,
                22, 153, 174, 216, 42, 88, 37, 0, 1, 113, 18, 32, 106, 243, 230, 189, 146, 33, 240,
                183, 33, 151, 146, 225, 234, 59, 184, 37, 32, 209, 168, 80, 212, 182, 104, 83, 75,
                0, 202, 172, 114, 169, 174, 57, 216, 42, 88, 37, 0, 1, 113, 18, 32, 164, 59, 41,
                32, 62, 218, 182, 154, 173, 92, 4, 18, 84, 68, 79, 102, 143, 96, 53, 171, 148, 45,
                217, 233, 167, 227, 128, 55, 79, 186, 52, 41, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                167, 163, 186, 13, 81, 49, 225, 43, 130, 45, 157, 31, 233, 5, 173, 246, 50, 128,
                242, 77, 25, 165, 47, 128, 50, 140, 238, 159, 29, 229, 125, 14, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 56, 161, 22, 122, 6, 194, 166, 63, 87, 185, 79, 66, 124, 112, 5,
                83, 48, 18, 108, 136, 168, 127, 182, 39, 105, 41, 66, 72, 147, 120, 245, 50, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 16, 79, 116, 45, 110, 208, 114, 46, 87, 232, 27,
                172, 25, 225, 202, 112, 13, 223, 129, 57, 229, 254, 185, 56, 242, 75, 177, 104, 90,
                170, 20, 163, 216, 42, 88, 37, 0, 1, 113, 18, 32, 118, 45, 246, 62, 251, 0, 98, 0,
                94, 74, 82, 157, 146, 50, 28, 246, 94, 27, 98, 97, 172, 179, 200, 122, 114, 0, 239,
                227, 0, 218, 85, 140, 216, 42, 88, 37, 0, 1, 113, 18, 32, 20, 163, 54, 50, 67, 87,
                116, 78, 116, 99, 138, 232, 235, 120, 250, 59, 87, 49, 47, 92, 0, 53, 70, 117, 106,
                9, 58, 94, 162, 46, 73, 207, 216, 42, 88, 37, 0, 1, 113, 18, 32, 157, 222, 244, 8,
                201, 203, 227, 28, 200, 52, 110, 87, 59, 111, 73, 183, 219, 136, 231, 122, 217, 98,
                248, 17, 55, 92, 13, 193, 96, 33, 78, 221, 216, 42, 88, 37, 0, 1, 113, 18, 32, 77,
                105, 127, 145, 43, 92, 51, 62, 146, 49, 240, 60, 247, 70, 137, 32, 234, 4, 133,
                148, 240, 68, 151, 216, 214, 95, 82, 226, 166, 42, 18, 227, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 3, 6, 19, 112, 130, 51, 131, 110, 151, 115, 239, 231, 12, 99, 234,
                215, 186, 144, 231, 113, 209, 228, 83, 82, 110, 85, 67, 20, 161, 199, 125, 255,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 234, 90, 149, 221, 100, 13, 34, 67, 217, 72,
                43, 220, 251, 233, 160, 110, 239, 156, 222, 98, 114, 168, 174, 64, 3, 133, 100,
                223, 8, 185, 130, 121, 216, 42, 88, 37, 0, 1, 113, 18, 32, 147, 247, 191, 37, 195,
                162, 135, 198, 15, 138, 36, 248, 95, 155, 202, 58, 52, 173, 213, 86, 150, 217, 12,
                96, 114, 223, 6, 238, 192, 56, 153, 91, 216, 42, 88, 37, 0, 1, 113, 18, 32, 64,
                179, 68, 155, 124, 57, 41, 88, 10, 121, 236, 64, 109, 126, 220, 167, 201, 19, 209,
                65, 126, 133, 58, 34, 54, 162, 143, 249, 94, 59, 6, 132, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 192, 228, 216, 176, 153, 81, 131, 73, 249, 62, 87, 139, 161, 125, 121,
                70, 172, 186, 109, 180, 10, 218, 131, 165, 47, 35, 9, 3, 232, 96, 195, 21, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 187, 24, 211, 28, 45, 82, 174, 51, 23, 99, 190, 72, 95,
                144, 79, 142, 170, 85, 135, 157, 98, 205, 215, 179, 165, 218, 12, 76, 185, 152,
                143, 59, 216, 42, 88, 37, 0, 1, 113, 18, 32, 138, 40, 4, 210, 161, 208, 178, 75,
                39, 87, 72, 43, 118, 77, 233, 63, 108, 148, 217, 240, 131, 66, 75, 109, 55, 191,
                92, 46, 113, 94, 140, 206, 216, 42, 88, 37, 0, 1, 113, 18, 32, 84, 68, 202, 54,
                159, 123, 21, 248, 185, 203, 44, 198, 125, 115, 158, 231, 69, 141, 236, 89, 134,
                15, 108, 147, 161, 179, 73, 79, 84, 78, 85, 54, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                143, 158, 60, 215, 167, 168, 26, 7, 151, 183, 166, 160, 169, 75, 170, 3, 43, 95,
                159, 144, 60, 181, 130, 5, 191, 131, 160, 207, 250, 134, 133, 174, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 58, 75, 153, 197, 110, 178, 215, 85, 241, 20, 0, 78, 202, 104,
                198, 56, 129, 150, 116, 26, 162, 157, 160, 197, 238, 139, 208, 30, 174, 200, 149,
                2, 216, 42, 88, 37, 0, 1, 113, 18, 32, 49, 125, 210, 205, 212, 215, 212, 254, 117,
                212, 89, 248, 200, 91, 208, 180, 148, 56, 146, 167, 64, 248, 96, 5, 246, 65, 0,
                237, 112, 131, 246, 72, 216, 42, 88, 37, 0, 1, 113, 18, 32, 42, 180, 254, 227, 82,
                246, 192, 218, 14, 11, 104, 46, 228, 77, 181, 159, 231, 17, 41, 82, 61, 177, 59,
                103, 71, 112, 63, 253, 10, 141, 226, 253, 216, 42, 88, 37, 0, 1, 113, 18, 32, 152,
                250, 19, 126, 190, 179, 125, 167, 229, 78, 133, 212, 165, 135, 131, 158, 105, 62,
                200, 190, 162, 32, 9, 176, 22, 210, 5, 143, 152, 142, 53, 47, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 8, 2, 28, 195, 152, 52, 109, 15, 35, 195, 15, 174, 140, 65, 100,
                16, 98, 224, 30, 205, 234, 90, 148, 59, 133, 227, 32, 38, 72, 50, 127, 58, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 227, 234, 225, 58, 191, 165, 213, 151, 98, 28, 229, 152,
                94, 236, 177, 200, 184, 210, 147, 249, 149, 123, 241, 222, 104, 213, 210, 217, 92,
                101, 124, 68, 216, 42, 88, 37, 0, 1, 113, 18, 32, 46, 183, 146, 198, 100, 14, 12,
                140, 41, 22, 251, 134, 171, 1, 215, 187, 7, 176, 119, 203, 69, 94, 56, 129, 37,
                178, 170, 13, 71, 87, 198, 59, 216, 42, 88, 37, 0, 1, 113, 18, 32, 60, 243, 131,
                11, 63, 60, 116, 240, 39, 123, 215, 186, 187, 13, 39, 94, 191, 193, 180, 54, 115,
                195, 13, 69, 107, 188, 146, 196, 213, 242, 80, 28, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 232, 63, 21, 142, 8, 222, 34, 47, 194, 255, 34, 26, 172, 219, 184, 215, 32, 50,
                237, 66, 225, 112, 48, 27, 215, 255, 210, 175, 54, 9, 219, 224, 216, 42, 88, 37, 0,
                1, 113, 18, 32, 223, 104, 84, 39, 186, 176, 255, 11, 147, 169, 187, 78, 98, 204,
                99, 131, 195, 149, 31, 109, 35, 126, 153, 34, 123, 78, 42, 223, 117, 193, 107, 60,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 134, 91, 133, 181, 11, 187, 125, 157, 161, 127,
                212, 65, 57, 99, 54, 137, 168, 210, 71, 127, 90, 148, 164, 21, 71, 139, 30, 87, 94,
                35, 195, 249, 216, 42, 88, 37, 0, 1, 113, 18, 32, 229, 225, 53, 41, 226, 86, 143,
                21, 227, 247, 145, 80, 201, 248, 206, 143, 74, 30, 50, 240, 200, 139, 45, 25, 60,
                42, 138, 227, 112, 242, 58, 36, 216, 42, 88, 37, 0, 1, 113, 18, 32, 136, 165, 157,
                187, 17, 165, 47, 202, 220, 224, 12, 77, 105, 194, 65, 143, 109, 117, 65, 243, 115,
                207, 15, 122, 124, 45, 171, 40, 41, 142, 243, 189, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 150, 27, 53, 58, 176, 114, 160, 217, 57, 197, 116, 119, 111, 145, 157, 80, 101,
                249, 234, 202, 58, 16, 100, 204, 179, 41, 200, 189, 179, 118, 209, 59, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 102, 172, 106, 134, 138, 247, 31, 120, 245, 175, 23, 30,
                135, 40, 83, 182, 4, 250, 238, 225, 237, 7, 111, 74, 37, 228, 192, 254, 242, 211,
                42, 64, 216, 42, 88, 37, 0, 1, 113, 18, 32, 13, 148, 211, 28, 164, 87, 44, 130, 22,
                17, 206, 37, 249, 11, 143, 70, 221, 1, 72, 109, 228, 48, 177, 167, 90, 70, 51, 35,
                3, 16, 29, 212, 216, 42, 88, 37, 0, 1, 113, 18, 32, 14, 217, 244, 64, 68, 174, 103,
                237, 62, 117, 181, 207, 165, 162, 106, 172, 16, 49, 87, 4, 222, 207, 225, 54, 48,
                141, 0, 33, 24, 2, 207, 254, 216, 42, 88, 37, 0, 1, 113, 18, 32, 56, 123, 3, 170,
                86, 20, 144, 49, 33, 226, 221, 233, 41, 187, 56, 96, 124, 234, 211, 17, 148, 92,
                19, 55, 155, 155, 107, 156, 192, 47, 21, 68, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                132, 141, 62, 132, 87, 6, 121, 213, 227, 59, 94, 226, 56, 10, 191, 75, 88, 81, 247,
                65, 178, 241, 214, 73, 39, 38, 25, 186, 34, 87, 143, 100, 216, 42, 88, 37, 0, 1,
                113, 18, 32, 152, 30, 253, 122, 6, 186, 225, 121, 115, 132, 108, 93, 130, 159, 152,
                164, 123, 189, 108, 82, 55, 22, 88, 242, 40, 134, 32, 55, 122, 204, 220, 44, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 186, 233, 51, 107, 113, 192, 227, 166, 127, 182, 71,
                218, 70, 73, 154, 120, 251, 217, 245, 21, 115, 173, 115, 197, 112, 187, 177, 244,
                68, 234, 250, 192, 216, 42, 88, 37, 0, 1, 113, 18, 32, 221, 199, 67, 208, 159, 100,
                86, 43, 117, 147, 3, 36, 197, 103, 80, 82, 45, 194, 59, 81, 116, 134, 97, 178, 175,
                84, 205, 207, 169, 254, 178, 39, 216, 42, 88, 37, 0, 1, 113, 18, 32, 124, 56, 198,
                65, 129, 44, 15, 1, 38, 117, 8, 169, 202, 68, 216, 49, 34, 86, 217, 226, 229, 36,
                16, 58, 133, 186, 253, 239, 18, 22, 90, 129, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                68, 127, 58, 68, 110, 203, 74, 222, 96, 68, 94, 8, 199, 14, 129, 190, 232, 220,
                117, 12, 142, 140, 48, 23, 176, 27, 139, 113, 58, 121, 193, 216, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 246, 3, 148, 180, 192, 243, 147, 114, 155, 53, 229, 92, 44, 89,
                204, 30, 105, 232, 82, 155, 205, 181, 67, 232, 58, 195, 89, 255, 24, 66, 67, 25,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 33, 187, 1, 118, 170, 102, 67, 25, 170, 21,
                223, 222, 227, 134, 12, 225, 251, 183, 128, 138, 63, 255, 44, 87, 216, 12, 12, 231,
                160, 193, 197, 204, 216, 42, 88, 37, 0, 1, 113, 18, 32, 79, 116, 36, 143, 190, 133,
                96, 213, 102, 30, 149, 245, 53, 127, 93, 127, 154, 88, 237, 105, 186, 66, 168, 177,
                99, 110, 177, 138, 181, 249, 214, 217, 216, 42, 88, 37, 0, 1, 113, 18, 32, 7, 222,
                146, 167, 127, 138, 38, 233, 202, 97, 127, 92, 30, 232, 233, 61, 114, 75, 255, 144,
                162, 110, 255, 176, 104, 88, 27, 3, 255, 206, 199, 90, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 212, 206, 161, 16, 20, 104, 21, 90, 163, 93, 189, 158, 140, 41, 169, 173,
                232, 83, 204, 36, 122, 2, 22, 188, 213, 202, 139, 7, 255, 198, 16, 221, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 174, 32, 24, 188, 27, 251, 210, 222, 52, 233, 230, 191,
                151, 99, 204, 75, 123, 125, 29, 92, 142, 231, 45, 191, 6, 97, 148, 194, 62, 82, 54,
                40, 216, 42, 88, 37, 0, 1, 113, 18, 32, 192, 127, 166, 32, 146, 74, 168, 232, 196,
                206, 21, 113, 144, 39, 198, 216, 167, 48, 25, 155, 195, 103, 13, 141, 17, 144, 93,
                88, 65, 184, 104, 71, 216, 42, 88, 37, 0, 1, 113, 18, 32, 21, 175, 147, 45, 241,
                68, 226, 219, 8, 47, 105, 46, 173, 120, 32, 121, 76, 128, 124, 141, 37, 5, 159,
                191, 205, 15, 36, 130, 1, 214, 236, 196, 216, 42, 88, 37, 0, 1, 113, 18, 32, 76,
                178, 54, 122, 12, 146, 127, 193, 131, 159, 54, 245, 199, 127, 25, 133, 227, 45,
                204, 223, 235, 57, 193, 53, 196, 109, 7, 205, 197, 114, 184, 73, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 163, 99, 155, 201, 6, 10, 38, 133, 217, 19, 200, 123, 65, 66,
                130, 77, 89, 135, 175, 143, 56, 215, 109, 224, 27, 113, 54, 84, 208, 26, 234, 217,
                216, 42, 88, 37, 0, 1, 113, 18, 32, 66, 71, 98, 66, 158, 215, 32, 109, 57, 220,
                254, 240, 220, 169, 190, 220, 29, 229, 9, 39, 30, 234, 13, 229, 229, 167, 120, 249,
                45, 190, 3, 242, 216, 42, 88, 37, 0, 1, 113, 18, 32, 156, 56, 108, 146, 191, 241,
                208, 106, 85, 157, 43, 108, 16, 9, 114, 131, 131, 87, 112, 216, 211, 222, 213, 111,
                141, 6, 241, 77, 174, 153, 75, 214, 216, 42, 88, 37, 0, 1, 113, 18, 32, 66, 47,
                187, 80, 185, 62, 174, 184, 200, 183, 80, 145, 231, 162, 48, 48, 1, 29, 128, 163,
                206, 19, 226, 140, 113, 213, 73, 181, 60, 69, 51, 39, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 74, 86, 45, 74, 203, 84, 132, 89, 192, 0, 120, 168, 155, 146, 231, 195, 7,
                27, 137, 187, 156, 223, 93, 174, 5, 207, 16, 93, 255, 106, 126, 207, 216, 42, 88,
                37, 0, 1, 113, 18, 32, 122, 205, 66, 216, 149, 177, 163, 180, 43, 200, 59, 207,
                198, 47, 174, 223, 196, 192, 211, 231, 21, 176, 237, 32, 250, 127, 42, 13, 213, 8,
                96, 242, 216, 42, 88, 37, 0, 1, 113, 18, 32, 188, 134, 245, 41, 252, 195, 47, 111,
                4, 111, 157, 41, 221, 219, 97, 136, 234, 179, 153, 21, 203, 110, 4, 117, 121, 23,
                151, 188, 227, 125, 180, 248, 216, 42, 88, 37, 0, 1, 113, 18, 32, 12, 146, 67, 135,
                102, 70, 167, 177, 140, 144, 188, 236, 4, 86, 168, 220, 70, 214, 23, 182, 1, 85,
                211, 89, 80, 127, 168, 157, 227, 60, 184, 131, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                174, 175, 49, 13, 119, 89, 229, 123, 38, 41, 151, 16, 124, 179, 192, 211, 228, 246,
                199, 91, 70, 139, 89, 111, 246, 216, 55, 244, 237, 149, 190, 48, 216, 42, 88, 37,
                0, 1, 113, 18, 32, 186, 189, 5, 216, 17, 68, 133, 182, 94, 32, 98, 38, 64, 110, 19,
                90, 20, 21, 89, 171, 159, 161, 94, 139, 107, 228, 229, 85, 244, 234, 137, 223, 216,
                42, 88, 37, 0, 1, 113, 18, 32, 234, 11, 80, 27, 220, 139, 21, 85, 216, 146, 153,
                105, 23, 125, 19, 105, 109, 189, 14, 239, 98, 139, 14, 1, 35, 116, 73, 105, 146,
                236, 55, 174, 216, 42, 88, 37, 0, 1, 113, 18, 32, 60, 120, 195, 48, 131, 215, 164,
                233, 109, 232, 70, 247, 245, 62, 178, 239, 36, 202, 122, 135, 49, 87, 159, 44, 91,
                239, 140, 242, 112, 189, 190, 136, 216, 42, 88, 37, 0, 1, 113, 18, 32, 69, 130,
                145, 215, 130, 174, 159, 112, 52, 16, 79, 198, 56, 51, 35, 28, 219, 24, 13, 56,
                139, 175, 76, 4, 55, 227, 66, 131, 34, 40, 185, 149, 216, 42, 88, 37, 0, 1, 113,
                18, 32, 95, 135, 47, 121, 148, 35, 139, 56, 189, 124, 186, 111, 253, 200, 123, 22,
                50, 45, 68, 62, 216, 165, 101, 184, 187, 191, 105, 181, 140, 144, 162, 12, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 86, 227, 155, 184, 112, 128, 195, 17, 2, 10, 23, 227,
                151, 79, 227, 26, 24, 161, 8, 223, 89, 218, 88, 212, 197, 115, 34, 19, 168, 49,
                115, 79, 216, 42, 88, 37, 0, 1, 113, 18, 32, 252, 160, 116, 76, 247, 61, 13, 253,
                191, 229, 124, 228, 204, 225, 195, 249, 233, 180, 196, 221, 169, 80, 126, 73, 125,
                69, 253, 2, 196, 116, 130, 171, 216, 42, 88, 37, 0, 1, 113, 18, 32, 228, 114, 113,
                224, 174, 3, 239, 146, 122, 148, 107, 70, 112, 84, 206, 67, 225, 96, 36, 123, 76,
                55, 254, 110, 161, 142, 121, 81, 109, 150, 118, 38, 216, 42, 88, 37, 0, 1, 113, 18,
                32, 142, 164, 68, 118, 167, 198, 158, 240, 108, 15, 128, 102, 25, 141, 238, 198,
                12, 190, 55, 253, 248, 41, 221, 45, 10, 238, 69, 4, 157, 165, 143, 177, 216, 42,
                88, 37, 0, 1, 113, 18, 32, 160, 48, 131, 18, 116, 148, 175, 200, 174, 212, 75, 76,
                183, 61, 133, 230, 54, 53, 156, 239, 14, 75, 2, 158, 135, 109, 79, 189, 146, 130,
                108, 58, 216, 42, 88, 37, 0, 1, 113, 18, 32, 141, 13, 129, 217, 224, 151, 1, 201,
                102, 14, 225, 150, 153, 49, 169, 93, 114, 136, 216, 233, 195, 198, 64, 48, 17, 226,
                24, 194, 88, 96, 125, 165, 216, 42, 88, 37, 0, 1, 113, 18, 32, 190, 111, 54, 166,
                223, 172, 72, 223, 225, 74, 254, 79, 132, 93, 10, 167, 116, 89, 135, 210, 201, 247,
                87, 150, 147, 185, 83, 147, 183, 18, 214, 66, 216, 42, 88, 37, 0, 1, 113, 18, 32,
                210, 34, 150, 170, 209, 5, 159, 27, 53, 142, 166, 13, 180, 212, 192, 131, 1, 123,
                24, 191, 210, 102, 115, 162, 84, 84, 98, 89, 181, 40, 181, 220, 131, 26, 1, 1, 20,
                138, 0, 246, 216, 42, 69, 0, 1, 85, 0, 0,
            ];
            let as_json_raw = serde_json::json!({"kind":2,"slot":16848011,"shredding":[{"entry_end_idx":0,"shred_end_idx":0},{"entry_end_idx":1,"shred_end_idx":1},{"entry_end_idx":2,"shred_end_idx":2},{"entry_end_idx":3,"shred_end_idx":3},{"entry_end_idx":4,"shred_end_idx":4},{"entry_end_idx":5,"shred_end_idx":5},{"entry_end_idx":6,"shred_end_idx":6},{"entry_end_idx":7,"shred_end_idx":7},{"entry_end_idx":8,"shred_end_idx":8},{"entry_end_idx":9,"shred_end_idx":9},{"entry_end_idx":10,"shred_end_idx":10},{"entry_end_idx":11,"shred_end_idx":11},{"entry_end_idx":12,"shred_end_idx":-1},{"entry_end_idx":13,"shred_end_idx":-1},{"entry_end_idx":14,"shred_end_idx":13},{"entry_end_idx":15,"shred_end_idx":-1},{"entry_end_idx":16,"shred_end_idx":14},{"entry_end_idx":17,"shred_end_idx":-1},{"entry_end_idx":18,"shred_end_idx":-1},{"entry_end_idx":19,"shred_end_idx":15},{"entry_end_idx":20,"shred_end_idx":-1},{"entry_end_idx":21,"shred_end_idx":-1},{"entry_end_idx":22,"shred_end_idx":17},{"entry_end_idx":23,"shred_end_idx":-1},{"entry_end_idx":24,"shred_end_idx":-1},{"entry_end_idx":25,"shred_end_idx":-1},{"entry_end_idx":26,"shred_end_idx":19},{"entry_end_idx":27,"shred_end_idx":-1},{"entry_end_idx":28,"shred_end_idx":-1},{"entry_end_idx":29,"shred_end_idx":-1},{"entry_end_idx":30,"shred_end_idx":-1},{"entry_end_idx":31,"shred_end_idx":21},{"entry_end_idx":32,"shred_end_idx":-1},{"entry_end_idx":33,"shred_end_idx":22},{"entry_end_idx":34,"shred_end_idx":23},{"entry_end_idx":35,"shred_end_idx":24},{"entry_end_idx":36,"shred_end_idx":25},{"entry_end_idx":37,"shred_end_idx":26},{"entry_end_idx":38,"shred_end_idx":27},{"entry_end_idx":39,"shred_end_idx":28},{"entry_end_idx":40,"shred_end_idx":29},{"entry_end_idx":41,"shred_end_idx":30},{"entry_end_idx":42,"shred_end_idx":31},{"entry_end_idx":43,"shred_end_idx":32},{"entry_end_idx":44,"shred_end_idx":33},{"entry_end_idx":45,"shred_end_idx":34},{"entry_end_idx":46,"shred_end_idx":35},{"entry_end_idx":47,"shred_end_idx":36},{"entry_end_idx":48,"shred_end_idx":37},{"entry_end_idx":49,"shred_end_idx":38},{"entry_end_idx":50,"shred_end_idx":39},{"entry_end_idx":51,"shred_end_idx":40},{"entry_end_idx":52,"shred_end_idx":41},{"entry_end_idx":53,"shred_end_idx":42},{"entry_end_idx":54,"shred_end_idx":43},{"entry_end_idx":55,"shred_end_idx":44},{"entry_end_idx":56,"shred_end_idx":45},{"entry_end_idx":57,"shred_end_idx":46},{"entry_end_idx":58,"shred_end_idx":47},{"entry_end_idx":59,"shred_end_idx":48},{"entry_end_idx":60,"shred_end_idx":49},{"entry_end_idx":61,"shred_end_idx":50},{"entry_end_idx":62,"shred_end_idx":51},{"entry_end_idx":63,"shred_end_idx":52},{"entry_end_idx":64,"shred_end_idx":53},{"entry_end_idx":65,"shred_end_idx":54},{"entry_end_idx":66,"shred_end_idx":55},{"entry_end_idx":67,"shred_end_idx":56},{"entry_end_idx":68,"shred_end_idx":57},{"entry_end_idx":69,"shred_end_idx":58},{"entry_end_idx":70,"shred_end_idx":59},{"entry_end_idx":71,"shred_end_idx":60},{"entry_end_idx":72,"shred_end_idx":61},{"entry_end_idx":73,"shred_end_idx":62},{"entry_end_idx":74,"shred_end_idx":63},{"entry_end_idx":75,"shred_end_idx":64},{"entry_end_idx":76,"shred_end_idx":-1},{"entry_end_idx":77,"shred_end_idx":-1},{"entry_end_idx":78,"shred_end_idx":65},{"entry_end_idx":79,"shred_end_idx":-1},{"entry_end_idx":80,"shred_end_idx":-1},{"entry_end_idx":81,"shred_end_idx":-1},{"entry_end_idx":82,"shred_end_idx":67},{"entry_end_idx":83,"shred_end_idx":-1},{"entry_end_idx":84,"shred_end_idx":68},{"entry_end_idx":85,"shred_end_idx":69},{"entry_end_idx":86,"shred_end_idx":-1},{"entry_end_idx":87,"shred_end_idx":-1},{"entry_end_idx":88,"shred_end_idx":-1},{"entry_end_idx":89,"shred_end_idx":-1},{"entry_end_idx":90,"shred_end_idx":-1},{"entry_end_idx":91,"shred_end_idx":-1},{"entry_end_idx":92,"shred_end_idx":-1},{"entry_end_idx":93,"shred_end_idx":72},{"entry_end_idx":94,"shred_end_idx":-1},{"entry_end_idx":95,"shred_end_idx":-1},{"entry_end_idx":96,"shred_end_idx":-1},{"entry_end_idx":97,"shred_end_idx":-1},{"entry_end_idx":98,"shred_end_idx":-1},{"entry_end_idx":99,"shred_end_idx":-1},{"entry_end_idx":100,"shred_end_idx":-1},{"entry_end_idx":101,"shred_end_idx":75},{"entry_end_idx":102,"shred_end_idx":-1},{"entry_end_idx":103,"shred_end_idx":-1},{"entry_end_idx":104,"shred_end_idx":-1},{"entry_end_idx":105,"shred_end_idx":-1},{"entry_end_idx":106,"shred_end_idx":-1},{"entry_end_idx":107,"shred_end_idx":-1},{"entry_end_idx":108,"shred_end_idx":-1},{"entry_end_idx":109,"shred_end_idx":78},{"entry_end_idx":110,"shred_end_idx":-1},{"entry_end_idx":111,"shred_end_idx":-1},{"entry_end_idx":112,"shred_end_idx":-1},{"entry_end_idx":113,"shred_end_idx":-1},{"entry_end_idx":114,"shred_end_idx":-1},{"entry_end_idx":115,"shred_end_idx":-1},{"entry_end_idx":116,"shred_end_idx":-1},{"entry_end_idx":117,"shred_end_idx":81},{"entry_end_idx":118,"shred_end_idx":-1},{"entry_end_idx":119,"shred_end_idx":82},{"entry_end_idx":120,"shred_end_idx":83},{"entry_end_idx":121,"shred_end_idx":84},{"entry_end_idx":122,"shred_end_idx":85},{"entry_end_idx":123,"shred_end_idx":86},{"entry_end_idx":124,"shred_end_idx":87}],"entries":[{"/":"bafyreifiljqbzjqmjnguctohiqmkm6ogmg7prx5pq75qfyl6yjvahqv6nm"},{"/":"bafyreid5fj253ix2bh4se75pgmiiraikkhz5dje5et6il7v5zcouveehtq"},{"/":"bafyreifmanxdsdxuz4g7qoci3ipnpzdxrqqo2ka2lujxuhxvti4tfpwqsa"},{"/":"bafyreigu4lcxy6q7sqfo3dvtgssyfbtv7ebfip5rixsgscs5xkxgkohdqm"},{"/":"bafyreifojc6akwnbam4qwhng2eknybwghuw6bchefcppb77dwk4q3x634u"},{"/":"bafyreiasermlv2oxhwbqnrstx2xfzcxwegxcdbb257ktwxbxp2y6drluie"},{"/":"bafyreif42fy5celttghcmu56jcxlqszxeienyeq4n55n2gblm5baelmjty"},{"/":"bafyreigrenu44ksbapi6xjvtmuv44bg3r6qoezfdte6swd24pj7bovb6jy"},{"/":"bafyreicjkcbgelriertgy7oxvluqly4azh5mg5f6ww32rdt5nsfnjw5tp4"},{"/":"bafyreic76qdhz7mkzxgakbf4vilw2jfox5i6n7cenn4sa3crz6567musya"},{"/":"bafyreieqiqjdxxzbzjttqy6fjv7rpucfnfc7o2pzcnd77tmdvfubyy6fwi"},{"/":"bafyreiey55hgvo6suxbtmape4354zh3ut24ng6vbdcznbcaaa66chrfefe"},{"/":"bafyreifb5ai37qgivkxe4jkhxsp7yx7jd532p3j4nyj2lxycl62bdcqfbe"},{"/":"bafyreifmjvfvhrklrccfqjv73qg7hkzbzoxlp2xlb7rb6w6wggkwk2w7qq"},{"/":"bafyreig4fqcbll2ebnuolsygy3c2mmhr2xlegeynza2cnfmivikjznf3dq"},{"/":"bafyreihdywyjsgcvfpbri3lfva7z5obvkqkjt5dsd5d6hn7gzlmryety2m"},{"/":"bafyreibeckhw2tlg4kq3oyw2hzbscf5zq4oacnxcsrqk2g6kpctol6zzku"},{"/":"bafyreibl2c2zxyp6iopopqjlcvud6ydrkjaojqinwfxpeksfny5fa7cgxq"},{"/":"bafyreihokydrp2jcc3aimok2gusry52cq3e3qqzo4b3qa3242bgcnalj2a"},{"/":"bafyreifuutenglldkeutff5wkywqw5fno2gpbcqhjoqk4jxkermvrzaomq"},{"/":"bafyreictdfxbjxcsq7e7edykxbhegzghpymmqcahwbtmfc6eers47vmahe"},{"/":"bafyreieiixahisdxfuv7jjtde42i2icaqdfee426im5doqe33iwwyv56ey"},{"/":"bafyreieudhjjtau2yfmuif3m5oezhexmiy62ys3iqayp5jqgvci6ruow5m"},{"/":"bafyreih7sw76c3vkql65w2p75ewjdrrxe7d2dijhhzgg5lrfkwbxrnqzca"},{"/":"bafyreifaks6dammgavp6dki4polw4w5llrql2eyrsjdckn4tz3h4f6zmoq"},{"/":"bafyreicstk6hmyi5kc5ie6y4wnwywugynfmlsmoq7vo56mwtb3qaujjdgi"},{"/":"bafyreibc4jevwag7c6c2csnow22puyupc3wpxcvtx2z257qagstuep6zmy"},{"/":"bafyreiaskan2ozpzmzrckxrhytafeh4znl6jqowny4kvscyoxsb42jxdkq"},{"/":"bafyreicvdwe77soazqw4kbg6exppzaoqhc2bs7lefrehibkcqlhqtbokl4"},{"/":"bafyreibm3vxjws7o6qnpklwiqu6uyl2eg6zvprnotud7wna4tqgfkmhnky"},{"/":"bafyreiglcixc7me3u355wcr7xvet75cgw2lk7wtui22lka4fybt4gpjuu4"},{"/":"bafyreib6fbzrirpp4gtbz2njetn4ihobeehvioatjdoay66cwvz4ug6vsu"},{"/":"bafyreig3v3ltmw52wsjusfu7y3ue5fesdvef5567rjwsy67elvzh7whndm"},{"/":"bafyreihb4mzev7bffmx52fykilxxrukkilyj5o2fwxa5ldmqofp453amby"},{"/":"bafyreicdrxhde7faynw2j3z3na3xqj32pwjmx43kw4hbobuwg5rraxhlpm"},{"/":"bafyreidmxw2qnw54uhsnsxx2wxrgst2l2fqmi25qlaitfjvjq5jd3mgnmy"},{"/":"bafyreicdiu5g4h3zx2sntwsud75l3noumbbrnwq3roo7337ryenzqr4bz4"},{"/":"bafyreibchdff7t4moiydb2mmw5itdeglppph5cvqjeolk53h6xijdthune"},{"/":"bafyreicwau6cnrem6au7socqnitzgeiszbrgpou3vnlti4awpdsy233j3y"},{"/":"bafyreidzytt6fjk5huolhq4c4rpzhoc6iyke6uowl26hunivy42n3iirfe"},{"/":"bafyreieshws6njwhxs4xrh5z3peu6i3ze3fsibhrdids7tvaiswndwry54"},{"/":"bafyreihuyyultpgd47sxct4227j65mci2rv72hlitn2x7rykniy4tcfabu"},{"/":"bafyreie6zfensf6rkrfgow3yryvvciost5brfowfnefc6joxmrloxcq25e"},{"/":"bafyreiftigxyi3mrn5yar3wprze6m56wlrxfedjm5rugmy2abdfebt3cq4"},{"/":"bafyreidz4jojjn62jfcaghklu4hl27blcla2jia3x5gqaaajdsroeexdyi"},{"/":"bafyreiazfg363okrecgvgm4cvitn3idizxegvsjpcnyntiafgiepvv3rjq"},{"/":"bafyreicfu5yjqdbbutuzpqlugkgq4lpp3bhobncng7gih7nqe3atyv5bni"},{"/":"bafyreie6bbwqlxinxzlz7zb2nunfaxaa7erbf2b4h5q5iyijk5wtjhfgpu"},{"/":"bafyreiarqfxo6czw27n7t67iugsooejszef7gibkr6bqqxap6xnos2vbhe"},{"/":"bafyreiawa4zrtqwiqausm65gg4yppz6csxyzhb44r6tdaaiz6xrwl66pae"},{"/":"bafyreievclildglrrtntpnvfbb4shwhdocdz7dvz23u4zvpe5ogjwnkhru"},{"/":"bafyreibaeusx7l6zmuny6l6afdp6ra4mrlyd3fwsybfhirhliq5d2fuzvy"},{"/":"bafyreidk6ptl3erb6c3sdf4s4hvdxobfedi2quguwzufgsyazkwhfknohe"},{"/":"bafyreifehmusapw2w2nk2xaecjkeit3gr5qdlk4ufxm6tj7dqa3u7orufe"},{"/":"bafyreifhuo5a2ujr4evyelm5d7uqllpwgkapetizuuxyamum52pr3zl5by"},{"/":"bafyreibyuelhubwcuy7vpokpij6habktgajgzcfip63co2jjijejg6hvgi"},{"/":"bafyreiaqj52c23wqoixfp2a3vqm6dstqbxpycopf724tr4slwfufvkquum"},{"/":"bafyreidwfx3d56yamiaf4ssstwjdehhwlynweynmwpehu4qa57rqbwsvrq"},{"/":"bafyreiauum3deq2xorhhiy4k5dvxr6r3k4ys6xaagvdhk2qjhjpkelsjz4"},{"/":"bafyreie5332arsol4momqndok45w6snx3oeoo6wzml4bcn24bxawaiko3u"},{"/":"bafyreicnnf7zck24gm7jempqht3uncja5icilfhqisl5rvs7klrkmkqs4m"},{"/":"bafyreiadayjxbartqnxjo47p44ggh2wxxkioo4or4rjve3svimkkdr3574"},{"/":"bafyreihklkk52zanejb5ssbl3t56tido56on4ytsvcxeaa4fmtpqromcpe"},{"/":"bafyreiet667slq5cq7da7cre7bpzxsr2gsw5kvuw3egga4w7a3xmaoezlm"},{"/":"bafyreicawncjw7bzffmau6pmibwx5xfhzej5cql6qu5cenvcr74v4oygqq"},{"/":"bafyreiga4tmlbgkrqne7spsxroqx26kgvs5g3nak3kb2klzdbeb6qygdcu"},{"/":"bafyreif3ddjrylksvyzroy56jbpzat4ovjkyphlczxl3hjo2brgltgephm"},{"/":"bafyreiekfacnfioqwjfsov2ifn3e32j7nsknt4edijfw2n57lqxhcxumzy"},{"/":"bafyreicuitfdnh33cx4ltszmyz6xhhxhiwg6ywmgb5wjhintjfhvitsvgy"},{"/":"bafyreiepty6npj5ididzpn5gucuuxkqdfnpz7eb4wwbalp4dudh7vbufvy"},{"/":"bafyreib2jom4k3vs25k7cfaaj3fgrrryqglhigvctwqml3ul2apk5sevai"},{"/":"bafyreibrpxjm3vgx2t7hlvcz7defxufusq4jfj2a7bqal5sbadwxba7wja"},{"/":"bafyreibkwt7oguxwydna4c3if3se3nm744issur5we5wor3qh76qvdpc7u"},{"/":"bafyreiey7ijx5pvtpwt6ktuf2ssypa46ne7mrpvceae3afwsawhzrdrvf4"},{"/":"bafyreiaiaiomhgbunuhshqypv2geczaqmlqb5tpklkkdxbpdeateqmt7hi"},{"/":"bafyreihd5lqtvp5f2wlwehhftbpozmoixdjjh6mvppy542gv2lmvyzl4iq"},{"/":"bafyreibow6jmmzaobsgcsfx3q2vqdv53a6yhps2fly4icjnsviguov6ghm"},{"/":"bafyreib46obqwpz4otyco66xxk5q2j26x7a3inttymguk254slcnl4sqdq"},{"/":"bafyreihih4ky4cg6eix4f7zcdkwnxogxeazo2qxboaybxv772kxtmco34a"},{"/":"bafyreig7nbkcpovq74fzhkn3jzrmyy4dyokr63jdp2mse62oflpxlqllhq"},{"/":"bafyreiegloc3kc53pwo2c76uie4wgnujvdjeo722sssbkr4ldzlv4i6d7e"},{"/":"bafyreihf4e2styswr4k6h54rkde7rtupjipdf4girmwrspbkrlrxb4r2eq"},{"/":"bafyreieiuwo3wenff7fnzyamjvu4eqmpnv2ud43tz4hxu7bnvmuctdxtxu"},{"/":"bafyreiewdm2tvmdsudmttrluo5xzdhkqmx46vsr2cbsmzmzjzc63g5wrhm"},{"/":"bafyreidgvrvincxxd54pllyxd2dsqu5wat5o5ypna5xuujpeyd7pfuzkia"},{"/":"bafyreianstjrzjcxfsbbmeooex4qxd2g3uauq3pegcy2owsggmrqgea52q"},{"/":"bafyreiao3h2earfom7wt45nvz6s2e2vmcayvobg6z7qtmmenaaqrqawp7y"},{"/":"bafyreibypmb2uvqusaysdyw55eu3wodaptvngemulqjtpg43noomalyviq"},{"/":"bafyreieeru7iivygphk6go264i4avp2llbi7oqns6hlesjzgdg5cev4pmq"},{"/":"bafyreieyd36xubv24f4xhbdmlwbj7gfepo6wyurxczmpekegea3xvtg4fq"},{"/":"bafyreif25ezww4oa4oth7nsh3jdetgty7pm7kfltvvz4k4f3wh2ej2x2ya"},{"/":"bafyreig5y5b5bh3ekyvxleydetcwoucsfxbdwuluqzq3fl2uzxh2t7vse4"},{"/":"bafyreid4hddedajmb4asm5iivhfejwbrejlntyxfeqidvbn27xxrefs2qe"},{"/":"bafyreicep45ei3wljlpgarc6bddq5an65dohkdeorqybpma3rnytu6ob3a"},{"/":"bafyreihwaokljqhtsnzjwnpflqwftta6nhuffg6nwvb6qowdlh7rqqsdde"},{"/":"bafyreibbxmaxnktgimm2ufo733rymdhb7o3ybcr774wfpwambtt2bqofzq"},{"/":"bafyreicpoqsi7pufmdkwmhuv6u2x6xl7tjmo22n2ikulcy3owgfll6ow3e"},{"/":"bafyreiah32jko74ke3u4uyl7lqpor2j5ojf77efcn373a2cydmb77twhli"},{"/":"bafyreiguz2qrafdicvnkgxn5t2gctknn5bj4yjd2aillzvokrmd77rqq3u"},{"/":"bafyreifoeamlyg732lpdj2pgx6lwhtclpn6r2xeo44w36btbstbd4urwfa"},{"/":"bafyreigap6tcbeskvdumjtqvogicprwyu4ybtg6dm4gy2emqlvmedodii4"},{"/":"bafyreiavv6js34ke4lnqql3jf2wxqidzjsahzdjfawp37tipesbadvxmyq"},{"/":"bafyreicmwi3hudesp7ayhhzw6xdx6gmf4mw4zx7lhhatlrdna7g4k4vyje"},{"/":"bafyreifdmon4sbqke2c5se6ipnaufasnlgd27dzy25w6ag3rgzknagxk3e"},{"/":"bafyreicci5refhwxebwttxh66doktpw4dxsqsjy65ig6lznhpd4s3pqd6i"},{"/":"bafyreie4hbwjfp7r2bvflhjlnqias4udqnlxbwgt33kw7dig6fg25gkl2y"},{"/":"bafyreiccf65vboj6v24mrn2qsht2embqaeoybi6ocpriy4ovjg2tyrjte4"},{"/":"bafyreickkywuvs2uqrm4aadyvcnzfz6da4nyto4435o24bopcbo762t6z4"},{"/":"bafyreid2zvbnrfnruo2cxsb3z7dc7lw7ytanhzyvwdwsb6t7fig5kcda6i"},{"/":"bafyreif4q32st7gdf5xqi345fho5wymi5kzzsfolnychk6ixs66og7nu7a"},{"/":"bafyreiamsjbyozsgu6yyzef45qcfnkg4i3lbpnqbkxjvsud7vco6gpfyqm"},{"/":"bafyreifov4yq252z4v5smkmxcb6lhqgt4t3mow2grnmw75wyg72o3fn6ga"},{"/":"bafyreif2xuc5qekeqw3f4idcezag4e22cqkvtk47ufpiw27e4vk7j2uj34"},{"/":"bafyreihkbnibxxelcvk5reuznelx2e3jnw6q533crmhaci3ujfuzf3bxvy"},{"/":"bafyreib4pdbtba6xutuw32cg672t5mxpetfhvbzrk6psyw7prtzhbpn6ra"},{"/":"bafyreicfqki5pavot5ydiecpyy4dgiy43mma2oelv5gain7dikbsekfzsu"},{"/":"bafyreic7q4xxtfbdrm4l27f2n764q6ywgiwuipwyuvs3ro57ng2yzefcbq"},{"/":"bafyreicw4on3q4eaymiqecqx4olu7yy2dcqqrx2z3jmnjrlteij2qmltj4"},{"/":"bafyreih4ub2ez5z5bx637zl44tgodq7z5g2mjxnjkb7es7kf7ubmi5ecvm"},{"/":"bafyreiheojy6blqd56jhvfdlizyfjtsd4fqci62mg77g5imopfiw3ftwey"},{"/":"bafyreieourchnj6gt3ygyd4amymy33wgbs7dp7pyfhos2cxoiucj3jmpwe"},{"/":"bafyreifagcbre5euv7ek5vcljs3t3bpggy2zz3yojmbj5b3nj66zfatmhi"},{"/":"bafyreienbwa5tyexahewmdxbs2mtdkk5okenr2odyzadaepcddbfqyd5uu"},{"/":"bafyreif6n43knx5mjdp6csx6j6cf2cvhormypuwj65lzne5zkoj3oewwii"},{"/":"bafyreigseklkvuift4ntldvgbw2njqedaf5rrp6smzz2evcumjm3kkfv3q"}],"meta":{"parent_slot":16848010,"blocktime":0,"block_height":null},"rewards":{"/":"bafkqaaa"}});

            let block = Block::from_bytes(raw).unwrap();
            let as_json = block.to_json();
            assert_eq!(as_json, as_json_raw);
        }
    }
}

// type Shredding struct {
// 	EntryEndIdx int
// 	ShredEndIdx int
// }
#[derive(Clone, PartialEq, Eq, Hash, Debug)]
pub struct Shredding {
    pub entry_end_idx: i64,
    pub shred_end_idx: i64,
}

impl Shredding {
    // from serde_cbor::Value
    pub fn from_cbor(val: serde_cbor::Value) -> Shredding {
        let mut shredding = Shredding {
            entry_end_idx: 0,
            shred_end_idx: 0,
        };

        if let serde_cbor::Value::Array(array) = val {
            // println!("Kind: {:?}", array[0]);
            if let Some(serde_cbor::Value::Integer(entry_end_idx)) = array.first() {
                // println!("Kind: {:?}", Kind::from_u64(kind as u64).unwrap().to_string());
                shredding.entry_end_idx = *entry_end_idx as i64;
            }
            if let Some(serde_cbor::Value::Integer(shred_end_idx)) = array.get(1) {
                shredding.shred_end_idx = *shred_end_idx as i64;
            }
        }
        shredding
    }

    pub fn to_json(&self) -> serde_json::Value {
        let mut map = serde_json::Map::new();
        map.insert(
            "entry_end_idx".to_string(),
            serde_json::Value::from(self.entry_end_idx),
        );
        map.insert(
            "shred_end_idx".to_string(),
            serde_json::Value::from(self.shred_end_idx),
        );

        serde_json::Value::from(map)
    }
}

#[cfg(test)]
mod shredding_tests {
    use super::*;

    #[test]
    fn test_shredding() {
        let shredding = Shredding {
            entry_end_idx: 1,
            shred_end_idx: 1,
        };
        let json = shredding.to_json();

        let wanted_json = serde_json::json!({
            "entry_end_idx": 1,
            "shred_end_idx": 1
        });

        assert_eq!(json, wanted_json);
    }
}

// type SlotMeta struct {
// 	Parent_slot  int
// 	Blocktime    int
// 	Block_height **int
// }
#[derive(Clone, PartialEq, Eq, Hash, Debug)]
pub struct SlotMeta {
    pub parent_slot: u64,
    pub blocktime: u64,
    pub block_height: Option<u64>,
}

impl SlotMeta {
    // from serde_cbor::Value
    pub fn from_cbor(val: serde_cbor::Value) -> SlotMeta {
        let mut slot_meta = SlotMeta {
            parent_slot: 0,
            blocktime: 0,
            block_height: None,
        };

        if let serde_cbor::Value::Array(array) = val {
            // println!("Kind: {:?}", array[0]);
            if let Some(serde_cbor::Value::Integer(parent_slot)) = array.first() {
                // println!("Kind: {:?}", Kind::from_u64(kind as u64).unwrap().to_string());
                slot_meta.parent_slot = *parent_slot as u64;
            }
            if let Some(serde_cbor::Value::Integer(blocktime)) = array.get(1) {
                slot_meta.blocktime = *blocktime as u64;
            }
            if let Some(serde_cbor::Value::Integer(block_height)) = array.get(2) {
                slot_meta.block_height = Some(*block_height as u64);
            }
        }
        slot_meta
    }

    pub fn to_json(&self) -> serde_json::Value {
        let mut map = serde_json::Map::new();
        map.insert(
            "parent_slot".to_string(),
            serde_json::Value::from(self.parent_slot),
        );
        map.insert(
            "blocktime".to_string(),
            serde_json::Value::from(self.blocktime),
        );
        if self.block_height.is_none() {
            map.insert("block_height".to_string(), serde_json::Value::Null);
        } else {
            map.insert(
                "block_height".to_string(),
                serde_json::Value::from(self.block_height),
            );
        }

        serde_json::Value::from(map)
    }
}

#[cfg(test)]
mod slot_meta_tests {
    use super::*;

    #[test]
    fn test_slot_meta() {
        let slot_meta = SlotMeta {
            parent_slot: 1,
            blocktime: 1,
            block_height: Some(1),
        };
        let json = slot_meta.to_json();

        let wanted_json = serde_json::json!({
            "parent_slot": 1,
            "blocktime": 1,
            "block_height": 1
        });

        assert_eq!(json, wanted_json);
    }
}
