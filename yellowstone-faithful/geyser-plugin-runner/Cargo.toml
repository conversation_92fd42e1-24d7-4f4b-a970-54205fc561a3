[package]
name = "demo-rust-ipld-car"
version = "0.1.0"
edition = "2021"
publish = false

[dependencies]
base64 = { workspace = true }
bincode = { workspace = true }
cid = { workspace = true }
crc = { workspace = true }
crossbeam-channel = { workspace = true }
fnv = { workspace = true }
multihash = { workspace = true }
prost_011 = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_cbor = { workspace = true }
serde_json = { workspace = true }
solana-accounts-db = { workspace = true }
solana-entry = { workspace = true }
solana-geyser-plugin-manager = { workspace = true }
solana-rpc = { workspace = true }
solana-runtime = { workspace = true }
solana-storage-proto = { workspace = true }
solana-transaction-status = { workspace = true }
solana-reward-info = { workspace = true }
solana-hash = { workspace = true }
solana-transaction = { workspace = true }
solana-pubkey = { workspace = true }
solana-message = { workspace = true }
solana-clock = { workspace = true }
solana-signature = { workspace = true }
solana-commitment-config = { workspace = true }
zstd = { workspace = true }
