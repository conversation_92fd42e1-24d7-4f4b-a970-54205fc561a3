package ipldsch

// Code generated by go-ipld-prime gengo.  DO NOT EDIT.

import (
	"github.com/ipld/go-ipld-prime/datamodel"
)

var _ datamodel.Node = nil // suppress errors when this dependency is not referenced
// Type is a struct embeding a NodePrototype/Type for every Node implementation in this package.
// One of its major uses is to start the construction of a value.
// You can use it like this:
//
//	ipldsch.Type.YourTypeName.NewBuilder().BeginMap() //...
//
// and:
//
//	ipldsch.Type.OtherTypeName.NewBuilder().AssignString("x") // ...
var Type typeSlab

type typeSlab struct {
	Block                 _Block__Prototype
	Block__Repr           _Block__ReprPrototype
	Bool                  _Bool__Prototype
	Bool__Repr            _Bool__ReprPrototype
	Buffer                _Buffer__Prototype
	Buffer__Repr          _Buffer__ReprPrototype
	Bytes                 _Bytes__Prototype
	Bytes__Repr           _Bytes__ReprPrototype
	DataFrame             _DataFrame__Prototype
	DataFrame__Repr       _DataFrame__ReprPrototype
	Entry                 _Entry__Prototype
	Entry__Repr           _Entry__ReprPrototype
	Epoch                 _Epoch__Prototype
	Epoch__Repr           _Epoch__ReprPrototype
	Float                 _Float__Prototype
	Float__Repr           _Float__ReprPrototype
	Hash                  _Hash__Prototype
	Hash__Repr            _Hash__ReprPrototype
	Int                   _Int__Prototype
	Int__Repr             _Int__ReprPrototype
	Link                  _Link__Prototype
	Link__Repr            _Link__ReprPrototype
	List__Link            _List__Link__Prototype
	List__Link__Repr      _List__Link__ReprPrototype
	List__Shredding       _List__Shredding__Prototype
	List__Shredding__Repr _List__Shredding__ReprPrototype
	Rewards               _Rewards__Prototype
	Rewards__Repr         _Rewards__ReprPrototype
	Shredding             _Shredding__Prototype
	Shredding__Repr       _Shredding__ReprPrototype
	SlotMeta              _SlotMeta__Prototype
	SlotMeta__Repr        _SlotMeta__ReprPrototype
	String                _String__Prototype
	String__Repr          _String__ReprPrototype
	Subset                _Subset__Prototype
	Subset__Repr          _Subset__ReprPrototype
	Transaction           _Transaction__Prototype
	Transaction__Repr     _Transaction__ReprPrototype
}

// --- type definitions follow ---

// Block matches the IPLD Schema type "Block".  It has struct type-kind, and may be interrogated like map kind.
type Block = *_Block
type _Block struct {
	kind      _Int
	slot      _Int
	shredding _List__Shredding
	entries   _List__Link
	meta      _SlotMeta
	rewards   _Link
}

// Bool matches the IPLD Schema type "Bool".  It has bool kind.
type Bool = *_Bool
type _Bool struct{ x bool }

// Buffer matches the IPLD Schema type "Buffer".  It has bytes kind.
type Buffer = *_Buffer
type _Buffer struct{ x []byte }

// Bytes matches the IPLD Schema type "Bytes".  It has bytes kind.
type Bytes = *_Bytes
type _Bytes struct{ x []byte }

// DataFrame matches the IPLD Schema type "DataFrame".  It has struct type-kind, and may be interrogated like map kind.
type DataFrame = *_DataFrame
type _DataFrame struct {
	kind  _Int
	hash  _Int__Maybe
	index _Int__Maybe
	total _Int__Maybe
	data  _Buffer
	next  _List__Link__Maybe
}

// Entry matches the IPLD Schema type "Entry".  It has struct type-kind, and may be interrogated like map kind.
type Entry = *_Entry
type _Entry struct {
	kind         _Int
	numHashes    _Int
	hash         _Hash
	transactions _List__Link
}

// Epoch matches the IPLD Schema type "Epoch".  It has struct type-kind, and may be interrogated like map kind.
type Epoch = *_Epoch
type _Epoch struct {
	kind    _Int
	epoch   _Int
	subsets _List__Link
}

// Float matches the IPLD Schema type "Float".  It has float kind.
type Float = *_Float
type _Float struct{ x float64 }

// Hash matches the IPLD Schema type "Hash".  It has bytes kind.
type Hash = *_Hash
type _Hash struct{ x []byte }

// Int matches the IPLD Schema type "Int".  It has int kind.
type Int = *_Int
type _Int struct{ x int64 }

// Link matches the IPLD Schema type "Link".  It has link kind.
type Link = *_Link
type _Link struct{ x datamodel.Link }

// List__Link matches the IPLD Schema type "List__Link".  It has list kind.
type List__Link = *_List__Link
type _List__Link struct {
	x []_Link
}

// List__Shredding matches the IPLD Schema type "List__Shredding".  It has list kind.
type List__Shredding = *_List__Shredding
type _List__Shredding struct {
	x []_Shredding
}

// Rewards matches the IPLD Schema type "Rewards".  It has struct type-kind, and may be interrogated like map kind.
type Rewards = *_Rewards
type _Rewards struct {
	kind _Int
	slot _Int
	data _DataFrame
}

// Shredding matches the IPLD Schema type "Shredding".  It has struct type-kind, and may be interrogated like map kind.
type Shredding = *_Shredding
type _Shredding struct {
	entryEndIdx _Int
	shredEndIdx _Int
}

// SlotMeta matches the IPLD Schema type "SlotMeta".  It has struct type-kind, and may be interrogated like map kind.
type SlotMeta = *_SlotMeta
type _SlotMeta struct {
	parent_slot  _Int
	blocktime    _Int
	block_height _Int__Maybe
}

// String matches the IPLD Schema type "String".  It has string kind.
type String = *_String
type _String struct{ x string }

// Subset matches the IPLD Schema type "Subset".  It has struct type-kind, and may be interrogated like map kind.
type Subset = *_Subset
type _Subset struct {
	kind   _Int
	first  _Int
	last   _Int
	blocks _List__Link
}

// Transaction matches the IPLD Schema type "Transaction".  It has struct type-kind, and may be interrogated like map kind.
type Transaction = *_Transaction
type _Transaction struct {
	kind     _Int
	data     _DataFrame
	metadata _DataFrame
	slot     _Int
	index    _Int__Maybe
}
