package ipldsch

// Code generated by go-ipld-prime gengo.  DO NOT EDIT.

import (
	"github.com/ipld/go-ipld-prime/datamodel"
	"github.com/ipld/go-ipld-prime/node/mixins"
	"github.com/ipld/go-ipld-prime/schema"
)

func (n _Block) FieldKind() Int {
	return &n.kind
}
func (n _Block) FieldSlot() Int {
	return &n.slot
}
func (n _Block) FieldShredding() List__Shredding {
	return &n.shredding
}
func (n _Block) FieldEntries() List__Link {
	return &n.entries
}
func (n _Block) FieldMeta() SlotMeta {
	return &n.meta
}
func (n _Block) FieldRewards() Link {
	return &n.rewards
}

type _Block__Maybe struct {
	m schema.Maybe
	v Block
}
type MaybeBlock = *_Block__Maybe

func (m MaybeBlock) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeBlock) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeBlock) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeBlock) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeBlock) Must() Block {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__Block_Kind      = _String{"kind"}
	fieldName__Block_Slot      = _String{"slot"}
	fieldName__Block_Shredding = _String{"shredding"}
	fieldName__Block_Entries   = _String{"entries"}
	fieldName__Block_Meta      = _String{"meta"}
	fieldName__Block_Rewards   = _String{"rewards"}
)
var _ datamodel.Node = (Block)(&_Block{})
var _ schema.TypedNode = (Block)(&_Block{})

func (Block) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n Block) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "kind":
		return &n.kind, nil
	case "slot":
		return &n.slot, nil
	case "shredding":
		return &n.shredding, nil
	case "entries":
		return &n.entries, nil
	case "meta":
		return &n.meta, nil
	case "rewards":
		return &n.rewards, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n Block) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (Block) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.Block"}.LookupByIndex(0)
}
func (n Block) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n Block) MapIterator() datamodel.MapIterator {
	return &_Block__MapItr{n, 0}
}

type _Block__MapItr struct {
	n   Block
	idx int
}

func (itr *_Block__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 6 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__Block_Kind
		v = &itr.n.kind
	case 1:
		k = &fieldName__Block_Slot
		v = &itr.n.slot
	case 2:
		k = &fieldName__Block_Shredding
		v = &itr.n.shredding
	case 3:
		k = &fieldName__Block_Entries
		v = &itr.n.entries
	case 4:
		k = &fieldName__Block_Meta
		v = &itr.n.meta
	case 5:
		k = &fieldName__Block_Rewards
		v = &itr.n.rewards
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Block__MapItr) Done() bool {
	return itr.idx >= 6
}

func (Block) ListIterator() datamodel.ListIterator {
	return nil
}
func (Block) Length() int64 {
	return 6
}
func (Block) IsAbsent() bool {
	return false
}
func (Block) IsNull() bool {
	return false
}
func (Block) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.Block"}.AsBool()
}
func (Block) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.Block"}.AsInt()
}
func (Block) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.Block"}.AsFloat()
}
func (Block) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.Block"}.AsString()
}
func (Block) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.Block"}.AsBytes()
}
func (Block) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.Block"}.AsLink()
}
func (Block) Prototype() datamodel.NodePrototype {
	return _Block__Prototype{}
}

type _Block__Prototype struct{}

func (_Block__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Block__Builder
	nb.Reset()
	return &nb
}

type _Block__Builder struct {
	_Block__Assembler
}

func (nb *_Block__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Block__Builder) Reset() {
	var w _Block
	var m schema.Maybe
	*nb = _Block__Builder{_Block__Assembler{w: &w, m: &m}}
}

type _Block__Assembler struct {
	w     *_Block
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm           schema.Maybe
	ca_kind      _Int__Assembler
	ca_slot      _Int__Assembler
	ca_shredding _List__Shredding__Assembler
	ca_entries   _List__Link__Assembler
	ca_meta      _SlotMeta__Assembler
	ca_rewards   _Link__Assembler
}

func (na *_Block__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_kind.reset()
	na.ca_slot.reset()
	na.ca_shredding.reset()
	na.ca_entries.reset()
	na.ca_meta.reset()
	na.ca_rewards.reset()
}

var (
	fieldBit__Block_Kind        = 1 << 0
	fieldBit__Block_Slot        = 1 << 1
	fieldBit__Block_Shredding   = 1 << 2
	fieldBit__Block_Entries     = 1 << 3
	fieldBit__Block_Meta        = 1 << 4
	fieldBit__Block_Rewards     = 1 << 5
	fieldBits__Block_sufficient = 0 + 1<<0 + 1<<1 + 1<<2 + 1<<3 + 1<<4 + 1<<5
)

func (na *_Block__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Block{}
	}
	return na, nil
}
func (_Block__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.Block"}.BeginList(0)
}
func (na *_Block__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.Block"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Block__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Block"}.AssignBool(false)
}
func (_Block__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Block"}.AssignInt(0)
}
func (_Block__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Block"}.AssignFloat(0)
}
func (_Block__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Block"}.AssignString("")
}
func (_Block__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Block"}.AssignBytes(nil)
}
func (_Block__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Block"}.AssignLink(nil)
}
func (na *_Block__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Block); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Block", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Block__Assembler) Prototype() datamodel.NodePrototype {
	return _Block__Prototype{}
}
func (ma *_Block__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_kind.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_slot.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 2:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_shredding.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 3:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_entries.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 4:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_meta.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 5:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_rewards.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_Block__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "kind":
		if ma.s&fieldBit__Block_Kind != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Kind}
		}
		ma.s += fieldBit__Block_Kind
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind, nil
	case "slot":
		if ma.s&fieldBit__Block_Slot != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Slot}
		}
		ma.s += fieldBit__Block_Slot
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_slot.w = &ma.w.slot
		ma.ca_slot.m = &ma.cm
		return &ma.ca_slot, nil
	case "shredding":
		if ma.s&fieldBit__Block_Shredding != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Shredding}
		}
		ma.s += fieldBit__Block_Shredding
		ma.state = maState_midValue
		ma.f = 2
		ma.ca_shredding.w = &ma.w.shredding
		ma.ca_shredding.m = &ma.cm
		return &ma.ca_shredding, nil
	case "entries":
		if ma.s&fieldBit__Block_Entries != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Entries}
		}
		ma.s += fieldBit__Block_Entries
		ma.state = maState_midValue
		ma.f = 3
		ma.ca_entries.w = &ma.w.entries
		ma.ca_entries.m = &ma.cm
		return &ma.ca_entries, nil
	case "meta":
		if ma.s&fieldBit__Block_Meta != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Meta}
		}
		ma.s += fieldBit__Block_Meta
		ma.state = maState_midValue
		ma.f = 4
		ma.ca_meta.w = &ma.w.meta
		ma.ca_meta.m = &ma.cm
		return &ma.ca_meta, nil
	case "rewards":
		if ma.s&fieldBit__Block_Rewards != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Rewards}
		}
		ma.s += fieldBit__Block_Rewards
		ma.state = maState_midValue
		ma.f = 5
		ma.ca_rewards.w = &ma.w.rewards
		ma.ca_rewards.m = &ma.cm
		return &ma.ca_rewards, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.Block", Key: &_String{k}}
}
func (ma *_Block__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_Block__KeyAssembler)(ma)
}
func (ma *_Block__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind
	case 1:
		ma.ca_slot.w = &ma.w.slot
		ma.ca_slot.m = &ma.cm
		return &ma.ca_slot
	case 2:
		ma.ca_shredding.w = &ma.w.shredding
		ma.ca_shredding.m = &ma.cm
		return &ma.ca_shredding
	case 3:
		ma.ca_entries.w = &ma.w.entries
		ma.ca_entries.m = &ma.cm
		return &ma.ca_entries
	case 4:
		ma.ca_meta.w = &ma.w.meta
		ma.ca_meta.m = &ma.cm
		return &ma.ca_meta
	case 5:
		ma.ca_rewards.w = &ma.w.rewards
		ma.ca_rewards.m = &ma.cm
		return &ma.ca_rewards
	default:
		panic("unreachable")
	}
}
func (ma *_Block__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__Block_sufficient != fieldBits__Block_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__Block_Kind == 0 {
			err.Missing = append(err.Missing, "kind")
		}
		if ma.s&fieldBit__Block_Slot == 0 {
			err.Missing = append(err.Missing, "slot")
		}
		if ma.s&fieldBit__Block_Shredding == 0 {
			err.Missing = append(err.Missing, "shredding")
		}
		if ma.s&fieldBit__Block_Entries == 0 {
			err.Missing = append(err.Missing, "entries")
		}
		if ma.s&fieldBit__Block_Meta == 0 {
			err.Missing = append(err.Missing, "meta")
		}
		if ma.s&fieldBit__Block_Rewards == 0 {
			err.Missing = append(err.Missing, "rewards")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_Block__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_Block__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _Block__KeyAssembler _Block__Assembler

func (_Block__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Block.KeyAssembler"}.BeginMap(0)
}
func (_Block__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Block.KeyAssembler"}.BeginList(0)
}
func (na *_Block__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.Block.KeyAssembler"}.AssignNull()
}
func (_Block__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Block.KeyAssembler"}.AssignBool(false)
}
func (_Block__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Block.KeyAssembler"}.AssignInt(0)
}
func (_Block__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Block.KeyAssembler"}.AssignFloat(0)
}
func (ka *_Block__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "kind":
		if ka.s&fieldBit__Block_Kind != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Kind}
		}
		ka.s += fieldBit__Block_Kind
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "slot":
		if ka.s&fieldBit__Block_Slot != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Slot}
		}
		ka.s += fieldBit__Block_Slot
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	case "shredding":
		if ka.s&fieldBit__Block_Shredding != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Shredding}
		}
		ka.s += fieldBit__Block_Shredding
		ka.state = maState_expectValue
		ka.f = 2
		return nil
	case "entries":
		if ka.s&fieldBit__Block_Entries != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Entries}
		}
		ka.s += fieldBit__Block_Entries
		ka.state = maState_expectValue
		ka.f = 3
		return nil
	case "meta":
		if ka.s&fieldBit__Block_Meta != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Meta}
		}
		ka.s += fieldBit__Block_Meta
		ka.state = maState_expectValue
		ka.f = 4
		return nil
	case "rewards":
		if ka.s&fieldBit__Block_Rewards != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Block_Rewards}
		}
		ka.s += fieldBit__Block_Rewards
		ka.state = maState_expectValue
		ka.f = 5
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.Block", Key: &_String{k}}
	}
}
func (_Block__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Block.KeyAssembler"}.AssignBytes(nil)
}
func (_Block__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Block.KeyAssembler"}.AssignLink(nil)
}
func (ka *_Block__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_Block__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (Block) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Block) Representation() datamodel.Node {
	return (*_Block__Repr)(n)
}

type _Block__Repr _Block

var _ datamodel.Node = &_Block__Repr{}

func (_Block__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_Block__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.Block.Repr"}.LookupByString("")
}
func (n *_Block__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_Block__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.kind.Representation(), nil
	case 1:
		return n.slot.Representation(), nil
	case 2:
		return n.shredding.Representation(), nil
	case 3:
		return n.entries.Representation(), nil
	case 4:
		return n.meta.Representation(), nil
	case 5:
		return n.rewards.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _Block__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.Block.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_Block__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_Block__Repr) ListIterator() datamodel.ListIterator {
	return &_Block__ReprListItr{n, 0}
}

type _Block__ReprListItr struct {
	n   *_Block__Repr
	idx int
}

func (itr *_Block__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 6 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.kind.Representation()
	case 1:
		idx = int64(itr.idx)
		v = itr.n.slot.Representation()
	case 2:
		idx = int64(itr.idx)
		v = itr.n.shredding.Representation()
	case 3:
		idx = int64(itr.idx)
		v = itr.n.entries.Representation()
	case 4:
		idx = int64(itr.idx)
		v = itr.n.meta.Representation()
	case 5:
		idx = int64(itr.idx)
		v = itr.n.rewards.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Block__ReprListItr) Done() bool {
	return itr.idx >= 6
}

func (rn *_Block__Repr) Length() int64 {
	l := 6
	return int64(l)
}
func (_Block__Repr) IsAbsent() bool {
	return false
}
func (_Block__Repr) IsNull() bool {
	return false
}
func (_Block__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.Block.Repr"}.AsBool()
}
func (_Block__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.Block.Repr"}.AsInt()
}
func (_Block__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.Block.Repr"}.AsFloat()
}
func (_Block__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.Block.Repr"}.AsString()
}
func (_Block__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.Block.Repr"}.AsBytes()
}
func (_Block__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.Block.Repr"}.AsLink()
}
func (_Block__Repr) Prototype() datamodel.NodePrototype {
	return _Block__ReprPrototype{}
}

type _Block__ReprPrototype struct{}

func (_Block__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Block__ReprBuilder
	nb.Reset()
	return &nb
}

type _Block__ReprBuilder struct {
	_Block__ReprAssembler
}

func (nb *_Block__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Block__ReprBuilder) Reset() {
	var w _Block
	var m schema.Maybe
	*nb = _Block__ReprBuilder{_Block__ReprAssembler{w: &w, m: &m}}
}

type _Block__ReprAssembler struct {
	w     *_Block
	m     *schema.Maybe
	state laState
	f     int

	cm           schema.Maybe
	ca_kind      _Int__ReprAssembler
	ca_slot      _Int__ReprAssembler
	ca_shredding _List__Shredding__ReprAssembler
	ca_entries   _List__Link__ReprAssembler
	ca_meta      _SlotMeta__ReprAssembler
	ca_rewards   _Link__ReprAssembler
}

func (na *_Block__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_kind.reset()
	na.ca_slot.reset()
	na.ca_shredding.reset()
	na.ca_entries.reset()
	na.ca_meta.reset()
	na.ca_rewards.reset()
}
func (_Block__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.Block.Repr"}.BeginMap(0)
}
func (na *_Block__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Block{}
	}
	return na, nil
}
func (na *_Block__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.Block.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Block__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Block.Repr"}.AssignBool(false)
}
func (_Block__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Block.Repr"}.AssignInt(0)
}
func (_Block__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Block.Repr"}.AssignFloat(0)
}
func (_Block__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Block.Repr"}.AssignString("")
}
func (_Block__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Block.Repr"}.AssignBytes(nil)
}
func (_Block__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Block.Repr"}.AssignLink(nil)
}
func (na *_Block__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Block); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Block.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Block__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _Block__ReprPrototype{}
}
func (la *_Block__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 2:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 3:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 4:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 5:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_Block__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 6 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(6)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_kind.w = &la.w.kind
		la.ca_kind.m = &la.cm
		return &la.ca_kind
	case 1:
		la.ca_slot.w = &la.w.slot
		la.ca_slot.m = &la.cm
		return &la.ca_slot
	case 2:
		la.ca_shredding.w = &la.w.shredding
		la.ca_shredding.m = &la.cm
		return &la.ca_shredding
	case 3:
		la.ca_entries.w = &la.w.entries
		la.ca_entries.m = &la.cm
		return &la.ca_entries
	case 4:
		la.ca_meta.w = &la.w.meta
		la.ca_meta.m = &la.cm
		return &la.ca_meta
	case 5:
		la.ca_rewards.w = &la.w.rewards
		la.ca_rewards.m = &la.cm
		return &la.ca_rewards
	default:
		panic("unreachable")
	}
}
func (la *_Block__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_Block__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}

func (n Bool) Bool() bool {
	return n.x
}
func (_Bool__Prototype) FromBool(v bool) (Bool, error) {
	n := _Bool{v}
	return &n, nil
}

type _Bool__Maybe struct {
	m schema.Maybe
	v _Bool
}
type MaybeBool = *_Bool__Maybe

func (m MaybeBool) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeBool) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeBool) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeBool) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeBool) Must() Bool {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (Bool)(&_Bool{})
var _ schema.TypedNode = (Bool)(&_Bool{})

func (Bool) Kind() datamodel.Kind {
	return datamodel.Kind_Bool
}
func (Bool) LookupByString(string) (datamodel.Node, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.LookupByString("")
}
func (Bool) LookupByNode(datamodel.Node) (datamodel.Node, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.LookupByNode(nil)
}
func (Bool) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.LookupByIndex(0)
}
func (Bool) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.LookupBySegment(seg)
}
func (Bool) MapIterator() datamodel.MapIterator {
	return nil
}
func (Bool) ListIterator() datamodel.ListIterator {
	return nil
}
func (Bool) Length() int64 {
	return -1
}
func (Bool) IsAbsent() bool {
	return false
}
func (Bool) IsNull() bool {
	return false
}
func (n Bool) AsBool() (bool, error) {
	return n.x, nil
}
func (Bool) AsInt() (int64, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.AsInt()
}
func (Bool) AsFloat() (float64, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.AsFloat()
}
func (Bool) AsString() (string, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.AsString()
}
func (Bool) AsBytes() ([]byte, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.AsBytes()
}
func (Bool) AsLink() (datamodel.Link, error) {
	return mixins.Bool{TypeName: "ipldsch.Bool"}.AsLink()
}
func (Bool) Prototype() datamodel.NodePrototype {
	return _Bool__Prototype{}
}

type _Bool__Prototype struct{}

func (_Bool__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Bool__Builder
	nb.Reset()
	return &nb
}

type _Bool__Builder struct {
	_Bool__Assembler
}

func (nb *_Bool__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Bool__Builder) Reset() {
	var w _Bool
	var m schema.Maybe
	*nb = _Bool__Builder{_Bool__Assembler{w: &w, m: &m}}
}

type _Bool__Assembler struct {
	w *_Bool
	m *schema.Maybe
}

func (na *_Bool__Assembler) reset() {}
func (_Bool__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.BoolAssembler{TypeName: "ipldsch.Bool"}.BeginMap(0)
}
func (_Bool__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.BoolAssembler{TypeName: "ipldsch.Bool"}.BeginList(0)
}
func (na *_Bool__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.BoolAssembler{TypeName: "ipldsch.Bool"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	panic("unreachable")
}
func (na *_Bool__Assembler) AssignBool(v bool) error {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	na.w.x = v
	*na.m = schema.Maybe_Value
	return nil
}
func (_Bool__Assembler) AssignInt(int64) error {
	return mixins.BoolAssembler{TypeName: "ipldsch.Bool"}.AssignInt(0)
}
func (_Bool__Assembler) AssignFloat(float64) error {
	return mixins.BoolAssembler{TypeName: "ipldsch.Bool"}.AssignFloat(0)
}
func (_Bool__Assembler) AssignString(string) error {
	return mixins.BoolAssembler{TypeName: "ipldsch.Bool"}.AssignString("")
}
func (_Bool__Assembler) AssignBytes([]byte) error {
	return mixins.BoolAssembler{TypeName: "ipldsch.Bool"}.AssignBytes(nil)
}
func (_Bool__Assembler) AssignLink(datamodel.Link) error {
	return mixins.BoolAssembler{TypeName: "ipldsch.Bool"}.AssignLink(nil)
}
func (na *_Bool__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Bool); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v2, err := v.AsBool(); err != nil {
		return err
	} else {
		return na.AssignBool(v2)
	}
}
func (_Bool__Assembler) Prototype() datamodel.NodePrototype {
	return _Bool__Prototype{}
}
func (Bool) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Bool) Representation() datamodel.Node {
	return (*_Bool__Repr)(n)
}

type _Bool__Repr = _Bool

var _ datamodel.Node = &_Bool__Repr{}

type _Bool__ReprPrototype = _Bool__Prototype
type _Bool__ReprAssembler = _Bool__Assembler

func (n Buffer) Bytes() []byte {
	return n.x
}
func (_Buffer__Prototype) FromBytes(v []byte) (Buffer, error) {
	n := _Buffer{v}
	return &n, nil
}

type _Buffer__Maybe struct {
	m schema.Maybe
	v _Buffer
}
type MaybeBuffer = *_Buffer__Maybe

func (m MaybeBuffer) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeBuffer) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeBuffer) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeBuffer) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeBuffer) Must() Buffer {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (Buffer)(&_Buffer{})
var _ schema.TypedNode = (Buffer)(&_Buffer{})

func (Buffer) Kind() datamodel.Kind {
	return datamodel.Kind_Bytes
}
func (Buffer) LookupByString(string) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.LookupByString("")
}
func (Buffer) LookupByNode(datamodel.Node) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.LookupByNode(nil)
}
func (Buffer) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.LookupByIndex(0)
}
func (Buffer) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.LookupBySegment(seg)
}
func (Buffer) MapIterator() datamodel.MapIterator {
	return nil
}
func (Buffer) ListIterator() datamodel.ListIterator {
	return nil
}
func (Buffer) Length() int64 {
	return -1
}
func (Buffer) IsAbsent() bool {
	return false
}
func (Buffer) IsNull() bool {
	return false
}
func (Buffer) AsBool() (bool, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.AsBool()
}
func (Buffer) AsInt() (int64, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.AsInt()
}
func (Buffer) AsFloat() (float64, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.AsFloat()
}
func (Buffer) AsString() (string, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.AsString()
}
func (n Buffer) AsBytes() ([]byte, error) {
	return n.x, nil
}
func (Buffer) AsLink() (datamodel.Link, error) {
	return mixins.Bytes{TypeName: "ipldsch.Buffer"}.AsLink()
}
func (Buffer) Prototype() datamodel.NodePrototype {
	return _Buffer__Prototype{}
}

type _Buffer__Prototype struct{}

func (_Buffer__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Buffer__Builder
	nb.Reset()
	return &nb
}

type _Buffer__Builder struct {
	_Buffer__Assembler
}

func (nb *_Buffer__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Buffer__Builder) Reset() {
	var w _Buffer
	var m schema.Maybe
	*nb = _Buffer__Builder{_Buffer__Assembler{w: &w, m: &m}}
}

type _Buffer__Assembler struct {
	w *_Buffer
	m *schema.Maybe
}

func (na *_Buffer__Assembler) reset() {}
func (_Buffer__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.BytesAssembler{TypeName: "ipldsch.Buffer"}.BeginMap(0)
}
func (_Buffer__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.BytesAssembler{TypeName: "ipldsch.Buffer"}.BeginList(0)
}
func (na *_Buffer__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.BytesAssembler{TypeName: "ipldsch.Buffer"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	panic("unreachable")
}
func (_Buffer__Assembler) AssignBool(bool) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Buffer"}.AssignBool(false)
}
func (_Buffer__Assembler) AssignInt(int64) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Buffer"}.AssignInt(0)
}
func (_Buffer__Assembler) AssignFloat(float64) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Buffer"}.AssignFloat(0)
}
func (_Buffer__Assembler) AssignString(string) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Buffer"}.AssignString("")
}
func (na *_Buffer__Assembler) AssignBytes(v []byte) error {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	na.w.x = v
	*na.m = schema.Maybe_Value
	return nil
}
func (_Buffer__Assembler) AssignLink(datamodel.Link) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Buffer"}.AssignLink(nil)
}
func (na *_Buffer__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Buffer); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v2, err := v.AsBytes(); err != nil {
		return err
	} else {
		return na.AssignBytes(v2)
	}
}
func (_Buffer__Assembler) Prototype() datamodel.NodePrototype {
	return _Buffer__Prototype{}
}
func (Buffer) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Buffer) Representation() datamodel.Node {
	return (*_Buffer__Repr)(n)
}

type _Buffer__Repr = _Buffer

var _ datamodel.Node = &_Buffer__Repr{}

type _Buffer__ReprPrototype = _Buffer__Prototype
type _Buffer__ReprAssembler = _Buffer__Assembler

func (n Bytes) Bytes() []byte {
	return n.x
}
func (_Bytes__Prototype) FromBytes(v []byte) (Bytes, error) {
	n := _Bytes{v}
	return &n, nil
}

type _Bytes__Maybe struct {
	m schema.Maybe
	v _Bytes
}
type MaybeBytes = *_Bytes__Maybe

func (m MaybeBytes) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeBytes) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeBytes) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeBytes) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeBytes) Must() Bytes {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (Bytes)(&_Bytes{})
var _ schema.TypedNode = (Bytes)(&_Bytes{})

func (Bytes) Kind() datamodel.Kind {
	return datamodel.Kind_Bytes
}
func (Bytes) LookupByString(string) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.LookupByString("")
}
func (Bytes) LookupByNode(datamodel.Node) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.LookupByNode(nil)
}
func (Bytes) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.LookupByIndex(0)
}
func (Bytes) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.LookupBySegment(seg)
}
func (Bytes) MapIterator() datamodel.MapIterator {
	return nil
}
func (Bytes) ListIterator() datamodel.ListIterator {
	return nil
}
func (Bytes) Length() int64 {
	return -1
}
func (Bytes) IsAbsent() bool {
	return false
}
func (Bytes) IsNull() bool {
	return false
}
func (Bytes) AsBool() (bool, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.AsBool()
}
func (Bytes) AsInt() (int64, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.AsInt()
}
func (Bytes) AsFloat() (float64, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.AsFloat()
}
func (Bytes) AsString() (string, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.AsString()
}
func (n Bytes) AsBytes() ([]byte, error) {
	return n.x, nil
}
func (Bytes) AsLink() (datamodel.Link, error) {
	return mixins.Bytes{TypeName: "ipldsch.Bytes"}.AsLink()
}
func (Bytes) Prototype() datamodel.NodePrototype {
	return _Bytes__Prototype{}
}

type _Bytes__Prototype struct{}

func (_Bytes__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Bytes__Builder
	nb.Reset()
	return &nb
}

type _Bytes__Builder struct {
	_Bytes__Assembler
}

func (nb *_Bytes__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Bytes__Builder) Reset() {
	var w _Bytes
	var m schema.Maybe
	*nb = _Bytes__Builder{_Bytes__Assembler{w: &w, m: &m}}
}

type _Bytes__Assembler struct {
	w *_Bytes
	m *schema.Maybe
}

func (na *_Bytes__Assembler) reset() {}
func (_Bytes__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.BytesAssembler{TypeName: "ipldsch.Bytes"}.BeginMap(0)
}
func (_Bytes__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.BytesAssembler{TypeName: "ipldsch.Bytes"}.BeginList(0)
}
func (na *_Bytes__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.BytesAssembler{TypeName: "ipldsch.Bytes"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	panic("unreachable")
}
func (_Bytes__Assembler) AssignBool(bool) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Bytes"}.AssignBool(false)
}
func (_Bytes__Assembler) AssignInt(int64) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Bytes"}.AssignInt(0)
}
func (_Bytes__Assembler) AssignFloat(float64) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Bytes"}.AssignFloat(0)
}
func (_Bytes__Assembler) AssignString(string) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Bytes"}.AssignString("")
}
func (na *_Bytes__Assembler) AssignBytes(v []byte) error {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	na.w.x = v
	*na.m = schema.Maybe_Value
	return nil
}
func (_Bytes__Assembler) AssignLink(datamodel.Link) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Bytes"}.AssignLink(nil)
}
func (na *_Bytes__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Bytes); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v2, err := v.AsBytes(); err != nil {
		return err
	} else {
		return na.AssignBytes(v2)
	}
}
func (_Bytes__Assembler) Prototype() datamodel.NodePrototype {
	return _Bytes__Prototype{}
}
func (Bytes) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Bytes) Representation() datamodel.Node {
	return (*_Bytes__Repr)(n)
}

type _Bytes__Repr = _Bytes

var _ datamodel.Node = &_Bytes__Repr{}

type _Bytes__ReprPrototype = _Bytes__Prototype
type _Bytes__ReprAssembler = _Bytes__Assembler

func (n _DataFrame) FieldKind() Int {
	return &n.kind
}
func (n _DataFrame) FieldHash() MaybeInt {
	return &n.hash
}
func (n _DataFrame) FieldIndex() MaybeInt {
	return &n.index
}
func (n _DataFrame) FieldTotal() MaybeInt {
	return &n.total
}
func (n _DataFrame) FieldData() Buffer {
	return &n.data
}
func (n _DataFrame) FieldNext() MaybeList__Link {
	return &n.next
}

type _DataFrame__Maybe struct {
	m schema.Maybe
	v DataFrame
}
type MaybeDataFrame = *_DataFrame__Maybe

func (m MaybeDataFrame) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeDataFrame) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeDataFrame) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeDataFrame) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeDataFrame) Must() DataFrame {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__DataFrame_Kind  = _String{"kind"}
	fieldName__DataFrame_Hash  = _String{"hash"}
	fieldName__DataFrame_Index = _String{"index"}
	fieldName__DataFrame_Total = _String{"total"}
	fieldName__DataFrame_Data  = _String{"data"}
	fieldName__DataFrame_Next  = _String{"next"}
)
var _ datamodel.Node = (DataFrame)(&_DataFrame{})
var _ schema.TypedNode = (DataFrame)(&_DataFrame{})

func (DataFrame) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n DataFrame) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "kind":
		return &n.kind, nil
	case "hash":
		if n.hash.m == schema.Maybe_Absent {
			return datamodel.Absent, nil
		}
		if n.hash.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return &n.hash.v, nil
	case "index":
		if n.index.m == schema.Maybe_Absent {
			return datamodel.Absent, nil
		}
		if n.index.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return &n.index.v, nil
	case "total":
		if n.total.m == schema.Maybe_Absent {
			return datamodel.Absent, nil
		}
		if n.total.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return &n.total.v, nil
	case "data":
		return &n.data, nil
	case "next":
		if n.next.m == schema.Maybe_Absent {
			return datamodel.Absent, nil
		}
		if n.next.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return &n.next.v, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n DataFrame) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (DataFrame) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.DataFrame"}.LookupByIndex(0)
}
func (n DataFrame) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n DataFrame) MapIterator() datamodel.MapIterator {
	return &_DataFrame__MapItr{n, 0}
}

type _DataFrame__MapItr struct {
	n   DataFrame
	idx int
}

func (itr *_DataFrame__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 6 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__DataFrame_Kind
		v = &itr.n.kind
	case 1:
		k = &fieldName__DataFrame_Hash
		if itr.n.hash.m == schema.Maybe_Absent {
			v = datamodel.Absent
			break
		}
		if itr.n.hash.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = &itr.n.hash.v
	case 2:
		k = &fieldName__DataFrame_Index
		if itr.n.index.m == schema.Maybe_Absent {
			v = datamodel.Absent
			break
		}
		if itr.n.index.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = &itr.n.index.v
	case 3:
		k = &fieldName__DataFrame_Total
		if itr.n.total.m == schema.Maybe_Absent {
			v = datamodel.Absent
			break
		}
		if itr.n.total.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = &itr.n.total.v
	case 4:
		k = &fieldName__DataFrame_Data
		v = &itr.n.data
	case 5:
		k = &fieldName__DataFrame_Next
		if itr.n.next.m == schema.Maybe_Absent {
			v = datamodel.Absent
			break
		}
		if itr.n.next.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = &itr.n.next.v
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_DataFrame__MapItr) Done() bool {
	return itr.idx >= 6
}

func (DataFrame) ListIterator() datamodel.ListIterator {
	return nil
}
func (DataFrame) Length() int64 {
	return 6
}
func (DataFrame) IsAbsent() bool {
	return false
}
func (DataFrame) IsNull() bool {
	return false
}
func (DataFrame) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.DataFrame"}.AsBool()
}
func (DataFrame) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.DataFrame"}.AsInt()
}
func (DataFrame) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.DataFrame"}.AsFloat()
}
func (DataFrame) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.DataFrame"}.AsString()
}
func (DataFrame) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.DataFrame"}.AsBytes()
}
func (DataFrame) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.DataFrame"}.AsLink()
}
func (DataFrame) Prototype() datamodel.NodePrototype {
	return _DataFrame__Prototype{}
}

type _DataFrame__Prototype struct{}

func (_DataFrame__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _DataFrame__Builder
	nb.Reset()
	return &nb
}

type _DataFrame__Builder struct {
	_DataFrame__Assembler
}

func (nb *_DataFrame__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_DataFrame__Builder) Reset() {
	var w _DataFrame
	var m schema.Maybe
	*nb = _DataFrame__Builder{_DataFrame__Assembler{w: &w, m: &m}}
}

type _DataFrame__Assembler struct {
	w     *_DataFrame
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm       schema.Maybe
	ca_kind  _Int__Assembler
	ca_hash  _Int__Assembler
	ca_index _Int__Assembler
	ca_total _Int__Assembler
	ca_data  _Buffer__Assembler
	ca_next  _List__Link__Assembler
}

func (na *_DataFrame__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_kind.reset()
	na.ca_hash.reset()
	na.ca_index.reset()
	na.ca_total.reset()
	na.ca_data.reset()
	na.ca_next.reset()
}

var (
	fieldBit__DataFrame_Kind        = 1 << 0
	fieldBit__DataFrame_Hash        = 1 << 1
	fieldBit__DataFrame_Index       = 1 << 2
	fieldBit__DataFrame_Total       = 1 << 3
	fieldBit__DataFrame_Data        = 1 << 4
	fieldBit__DataFrame_Next        = 1 << 5
	fieldBits__DataFrame_sufficient = 0 + 1<<0 + 1<<4
)

func (na *_DataFrame__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_DataFrame{}
	}
	return na, nil
}
func (_DataFrame__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.DataFrame"}.BeginList(0)
}
func (na *_DataFrame__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.DataFrame"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_DataFrame__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.DataFrame"}.AssignBool(false)
}
func (_DataFrame__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.DataFrame"}.AssignInt(0)
}
func (_DataFrame__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.DataFrame"}.AssignFloat(0)
}
func (_DataFrame__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.DataFrame"}.AssignString("")
}
func (_DataFrame__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.DataFrame"}.AssignBytes(nil)
}
func (_DataFrame__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.DataFrame"}.AssignLink(nil)
}
func (na *_DataFrame__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_DataFrame); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.DataFrame", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_DataFrame__Assembler) Prototype() datamodel.NodePrototype {
	return _DataFrame__Prototype{}
}
func (ma *_DataFrame__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_kind.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.w.hash.m {
		case schema.Maybe_Null:
			ma.state = maState_initial
			return true
		case schema.Maybe_Value:
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 2:
		switch ma.w.index.m {
		case schema.Maybe_Null:
			ma.state = maState_initial
			return true
		case schema.Maybe_Value:
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 3:
		switch ma.w.total.m {
		case schema.Maybe_Null:
			ma.state = maState_initial
			return true
		case schema.Maybe_Value:
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 4:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_data.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 5:
		switch ma.w.next.m {
		case schema.Maybe_Null:
			ma.state = maState_initial
			return true
		case schema.Maybe_Value:
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_DataFrame__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "kind":
		if ma.s&fieldBit__DataFrame_Kind != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Kind}
		}
		ma.s += fieldBit__DataFrame_Kind
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind, nil
	case "hash":
		if ma.s&fieldBit__DataFrame_Hash != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Hash}
		}
		ma.s += fieldBit__DataFrame_Hash
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_hash.w = &ma.w.hash.v
		ma.ca_hash.m = &ma.w.hash.m
		ma.w.hash.m = allowNull
		return &ma.ca_hash, nil
	case "index":
		if ma.s&fieldBit__DataFrame_Index != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Index}
		}
		ma.s += fieldBit__DataFrame_Index
		ma.state = maState_midValue
		ma.f = 2
		ma.ca_index.w = &ma.w.index.v
		ma.ca_index.m = &ma.w.index.m
		ma.w.index.m = allowNull
		return &ma.ca_index, nil
	case "total":
		if ma.s&fieldBit__DataFrame_Total != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Total}
		}
		ma.s += fieldBit__DataFrame_Total
		ma.state = maState_midValue
		ma.f = 3
		ma.ca_total.w = &ma.w.total.v
		ma.ca_total.m = &ma.w.total.m
		ma.w.total.m = allowNull
		return &ma.ca_total, nil
	case "data":
		if ma.s&fieldBit__DataFrame_Data != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Data}
		}
		ma.s += fieldBit__DataFrame_Data
		ma.state = maState_midValue
		ma.f = 4
		ma.ca_data.w = &ma.w.data
		ma.ca_data.m = &ma.cm
		return &ma.ca_data, nil
	case "next":
		if ma.s&fieldBit__DataFrame_Next != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Next}
		}
		ma.s += fieldBit__DataFrame_Next
		ma.state = maState_midValue
		ma.f = 5
		ma.ca_next.w = &ma.w.next.v
		ma.ca_next.m = &ma.w.next.m
		ma.w.next.m = allowNull
		return &ma.ca_next, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.DataFrame", Key: &_String{k}}
}
func (ma *_DataFrame__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_DataFrame__KeyAssembler)(ma)
}
func (ma *_DataFrame__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind
	case 1:
		ma.ca_hash.w = &ma.w.hash.v
		ma.ca_hash.m = &ma.w.hash.m
		ma.w.hash.m = allowNull
		return &ma.ca_hash
	case 2:
		ma.ca_index.w = &ma.w.index.v
		ma.ca_index.m = &ma.w.index.m
		ma.w.index.m = allowNull
		return &ma.ca_index
	case 3:
		ma.ca_total.w = &ma.w.total.v
		ma.ca_total.m = &ma.w.total.m
		ma.w.total.m = allowNull
		return &ma.ca_total
	case 4:
		ma.ca_data.w = &ma.w.data
		ma.ca_data.m = &ma.cm
		return &ma.ca_data
	case 5:
		ma.ca_next.w = &ma.w.next.v
		ma.ca_next.m = &ma.w.next.m
		ma.w.next.m = allowNull
		return &ma.ca_next
	default:
		panic("unreachable")
	}
}
func (ma *_DataFrame__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__DataFrame_sufficient != fieldBits__DataFrame_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__DataFrame_Kind == 0 {
			err.Missing = append(err.Missing, "kind")
		}
		if ma.s&fieldBit__DataFrame_Data == 0 {
			err.Missing = append(err.Missing, "data")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_DataFrame__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_DataFrame__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _DataFrame__KeyAssembler _DataFrame__Assembler

func (_DataFrame__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.DataFrame.KeyAssembler"}.BeginMap(0)
}
func (_DataFrame__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.DataFrame.KeyAssembler"}.BeginList(0)
}
func (na *_DataFrame__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.DataFrame.KeyAssembler"}.AssignNull()
}
func (_DataFrame__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.DataFrame.KeyAssembler"}.AssignBool(false)
}
func (_DataFrame__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.DataFrame.KeyAssembler"}.AssignInt(0)
}
func (_DataFrame__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.DataFrame.KeyAssembler"}.AssignFloat(0)
}
func (ka *_DataFrame__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "kind":
		if ka.s&fieldBit__DataFrame_Kind != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Kind}
		}
		ka.s += fieldBit__DataFrame_Kind
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "hash":
		if ka.s&fieldBit__DataFrame_Hash != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Hash}
		}
		ka.s += fieldBit__DataFrame_Hash
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	case "index":
		if ka.s&fieldBit__DataFrame_Index != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Index}
		}
		ka.s += fieldBit__DataFrame_Index
		ka.state = maState_expectValue
		ka.f = 2
		return nil
	case "total":
		if ka.s&fieldBit__DataFrame_Total != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Total}
		}
		ka.s += fieldBit__DataFrame_Total
		ka.state = maState_expectValue
		ka.f = 3
		return nil
	case "data":
		if ka.s&fieldBit__DataFrame_Data != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Data}
		}
		ka.s += fieldBit__DataFrame_Data
		ka.state = maState_expectValue
		ka.f = 4
		return nil
	case "next":
		if ka.s&fieldBit__DataFrame_Next != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__DataFrame_Next}
		}
		ka.s += fieldBit__DataFrame_Next
		ka.state = maState_expectValue
		ka.f = 5
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.DataFrame", Key: &_String{k}}
	}
}
func (_DataFrame__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.DataFrame.KeyAssembler"}.AssignBytes(nil)
}
func (_DataFrame__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.DataFrame.KeyAssembler"}.AssignLink(nil)
}
func (ka *_DataFrame__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_DataFrame__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (DataFrame) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n DataFrame) Representation() datamodel.Node {
	return (*_DataFrame__Repr)(n)
}

type _DataFrame__Repr _DataFrame

var _ datamodel.Node = &_DataFrame__Repr{}

func (_DataFrame__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_DataFrame__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.DataFrame.Repr"}.LookupByString("")
}
func (n *_DataFrame__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_DataFrame__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.kind.Representation(), nil
	case 1:
		if n.hash.m == schema.Maybe_Absent {
			return datamodel.Absent, datamodel.ErrNotExists{Segment: datamodel.PathSegmentOfInt(idx)}
		}
		if n.hash.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return n.hash.v.Representation(), nil
	case 2:
		if n.index.m == schema.Maybe_Absent {
			return datamodel.Absent, datamodel.ErrNotExists{Segment: datamodel.PathSegmentOfInt(idx)}
		}
		if n.index.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return n.index.v.Representation(), nil
	case 3:
		if n.total.m == schema.Maybe_Absent {
			return datamodel.Absent, datamodel.ErrNotExists{Segment: datamodel.PathSegmentOfInt(idx)}
		}
		if n.total.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return n.total.v.Representation(), nil
	case 4:
		return n.data.Representation(), nil
	case 5:
		if n.next.m == schema.Maybe_Absent {
			return datamodel.Absent, datamodel.ErrNotExists{Segment: datamodel.PathSegmentOfInt(idx)}
		}
		if n.next.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return n.next.v.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _DataFrame__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.DataFrame.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_DataFrame__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_DataFrame__Repr) ListIterator() datamodel.ListIterator {
	end := 6
	if n.next.m == schema.Maybe_Absent {
		end = 5
	} else {
		goto done
	}
done:
	return &_DataFrame__ReprListItr{n, 0, end}
}

type _DataFrame__ReprListItr struct {
	n   *_DataFrame__Repr
	idx int
	end int
}

func (itr *_DataFrame__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 6 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.kind.Representation()
	case 1:
		idx = int64(itr.idx)
		if itr.n.hash.m == schema.Maybe_Absent {
			return -1, nil, datamodel.ErrIteratorOverread{}
		}
		if itr.n.hash.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = itr.n.hash.v.Representation()
	case 2:
		idx = int64(itr.idx)
		if itr.n.index.m == schema.Maybe_Absent {
			return -1, nil, datamodel.ErrIteratorOverread{}
		}
		if itr.n.index.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = itr.n.index.v.Representation()
	case 3:
		idx = int64(itr.idx)
		if itr.n.total.m == schema.Maybe_Absent {
			return -1, nil, datamodel.ErrIteratorOverread{}
		}
		if itr.n.total.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = itr.n.total.v.Representation()
	case 4:
		idx = int64(itr.idx)
		v = itr.n.data.Representation()
	case 5:
		idx = int64(itr.idx)
		if itr.n.next.m == schema.Maybe_Absent {
			return -1, nil, datamodel.ErrIteratorOverread{}
		}
		if itr.n.next.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = itr.n.next.v.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_DataFrame__ReprListItr) Done() bool {
	return itr.idx >= itr.end
}

func (rn *_DataFrame__Repr) Length() int64 {
	l := 6
	if rn.hash.m == schema.Maybe_Absent {
		l--
	}
	if rn.index.m == schema.Maybe_Absent {
		l--
	}
	if rn.total.m == schema.Maybe_Absent {
		l--
	}
	if rn.next.m == schema.Maybe_Absent {
		l--
	}
	return int64(l)
}
func (_DataFrame__Repr) IsAbsent() bool {
	return false
}
func (_DataFrame__Repr) IsNull() bool {
	return false
}
func (_DataFrame__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.DataFrame.Repr"}.AsBool()
}
func (_DataFrame__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.DataFrame.Repr"}.AsInt()
}
func (_DataFrame__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.DataFrame.Repr"}.AsFloat()
}
func (_DataFrame__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.DataFrame.Repr"}.AsString()
}
func (_DataFrame__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.DataFrame.Repr"}.AsBytes()
}
func (_DataFrame__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.DataFrame.Repr"}.AsLink()
}
func (_DataFrame__Repr) Prototype() datamodel.NodePrototype {
	return _DataFrame__ReprPrototype{}
}

type _DataFrame__ReprPrototype struct{}

func (_DataFrame__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _DataFrame__ReprBuilder
	nb.Reset()
	return &nb
}

type _DataFrame__ReprBuilder struct {
	_DataFrame__ReprAssembler
}

func (nb *_DataFrame__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_DataFrame__ReprBuilder) Reset() {
	var w _DataFrame
	var m schema.Maybe
	*nb = _DataFrame__ReprBuilder{_DataFrame__ReprAssembler{w: &w, m: &m}}
}

type _DataFrame__ReprAssembler struct {
	w     *_DataFrame
	m     *schema.Maybe
	state laState
	f     int

	cm       schema.Maybe
	ca_kind  _Int__ReprAssembler
	ca_hash  _Int__ReprAssembler
	ca_index _Int__ReprAssembler
	ca_total _Int__ReprAssembler
	ca_data  _Buffer__ReprAssembler
	ca_next  _List__Link__ReprAssembler
}

func (na *_DataFrame__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_kind.reset()
	na.ca_hash.reset()
	na.ca_index.reset()
	na.ca_total.reset()
	na.ca_data.reset()
	na.ca_next.reset()
}
func (_DataFrame__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.DataFrame.Repr"}.BeginMap(0)
}
func (na *_DataFrame__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_DataFrame{}
	}
	return na, nil
}
func (na *_DataFrame__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.DataFrame.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_DataFrame__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.DataFrame.Repr"}.AssignBool(false)
}
func (_DataFrame__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.DataFrame.Repr"}.AssignInt(0)
}
func (_DataFrame__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.DataFrame.Repr"}.AssignFloat(0)
}
func (_DataFrame__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.DataFrame.Repr"}.AssignString("")
}
func (_DataFrame__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.DataFrame.Repr"}.AssignBytes(nil)
}
func (_DataFrame__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.DataFrame.Repr"}.AssignLink(nil)
}
func (na *_DataFrame__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_DataFrame); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.DataFrame.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_DataFrame__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _DataFrame__ReprPrototype{}
}
func (la *_DataFrame__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.w.hash.m {
		case schema.Maybe_Value:
			la.state = laState_initial
			la.f++
			return true
		case schema.Maybe_Null:
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 2:
		switch la.w.index.m {
		case schema.Maybe_Value:
			la.state = laState_initial
			la.f++
			return true
		case schema.Maybe_Null:
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 3:
		switch la.w.total.m {
		case schema.Maybe_Value:
			la.state = laState_initial
			la.f++
			return true
		case schema.Maybe_Null:
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 4:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 5:
		switch la.w.next.m {
		case schema.Maybe_Value:
			la.state = laState_initial
			la.f++
			return true
		case schema.Maybe_Null:
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_DataFrame__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 6 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(6)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_kind.w = &la.w.kind
		la.ca_kind.m = &la.cm
		return &la.ca_kind
	case 1:
		la.ca_hash.w = &la.w.hash.v
		la.ca_hash.m = &la.w.hash.m
		la.w.hash.m = allowNull
		return &la.ca_hash
	case 2:
		la.ca_index.w = &la.w.index.v
		la.ca_index.m = &la.w.index.m
		la.w.index.m = allowNull
		return &la.ca_index
	case 3:
		la.ca_total.w = &la.w.total.v
		la.ca_total.m = &la.w.total.m
		la.w.total.m = allowNull
		return &la.ca_total
	case 4:
		la.ca_data.w = &la.w.data
		la.ca_data.m = &la.cm
		return &la.ca_data
	case 5:
		la.ca_next.w = &la.w.next.v
		la.ca_next.m = &la.w.next.m
		la.w.next.m = allowNull
		return &la.ca_next
	default:
		panic("unreachable")
	}
}
func (la *_DataFrame__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_DataFrame__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}

func (n _Entry) FieldKind() Int {
	return &n.kind
}
func (n _Entry) FieldNumHashes() Int {
	return &n.numHashes
}
func (n _Entry) FieldHash() Hash {
	return &n.hash
}
func (n _Entry) FieldTransactions() List__Link {
	return &n.transactions
}

type _Entry__Maybe struct {
	m schema.Maybe
	v Entry
}
type MaybeEntry = *_Entry__Maybe

func (m MaybeEntry) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeEntry) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeEntry) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeEntry) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeEntry) Must() Entry {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__Entry_Kind         = _String{"kind"}
	fieldName__Entry_NumHashes    = _String{"numHashes"}
	fieldName__Entry_Hash         = _String{"hash"}
	fieldName__Entry_Transactions = _String{"transactions"}
)
var _ datamodel.Node = (Entry)(&_Entry{})
var _ schema.TypedNode = (Entry)(&_Entry{})

func (Entry) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n Entry) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "kind":
		return &n.kind, nil
	case "numHashes":
		return &n.numHashes, nil
	case "hash":
		return &n.hash, nil
	case "transactions":
		return &n.transactions, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n Entry) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (Entry) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.Entry"}.LookupByIndex(0)
}
func (n Entry) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n Entry) MapIterator() datamodel.MapIterator {
	return &_Entry__MapItr{n, 0}
}

type _Entry__MapItr struct {
	n   Entry
	idx int
}

func (itr *_Entry__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 4 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__Entry_Kind
		v = &itr.n.kind
	case 1:
		k = &fieldName__Entry_NumHashes
		v = &itr.n.numHashes
	case 2:
		k = &fieldName__Entry_Hash
		v = &itr.n.hash
	case 3:
		k = &fieldName__Entry_Transactions
		v = &itr.n.transactions
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Entry__MapItr) Done() bool {
	return itr.idx >= 4
}

func (Entry) ListIterator() datamodel.ListIterator {
	return nil
}
func (Entry) Length() int64 {
	return 4
}
func (Entry) IsAbsent() bool {
	return false
}
func (Entry) IsNull() bool {
	return false
}
func (Entry) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.Entry"}.AsBool()
}
func (Entry) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.Entry"}.AsInt()
}
func (Entry) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.Entry"}.AsFloat()
}
func (Entry) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.Entry"}.AsString()
}
func (Entry) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.Entry"}.AsBytes()
}
func (Entry) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.Entry"}.AsLink()
}
func (Entry) Prototype() datamodel.NodePrototype {
	return _Entry__Prototype{}
}

type _Entry__Prototype struct{}

func (_Entry__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Entry__Builder
	nb.Reset()
	return &nb
}

type _Entry__Builder struct {
	_Entry__Assembler
}

func (nb *_Entry__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Entry__Builder) Reset() {
	var w _Entry
	var m schema.Maybe
	*nb = _Entry__Builder{_Entry__Assembler{w: &w, m: &m}}
}

type _Entry__Assembler struct {
	w     *_Entry
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm              schema.Maybe
	ca_kind         _Int__Assembler
	ca_numHashes    _Int__Assembler
	ca_hash         _Hash__Assembler
	ca_transactions _List__Link__Assembler
}

func (na *_Entry__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_kind.reset()
	na.ca_numHashes.reset()
	na.ca_hash.reset()
	na.ca_transactions.reset()
}

var (
	fieldBit__Entry_Kind         = 1 << 0
	fieldBit__Entry_NumHashes    = 1 << 1
	fieldBit__Entry_Hash         = 1 << 2
	fieldBit__Entry_Transactions = 1 << 3
	fieldBits__Entry_sufficient  = 0 + 1<<0 + 1<<1 + 1<<2 + 1<<3
)

func (na *_Entry__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Entry{}
	}
	return na, nil
}
func (_Entry__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.Entry"}.BeginList(0)
}
func (na *_Entry__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.Entry"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Entry__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Entry"}.AssignBool(false)
}
func (_Entry__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Entry"}.AssignInt(0)
}
func (_Entry__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Entry"}.AssignFloat(0)
}
func (_Entry__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Entry"}.AssignString("")
}
func (_Entry__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Entry"}.AssignBytes(nil)
}
func (_Entry__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Entry"}.AssignLink(nil)
}
func (na *_Entry__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Entry); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Entry", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Entry__Assembler) Prototype() datamodel.NodePrototype {
	return _Entry__Prototype{}
}
func (ma *_Entry__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_kind.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_numHashes.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 2:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_hash.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 3:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_transactions.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_Entry__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "kind":
		if ma.s&fieldBit__Entry_Kind != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Entry_Kind}
		}
		ma.s += fieldBit__Entry_Kind
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind, nil
	case "numHashes":
		if ma.s&fieldBit__Entry_NumHashes != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Entry_NumHashes}
		}
		ma.s += fieldBit__Entry_NumHashes
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_numHashes.w = &ma.w.numHashes
		ma.ca_numHashes.m = &ma.cm
		return &ma.ca_numHashes, nil
	case "hash":
		if ma.s&fieldBit__Entry_Hash != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Entry_Hash}
		}
		ma.s += fieldBit__Entry_Hash
		ma.state = maState_midValue
		ma.f = 2
		ma.ca_hash.w = &ma.w.hash
		ma.ca_hash.m = &ma.cm
		return &ma.ca_hash, nil
	case "transactions":
		if ma.s&fieldBit__Entry_Transactions != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Entry_Transactions}
		}
		ma.s += fieldBit__Entry_Transactions
		ma.state = maState_midValue
		ma.f = 3
		ma.ca_transactions.w = &ma.w.transactions
		ma.ca_transactions.m = &ma.cm
		return &ma.ca_transactions, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.Entry", Key: &_String{k}}
}
func (ma *_Entry__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_Entry__KeyAssembler)(ma)
}
func (ma *_Entry__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind
	case 1:
		ma.ca_numHashes.w = &ma.w.numHashes
		ma.ca_numHashes.m = &ma.cm
		return &ma.ca_numHashes
	case 2:
		ma.ca_hash.w = &ma.w.hash
		ma.ca_hash.m = &ma.cm
		return &ma.ca_hash
	case 3:
		ma.ca_transactions.w = &ma.w.transactions
		ma.ca_transactions.m = &ma.cm
		return &ma.ca_transactions
	default:
		panic("unreachable")
	}
}
func (ma *_Entry__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__Entry_sufficient != fieldBits__Entry_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__Entry_Kind == 0 {
			err.Missing = append(err.Missing, "kind")
		}
		if ma.s&fieldBit__Entry_NumHashes == 0 {
			err.Missing = append(err.Missing, "numHashes")
		}
		if ma.s&fieldBit__Entry_Hash == 0 {
			err.Missing = append(err.Missing, "hash")
		}
		if ma.s&fieldBit__Entry_Transactions == 0 {
			err.Missing = append(err.Missing, "transactions")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_Entry__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_Entry__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _Entry__KeyAssembler _Entry__Assembler

func (_Entry__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Entry.KeyAssembler"}.BeginMap(0)
}
func (_Entry__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Entry.KeyAssembler"}.BeginList(0)
}
func (na *_Entry__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.Entry.KeyAssembler"}.AssignNull()
}
func (_Entry__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Entry.KeyAssembler"}.AssignBool(false)
}
func (_Entry__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Entry.KeyAssembler"}.AssignInt(0)
}
func (_Entry__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Entry.KeyAssembler"}.AssignFloat(0)
}
func (ka *_Entry__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "kind":
		if ka.s&fieldBit__Entry_Kind != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Entry_Kind}
		}
		ka.s += fieldBit__Entry_Kind
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "numHashes":
		if ka.s&fieldBit__Entry_NumHashes != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Entry_NumHashes}
		}
		ka.s += fieldBit__Entry_NumHashes
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	case "hash":
		if ka.s&fieldBit__Entry_Hash != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Entry_Hash}
		}
		ka.s += fieldBit__Entry_Hash
		ka.state = maState_expectValue
		ka.f = 2
		return nil
	case "transactions":
		if ka.s&fieldBit__Entry_Transactions != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Entry_Transactions}
		}
		ka.s += fieldBit__Entry_Transactions
		ka.state = maState_expectValue
		ka.f = 3
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.Entry", Key: &_String{k}}
	}
}
func (_Entry__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Entry.KeyAssembler"}.AssignBytes(nil)
}
func (_Entry__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Entry.KeyAssembler"}.AssignLink(nil)
}
func (ka *_Entry__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_Entry__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (Entry) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Entry) Representation() datamodel.Node {
	return (*_Entry__Repr)(n)
}

type _Entry__Repr _Entry

var _ datamodel.Node = &_Entry__Repr{}

func (_Entry__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_Entry__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.Entry.Repr"}.LookupByString("")
}
func (n *_Entry__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_Entry__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.kind.Representation(), nil
	case 1:
		return n.numHashes.Representation(), nil
	case 2:
		return n.hash.Representation(), nil
	case 3:
		return n.transactions.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _Entry__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.Entry.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_Entry__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_Entry__Repr) ListIterator() datamodel.ListIterator {
	return &_Entry__ReprListItr{n, 0}
}

type _Entry__ReprListItr struct {
	n   *_Entry__Repr
	idx int
}

func (itr *_Entry__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 4 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.kind.Representation()
	case 1:
		idx = int64(itr.idx)
		v = itr.n.numHashes.Representation()
	case 2:
		idx = int64(itr.idx)
		v = itr.n.hash.Representation()
	case 3:
		idx = int64(itr.idx)
		v = itr.n.transactions.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Entry__ReprListItr) Done() bool {
	return itr.idx >= 4
}

func (rn *_Entry__Repr) Length() int64 {
	l := 4
	return int64(l)
}
func (_Entry__Repr) IsAbsent() bool {
	return false
}
func (_Entry__Repr) IsNull() bool {
	return false
}
func (_Entry__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.Entry.Repr"}.AsBool()
}
func (_Entry__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.Entry.Repr"}.AsInt()
}
func (_Entry__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.Entry.Repr"}.AsFloat()
}
func (_Entry__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.Entry.Repr"}.AsString()
}
func (_Entry__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.Entry.Repr"}.AsBytes()
}
func (_Entry__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.Entry.Repr"}.AsLink()
}
func (_Entry__Repr) Prototype() datamodel.NodePrototype {
	return _Entry__ReprPrototype{}
}

type _Entry__ReprPrototype struct{}

func (_Entry__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Entry__ReprBuilder
	nb.Reset()
	return &nb
}

type _Entry__ReprBuilder struct {
	_Entry__ReprAssembler
}

func (nb *_Entry__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Entry__ReprBuilder) Reset() {
	var w _Entry
	var m schema.Maybe
	*nb = _Entry__ReprBuilder{_Entry__ReprAssembler{w: &w, m: &m}}
}

type _Entry__ReprAssembler struct {
	w     *_Entry
	m     *schema.Maybe
	state laState
	f     int

	cm              schema.Maybe
	ca_kind         _Int__ReprAssembler
	ca_numHashes    _Int__ReprAssembler
	ca_hash         _Hash__ReprAssembler
	ca_transactions _List__Link__ReprAssembler
}

func (na *_Entry__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_kind.reset()
	na.ca_numHashes.reset()
	na.ca_hash.reset()
	na.ca_transactions.reset()
}
func (_Entry__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.Entry.Repr"}.BeginMap(0)
}
func (na *_Entry__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Entry{}
	}
	return na, nil
}
func (na *_Entry__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.Entry.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Entry__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Entry.Repr"}.AssignBool(false)
}
func (_Entry__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Entry.Repr"}.AssignInt(0)
}
func (_Entry__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Entry.Repr"}.AssignFloat(0)
}
func (_Entry__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Entry.Repr"}.AssignString("")
}
func (_Entry__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Entry.Repr"}.AssignBytes(nil)
}
func (_Entry__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Entry.Repr"}.AssignLink(nil)
}
func (na *_Entry__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Entry); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Entry.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Entry__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _Entry__ReprPrototype{}
}
func (la *_Entry__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 2:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 3:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_Entry__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 4 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(4)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_kind.w = &la.w.kind
		la.ca_kind.m = &la.cm
		return &la.ca_kind
	case 1:
		la.ca_numHashes.w = &la.w.numHashes
		la.ca_numHashes.m = &la.cm
		return &la.ca_numHashes
	case 2:
		la.ca_hash.w = &la.w.hash
		la.ca_hash.m = &la.cm
		return &la.ca_hash
	case 3:
		la.ca_transactions.w = &la.w.transactions
		la.ca_transactions.m = &la.cm
		return &la.ca_transactions
	default:
		panic("unreachable")
	}
}
func (la *_Entry__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_Entry__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}

func (n _Epoch) FieldKind() Int {
	return &n.kind
}
func (n _Epoch) FieldEpoch() Int {
	return &n.epoch
}
func (n _Epoch) FieldSubsets() List__Link {
	return &n.subsets
}

type _Epoch__Maybe struct {
	m schema.Maybe
	v Epoch
}
type MaybeEpoch = *_Epoch__Maybe

func (m MaybeEpoch) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeEpoch) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeEpoch) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeEpoch) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeEpoch) Must() Epoch {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__Epoch_Kind    = _String{"kind"}
	fieldName__Epoch_Epoch   = _String{"epoch"}
	fieldName__Epoch_Subsets = _String{"subsets"}
)
var _ datamodel.Node = (Epoch)(&_Epoch{})
var _ schema.TypedNode = (Epoch)(&_Epoch{})

func (Epoch) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n Epoch) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "kind":
		return &n.kind, nil
	case "epoch":
		return &n.epoch, nil
	case "subsets":
		return &n.subsets, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n Epoch) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (Epoch) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.Epoch"}.LookupByIndex(0)
}
func (n Epoch) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n Epoch) MapIterator() datamodel.MapIterator {
	return &_Epoch__MapItr{n, 0}
}

type _Epoch__MapItr struct {
	n   Epoch
	idx int
}

func (itr *_Epoch__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 3 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__Epoch_Kind
		v = &itr.n.kind
	case 1:
		k = &fieldName__Epoch_Epoch
		v = &itr.n.epoch
	case 2:
		k = &fieldName__Epoch_Subsets
		v = &itr.n.subsets
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Epoch__MapItr) Done() bool {
	return itr.idx >= 3
}

func (Epoch) ListIterator() datamodel.ListIterator {
	return nil
}
func (Epoch) Length() int64 {
	return 3
}
func (Epoch) IsAbsent() bool {
	return false
}
func (Epoch) IsNull() bool {
	return false
}
func (Epoch) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.Epoch"}.AsBool()
}
func (Epoch) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.Epoch"}.AsInt()
}
func (Epoch) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.Epoch"}.AsFloat()
}
func (Epoch) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.Epoch"}.AsString()
}
func (Epoch) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.Epoch"}.AsBytes()
}
func (Epoch) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.Epoch"}.AsLink()
}
func (Epoch) Prototype() datamodel.NodePrototype {
	return _Epoch__Prototype{}
}

type _Epoch__Prototype struct{}

func (_Epoch__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Epoch__Builder
	nb.Reset()
	return &nb
}

type _Epoch__Builder struct {
	_Epoch__Assembler
}

func (nb *_Epoch__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Epoch__Builder) Reset() {
	var w _Epoch
	var m schema.Maybe
	*nb = _Epoch__Builder{_Epoch__Assembler{w: &w, m: &m}}
}

type _Epoch__Assembler struct {
	w     *_Epoch
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm         schema.Maybe
	ca_kind    _Int__Assembler
	ca_epoch   _Int__Assembler
	ca_subsets _List__Link__Assembler
}

func (na *_Epoch__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_kind.reset()
	na.ca_epoch.reset()
	na.ca_subsets.reset()
}

var (
	fieldBit__Epoch_Kind        = 1 << 0
	fieldBit__Epoch_Epoch       = 1 << 1
	fieldBit__Epoch_Subsets     = 1 << 2
	fieldBits__Epoch_sufficient = 0 + 1<<0 + 1<<1 + 1<<2
)

func (na *_Epoch__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Epoch{}
	}
	return na, nil
}
func (_Epoch__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.Epoch"}.BeginList(0)
}
func (na *_Epoch__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.Epoch"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Epoch__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Epoch"}.AssignBool(false)
}
func (_Epoch__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Epoch"}.AssignInt(0)
}
func (_Epoch__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Epoch"}.AssignFloat(0)
}
func (_Epoch__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Epoch"}.AssignString("")
}
func (_Epoch__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Epoch"}.AssignBytes(nil)
}
func (_Epoch__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Epoch"}.AssignLink(nil)
}
func (na *_Epoch__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Epoch); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Epoch", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Epoch__Assembler) Prototype() datamodel.NodePrototype {
	return _Epoch__Prototype{}
}
func (ma *_Epoch__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_kind.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_epoch.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 2:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_subsets.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_Epoch__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "kind":
		if ma.s&fieldBit__Epoch_Kind != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Epoch_Kind}
		}
		ma.s += fieldBit__Epoch_Kind
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind, nil
	case "epoch":
		if ma.s&fieldBit__Epoch_Epoch != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Epoch_Epoch}
		}
		ma.s += fieldBit__Epoch_Epoch
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_epoch.w = &ma.w.epoch
		ma.ca_epoch.m = &ma.cm
		return &ma.ca_epoch, nil
	case "subsets":
		if ma.s&fieldBit__Epoch_Subsets != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Epoch_Subsets}
		}
		ma.s += fieldBit__Epoch_Subsets
		ma.state = maState_midValue
		ma.f = 2
		ma.ca_subsets.w = &ma.w.subsets
		ma.ca_subsets.m = &ma.cm
		return &ma.ca_subsets, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.Epoch", Key: &_String{k}}
}
func (ma *_Epoch__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_Epoch__KeyAssembler)(ma)
}
func (ma *_Epoch__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind
	case 1:
		ma.ca_epoch.w = &ma.w.epoch
		ma.ca_epoch.m = &ma.cm
		return &ma.ca_epoch
	case 2:
		ma.ca_subsets.w = &ma.w.subsets
		ma.ca_subsets.m = &ma.cm
		return &ma.ca_subsets
	default:
		panic("unreachable")
	}
}
func (ma *_Epoch__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__Epoch_sufficient != fieldBits__Epoch_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__Epoch_Kind == 0 {
			err.Missing = append(err.Missing, "kind")
		}
		if ma.s&fieldBit__Epoch_Epoch == 0 {
			err.Missing = append(err.Missing, "epoch")
		}
		if ma.s&fieldBit__Epoch_Subsets == 0 {
			err.Missing = append(err.Missing, "subsets")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_Epoch__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_Epoch__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _Epoch__KeyAssembler _Epoch__Assembler

func (_Epoch__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Epoch.KeyAssembler"}.BeginMap(0)
}
func (_Epoch__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Epoch.KeyAssembler"}.BeginList(0)
}
func (na *_Epoch__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.Epoch.KeyAssembler"}.AssignNull()
}
func (_Epoch__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Epoch.KeyAssembler"}.AssignBool(false)
}
func (_Epoch__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Epoch.KeyAssembler"}.AssignInt(0)
}
func (_Epoch__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Epoch.KeyAssembler"}.AssignFloat(0)
}
func (ka *_Epoch__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "kind":
		if ka.s&fieldBit__Epoch_Kind != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Epoch_Kind}
		}
		ka.s += fieldBit__Epoch_Kind
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "epoch":
		if ka.s&fieldBit__Epoch_Epoch != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Epoch_Epoch}
		}
		ka.s += fieldBit__Epoch_Epoch
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	case "subsets":
		if ka.s&fieldBit__Epoch_Subsets != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Epoch_Subsets}
		}
		ka.s += fieldBit__Epoch_Subsets
		ka.state = maState_expectValue
		ka.f = 2
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.Epoch", Key: &_String{k}}
	}
}
func (_Epoch__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Epoch.KeyAssembler"}.AssignBytes(nil)
}
func (_Epoch__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Epoch.KeyAssembler"}.AssignLink(nil)
}
func (ka *_Epoch__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_Epoch__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (Epoch) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Epoch) Representation() datamodel.Node {
	return (*_Epoch__Repr)(n)
}

type _Epoch__Repr _Epoch

var _ datamodel.Node = &_Epoch__Repr{}

func (_Epoch__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_Epoch__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.Epoch.Repr"}.LookupByString("")
}
func (n *_Epoch__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_Epoch__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.kind.Representation(), nil
	case 1:
		return n.epoch.Representation(), nil
	case 2:
		return n.subsets.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _Epoch__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.Epoch.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_Epoch__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_Epoch__Repr) ListIterator() datamodel.ListIterator {
	return &_Epoch__ReprListItr{n, 0}
}

type _Epoch__ReprListItr struct {
	n   *_Epoch__Repr
	idx int
}

func (itr *_Epoch__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 3 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.kind.Representation()
	case 1:
		idx = int64(itr.idx)
		v = itr.n.epoch.Representation()
	case 2:
		idx = int64(itr.idx)
		v = itr.n.subsets.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Epoch__ReprListItr) Done() bool {
	return itr.idx >= 3
}

func (rn *_Epoch__Repr) Length() int64 {
	l := 3
	return int64(l)
}
func (_Epoch__Repr) IsAbsent() bool {
	return false
}
func (_Epoch__Repr) IsNull() bool {
	return false
}
func (_Epoch__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.Epoch.Repr"}.AsBool()
}
func (_Epoch__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.Epoch.Repr"}.AsInt()
}
func (_Epoch__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.Epoch.Repr"}.AsFloat()
}
func (_Epoch__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.Epoch.Repr"}.AsString()
}
func (_Epoch__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.Epoch.Repr"}.AsBytes()
}
func (_Epoch__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.Epoch.Repr"}.AsLink()
}
func (_Epoch__Repr) Prototype() datamodel.NodePrototype {
	return _Epoch__ReprPrototype{}
}

type _Epoch__ReprPrototype struct{}

func (_Epoch__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Epoch__ReprBuilder
	nb.Reset()
	return &nb
}

type _Epoch__ReprBuilder struct {
	_Epoch__ReprAssembler
}

func (nb *_Epoch__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Epoch__ReprBuilder) Reset() {
	var w _Epoch
	var m schema.Maybe
	*nb = _Epoch__ReprBuilder{_Epoch__ReprAssembler{w: &w, m: &m}}
}

type _Epoch__ReprAssembler struct {
	w     *_Epoch
	m     *schema.Maybe
	state laState
	f     int

	cm         schema.Maybe
	ca_kind    _Int__ReprAssembler
	ca_epoch   _Int__ReprAssembler
	ca_subsets _List__Link__ReprAssembler
}

func (na *_Epoch__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_kind.reset()
	na.ca_epoch.reset()
	na.ca_subsets.reset()
}
func (_Epoch__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.Epoch.Repr"}.BeginMap(0)
}
func (na *_Epoch__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Epoch{}
	}
	return na, nil
}
func (na *_Epoch__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.Epoch.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Epoch__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Epoch.Repr"}.AssignBool(false)
}
func (_Epoch__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Epoch.Repr"}.AssignInt(0)
}
func (_Epoch__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Epoch.Repr"}.AssignFloat(0)
}
func (_Epoch__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Epoch.Repr"}.AssignString("")
}
func (_Epoch__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Epoch.Repr"}.AssignBytes(nil)
}
func (_Epoch__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Epoch.Repr"}.AssignLink(nil)
}
func (na *_Epoch__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Epoch); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Epoch.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Epoch__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _Epoch__ReprPrototype{}
}
func (la *_Epoch__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 2:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_Epoch__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 3 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(3)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_kind.w = &la.w.kind
		la.ca_kind.m = &la.cm
		return &la.ca_kind
	case 1:
		la.ca_epoch.w = &la.w.epoch
		la.ca_epoch.m = &la.cm
		return &la.ca_epoch
	case 2:
		la.ca_subsets.w = &la.w.subsets
		la.ca_subsets.m = &la.cm
		return &la.ca_subsets
	default:
		panic("unreachable")
	}
}
func (la *_Epoch__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_Epoch__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}

func (n Float) Float() float64 {
	return n.x
}
func (_Float__Prototype) FromFloat(v float64) (Float, error) {
	n := _Float{v}
	return &n, nil
}

type _Float__Maybe struct {
	m schema.Maybe
	v _Float
}
type MaybeFloat = *_Float__Maybe

func (m MaybeFloat) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeFloat) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeFloat) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeFloat) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeFloat) Must() Float {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (Float)(&_Float{})
var _ schema.TypedNode = (Float)(&_Float{})

func (Float) Kind() datamodel.Kind {
	return datamodel.Kind_Float
}
func (Float) LookupByString(string) (datamodel.Node, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.LookupByString("")
}
func (Float) LookupByNode(datamodel.Node) (datamodel.Node, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.LookupByNode(nil)
}
func (Float) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.LookupByIndex(0)
}
func (Float) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.LookupBySegment(seg)
}
func (Float) MapIterator() datamodel.MapIterator {
	return nil
}
func (Float) ListIterator() datamodel.ListIterator {
	return nil
}
func (Float) Length() int64 {
	return -1
}
func (Float) IsAbsent() bool {
	return false
}
func (Float) IsNull() bool {
	return false
}
func (Float) AsBool() (bool, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.AsBool()
}
func (Float) AsInt() (int64, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.AsInt()
}
func (n Float) AsFloat() (float64, error) {
	return n.x, nil
}
func (Float) AsString() (string, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.AsString()
}
func (Float) AsBytes() ([]byte, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.AsBytes()
}
func (Float) AsLink() (datamodel.Link, error) {
	return mixins.Float{TypeName: "ipldsch.Float"}.AsLink()
}
func (Float) Prototype() datamodel.NodePrototype {
	return _Float__Prototype{}
}

type _Float__Prototype struct{}

func (_Float__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Float__Builder
	nb.Reset()
	return &nb
}

type _Float__Builder struct {
	_Float__Assembler
}

func (nb *_Float__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Float__Builder) Reset() {
	var w _Float
	var m schema.Maybe
	*nb = _Float__Builder{_Float__Assembler{w: &w, m: &m}}
}

type _Float__Assembler struct {
	w *_Float
	m *schema.Maybe
}

func (na *_Float__Assembler) reset() {}
func (_Float__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.FloatAssembler{TypeName: "ipldsch.Float"}.BeginMap(0)
}
func (_Float__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.FloatAssembler{TypeName: "ipldsch.Float"}.BeginList(0)
}
func (na *_Float__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.FloatAssembler{TypeName: "ipldsch.Float"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	panic("unreachable")
}
func (_Float__Assembler) AssignBool(bool) error {
	return mixins.FloatAssembler{TypeName: "ipldsch.Float"}.AssignBool(false)
}
func (_Float__Assembler) AssignInt(int64) error {
	return mixins.FloatAssembler{TypeName: "ipldsch.Float"}.AssignInt(0)
}
func (na *_Float__Assembler) AssignFloat(v float64) error {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	na.w.x = v
	*na.m = schema.Maybe_Value
	return nil
}
func (_Float__Assembler) AssignString(string) error {
	return mixins.FloatAssembler{TypeName: "ipldsch.Float"}.AssignString("")
}
func (_Float__Assembler) AssignBytes([]byte) error {
	return mixins.FloatAssembler{TypeName: "ipldsch.Float"}.AssignBytes(nil)
}
func (_Float__Assembler) AssignLink(datamodel.Link) error {
	return mixins.FloatAssembler{TypeName: "ipldsch.Float"}.AssignLink(nil)
}
func (na *_Float__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Float); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v2, err := v.AsFloat(); err != nil {
		return err
	} else {
		return na.AssignFloat(v2)
	}
}
func (_Float__Assembler) Prototype() datamodel.NodePrototype {
	return _Float__Prototype{}
}
func (Float) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Float) Representation() datamodel.Node {
	return (*_Float__Repr)(n)
}

type _Float__Repr = _Float

var _ datamodel.Node = &_Float__Repr{}

type _Float__ReprPrototype = _Float__Prototype
type _Float__ReprAssembler = _Float__Assembler

func (n Hash) Bytes() []byte {
	return n.x
}
func (_Hash__Prototype) FromBytes(v []byte) (Hash, error) {
	n := _Hash{v}
	return &n, nil
}

type _Hash__Maybe struct {
	m schema.Maybe
	v _Hash
}
type MaybeHash = *_Hash__Maybe

func (m MaybeHash) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeHash) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeHash) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeHash) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeHash) Must() Hash {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (Hash)(&_Hash{})
var _ schema.TypedNode = (Hash)(&_Hash{})

func (Hash) Kind() datamodel.Kind {
	return datamodel.Kind_Bytes
}
func (Hash) LookupByString(string) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.LookupByString("")
}
func (Hash) LookupByNode(datamodel.Node) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.LookupByNode(nil)
}
func (Hash) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.LookupByIndex(0)
}
func (Hash) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.LookupBySegment(seg)
}
func (Hash) MapIterator() datamodel.MapIterator {
	return nil
}
func (Hash) ListIterator() datamodel.ListIterator {
	return nil
}
func (Hash) Length() int64 {
	return -1
}
func (Hash) IsAbsent() bool {
	return false
}
func (Hash) IsNull() bool {
	return false
}
func (Hash) AsBool() (bool, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.AsBool()
}
func (Hash) AsInt() (int64, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.AsInt()
}
func (Hash) AsFloat() (float64, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.AsFloat()
}
func (Hash) AsString() (string, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.AsString()
}
func (n Hash) AsBytes() ([]byte, error) {
	return n.x, nil
}
func (Hash) AsLink() (datamodel.Link, error) {
	return mixins.Bytes{TypeName: "ipldsch.Hash"}.AsLink()
}
func (Hash) Prototype() datamodel.NodePrototype {
	return _Hash__Prototype{}
}

type _Hash__Prototype struct{}

func (_Hash__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Hash__Builder
	nb.Reset()
	return &nb
}

type _Hash__Builder struct {
	_Hash__Assembler
}

func (nb *_Hash__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Hash__Builder) Reset() {
	var w _Hash
	var m schema.Maybe
	*nb = _Hash__Builder{_Hash__Assembler{w: &w, m: &m}}
}

type _Hash__Assembler struct {
	w *_Hash
	m *schema.Maybe
}

func (na *_Hash__Assembler) reset() {}
func (_Hash__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.BytesAssembler{TypeName: "ipldsch.Hash"}.BeginMap(0)
}
func (_Hash__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.BytesAssembler{TypeName: "ipldsch.Hash"}.BeginList(0)
}
func (na *_Hash__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.BytesAssembler{TypeName: "ipldsch.Hash"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	panic("unreachable")
}
func (_Hash__Assembler) AssignBool(bool) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Hash"}.AssignBool(false)
}
func (_Hash__Assembler) AssignInt(int64) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Hash"}.AssignInt(0)
}
func (_Hash__Assembler) AssignFloat(float64) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Hash"}.AssignFloat(0)
}
func (_Hash__Assembler) AssignString(string) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Hash"}.AssignString("")
}
func (na *_Hash__Assembler) AssignBytes(v []byte) error {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	na.w.x = v
	*na.m = schema.Maybe_Value
	return nil
}
func (_Hash__Assembler) AssignLink(datamodel.Link) error {
	return mixins.BytesAssembler{TypeName: "ipldsch.Hash"}.AssignLink(nil)
}
func (na *_Hash__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Hash); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v2, err := v.AsBytes(); err != nil {
		return err
	} else {
		return na.AssignBytes(v2)
	}
}
func (_Hash__Assembler) Prototype() datamodel.NodePrototype {
	return _Hash__Prototype{}
}
func (Hash) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Hash) Representation() datamodel.Node {
	return (*_Hash__Repr)(n)
}

type _Hash__Repr = _Hash

var _ datamodel.Node = &_Hash__Repr{}

type _Hash__ReprPrototype = _Hash__Prototype
type _Hash__ReprAssembler = _Hash__Assembler

func (n Int) Int() int64 {
	return n.x
}
func (_Int__Prototype) FromInt(v int64) (Int, error) {
	n := _Int{v}
	return &n, nil
}

type _Int__Maybe struct {
	m schema.Maybe
	v _Int
}
type MaybeInt = *_Int__Maybe

func (m MaybeInt) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeInt) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeInt) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeInt) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeInt) Must() Int {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (Int)(&_Int{})
var _ schema.TypedNode = (Int)(&_Int{})

func (Int) Kind() datamodel.Kind {
	return datamodel.Kind_Int
}
func (Int) LookupByString(string) (datamodel.Node, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.LookupByString("")
}
func (Int) LookupByNode(datamodel.Node) (datamodel.Node, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.LookupByNode(nil)
}
func (Int) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.LookupByIndex(0)
}
func (Int) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.LookupBySegment(seg)
}
func (Int) MapIterator() datamodel.MapIterator {
	return nil
}
func (Int) ListIterator() datamodel.ListIterator {
	return nil
}
func (Int) Length() int64 {
	return -1
}
func (Int) IsAbsent() bool {
	return false
}
func (Int) IsNull() bool {
	return false
}
func (Int) AsBool() (bool, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.AsBool()
}
func (n Int) AsInt() (int64, error) {
	return n.x, nil
}
func (Int) AsFloat() (float64, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.AsFloat()
}
func (Int) AsString() (string, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.AsString()
}
func (Int) AsBytes() ([]byte, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.AsBytes()
}
func (Int) AsLink() (datamodel.Link, error) {
	return mixins.Int{TypeName: "ipldsch.Int"}.AsLink()
}
func (Int) Prototype() datamodel.NodePrototype {
	return _Int__Prototype{}
}

type _Int__Prototype struct{}

func (_Int__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Int__Builder
	nb.Reset()
	return &nb
}

type _Int__Builder struct {
	_Int__Assembler
}

func (nb *_Int__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Int__Builder) Reset() {
	var w _Int
	var m schema.Maybe
	*nb = _Int__Builder{_Int__Assembler{w: &w, m: &m}}
}

type _Int__Assembler struct {
	w *_Int
	m *schema.Maybe
}

func (na *_Int__Assembler) reset() {}
func (_Int__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.IntAssembler{TypeName: "ipldsch.Int"}.BeginMap(0)
}
func (_Int__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.IntAssembler{TypeName: "ipldsch.Int"}.BeginList(0)
}
func (na *_Int__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.IntAssembler{TypeName: "ipldsch.Int"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	panic("unreachable")
}
func (_Int__Assembler) AssignBool(bool) error {
	return mixins.IntAssembler{TypeName: "ipldsch.Int"}.AssignBool(false)
}
func (na *_Int__Assembler) AssignInt(v int64) error {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	na.w.x = v
	*na.m = schema.Maybe_Value
	return nil
}
func (_Int__Assembler) AssignFloat(float64) error {
	return mixins.IntAssembler{TypeName: "ipldsch.Int"}.AssignFloat(0)
}
func (_Int__Assembler) AssignString(string) error {
	return mixins.IntAssembler{TypeName: "ipldsch.Int"}.AssignString("")
}
func (_Int__Assembler) AssignBytes([]byte) error {
	return mixins.IntAssembler{TypeName: "ipldsch.Int"}.AssignBytes(nil)
}
func (_Int__Assembler) AssignLink(datamodel.Link) error {
	return mixins.IntAssembler{TypeName: "ipldsch.Int"}.AssignLink(nil)
}
func (na *_Int__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Int); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v2, err := v.AsInt(); err != nil {
		return err
	} else {
		return na.AssignInt(v2)
	}
}
func (_Int__Assembler) Prototype() datamodel.NodePrototype {
	return _Int__Prototype{}
}
func (Int) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Int) Representation() datamodel.Node {
	return (*_Int__Repr)(n)
}

type _Int__Repr = _Int

var _ datamodel.Node = &_Int__Repr{}

type _Int__ReprPrototype = _Int__Prototype
type _Int__ReprAssembler = _Int__Assembler

func (n Link) Link() datamodel.Link {
	return n.x
}
func (_Link__Prototype) FromLink(v datamodel.Link) (Link, error) {
	n := _Link{v}
	return &n, nil
}

type _Link__Maybe struct {
	m schema.Maybe
	v _Link
}
type MaybeLink = *_Link__Maybe

func (m MaybeLink) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeLink) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeLink) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeLink) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeLink) Must() Link {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (Link)(&_Link{})
var _ schema.TypedNode = (Link)(&_Link{})

func (Link) Kind() datamodel.Kind {
	return datamodel.Kind_Link
}
func (Link) LookupByString(string) (datamodel.Node, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.LookupByString("")
}
func (Link) LookupByNode(datamodel.Node) (datamodel.Node, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.LookupByNode(nil)
}
func (Link) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.LookupByIndex(0)
}
func (Link) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.LookupBySegment(seg)
}
func (Link) MapIterator() datamodel.MapIterator {
	return nil
}
func (Link) ListIterator() datamodel.ListIterator {
	return nil
}
func (Link) Length() int64 {
	return -1
}
func (Link) IsAbsent() bool {
	return false
}
func (Link) IsNull() bool {
	return false
}
func (Link) AsBool() (bool, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.AsBool()
}
func (Link) AsInt() (int64, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.AsInt()
}
func (Link) AsFloat() (float64, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.AsFloat()
}
func (Link) AsString() (string, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.AsString()
}
func (Link) AsBytes() ([]byte, error) {
	return mixins.Link{TypeName: "ipldsch.Link"}.AsBytes()
}
func (n Link) AsLink() (datamodel.Link, error) {
	return n.x, nil
}
func (Link) Prototype() datamodel.NodePrototype {
	return _Link__Prototype{}
}

type _Link__Prototype struct{}

func (_Link__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Link__Builder
	nb.Reset()
	return &nb
}

type _Link__Builder struct {
	_Link__Assembler
}

func (nb *_Link__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Link__Builder) Reset() {
	var w _Link
	var m schema.Maybe
	*nb = _Link__Builder{_Link__Assembler{w: &w, m: &m}}
}

type _Link__Assembler struct {
	w *_Link
	m *schema.Maybe
}

func (na *_Link__Assembler) reset() {}
func (_Link__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.LinkAssembler{TypeName: "ipldsch.Link"}.BeginMap(0)
}
func (_Link__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.LinkAssembler{TypeName: "ipldsch.Link"}.BeginList(0)
}
func (na *_Link__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.LinkAssembler{TypeName: "ipldsch.Link"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	panic("unreachable")
}
func (_Link__Assembler) AssignBool(bool) error {
	return mixins.LinkAssembler{TypeName: "ipldsch.Link"}.AssignBool(false)
}
func (_Link__Assembler) AssignInt(int64) error {
	return mixins.LinkAssembler{TypeName: "ipldsch.Link"}.AssignInt(0)
}
func (_Link__Assembler) AssignFloat(float64) error {
	return mixins.LinkAssembler{TypeName: "ipldsch.Link"}.AssignFloat(0)
}
func (_Link__Assembler) AssignString(string) error {
	return mixins.LinkAssembler{TypeName: "ipldsch.Link"}.AssignString("")
}
func (_Link__Assembler) AssignBytes([]byte) error {
	return mixins.LinkAssembler{TypeName: "ipldsch.Link"}.AssignBytes(nil)
}
func (na *_Link__Assembler) AssignLink(v datamodel.Link) error {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	na.w.x = v
	*na.m = schema.Maybe_Value
	return nil
}
func (na *_Link__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Link); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v2, err := v.AsLink(); err != nil {
		return err
	} else {
		return na.AssignLink(v2)
	}
}
func (_Link__Assembler) Prototype() datamodel.NodePrototype {
	return _Link__Prototype{}
}
func (Link) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Link) Representation() datamodel.Node {
	return (*_Link__Repr)(n)
}

type _Link__Repr = _Link

var _ datamodel.Node = &_Link__Repr{}

type _Link__ReprPrototype = _Link__Prototype
type _Link__ReprAssembler = _Link__Assembler

func (n *_List__Link) Lookup(idx int64) Link {
	if n.Length() <= idx {
		return nil
	}
	v := &n.x[idx]
	return v
}
func (n *_List__Link) LookupMaybe(idx int64) MaybeLink {
	if n.Length() <= idx {
		return nil
	}
	v := &n.x[idx]
	return &_Link__Maybe{
		m: schema.Maybe_Value,
		v: *v,
	}
}

var _List__Link__valueAbsent = _Link__Maybe{m: schema.Maybe_Absent}

func (n List__Link) Iterator() *List__Link__Itr {
	return &List__Link__Itr{n, 0}
}

type List__Link__Itr struct {
	n   List__Link
	idx int
}

func (itr *List__Link__Itr) Next() (idx int64, v Link) {
	if itr.idx >= len(itr.n.x) {
		return -1, nil
	}
	idx = int64(itr.idx)
	v = &itr.n.x[itr.idx]
	itr.idx++
	return
}
func (itr *List__Link__Itr) Done() bool {
	return itr.idx >= len(itr.n.x)
}

type _List__Link__Maybe struct {
	m schema.Maybe
	v _List__Link
}
type MaybeList__Link = *_List__Link__Maybe

func (m MaybeList__Link) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeList__Link) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeList__Link) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeList__Link) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeList__Link) Must() List__Link {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (List__Link)(&_List__Link{})
var _ schema.TypedNode = (List__Link)(&_List__Link{})

func (List__Link) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (List__Link) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.List__Link"}.LookupByString("")
}
func (n List__Link) LookupByNode(k datamodel.Node) (datamodel.Node, error) {
	idx, err := k.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(idx)
}
func (n List__Link) LookupByIndex(idx int64) (datamodel.Node, error) {
	if n.Length() <= idx {
		return nil, datamodel.ErrNotExists{Segment: datamodel.PathSegmentOfInt(idx)}
	}
	v := &n.x[idx]
	return v, nil
}
func (n List__Link) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.List__Link", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (List__Link) MapIterator() datamodel.MapIterator {
	return nil
}
func (n List__Link) ListIterator() datamodel.ListIterator {
	return &_List__Link__ListItr{n, 0}
}

type _List__Link__ListItr struct {
	n   List__Link
	idx int
}

func (itr *_List__Link__ListItr) Next() (idx int64, v datamodel.Node, _ error) {
	if itr.idx >= len(itr.n.x) {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	idx = int64(itr.idx)
	x := &itr.n.x[itr.idx]
	v = x
	itr.idx++
	return
}
func (itr *_List__Link__ListItr) Done() bool {
	return itr.idx >= len(itr.n.x)
}

func (n List__Link) Length() int64 {
	return int64(len(n.x))
}
func (List__Link) IsAbsent() bool {
	return false
}
func (List__Link) IsNull() bool {
	return false
}
func (List__Link) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.List__Link"}.AsBool()
}
func (List__Link) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.List__Link"}.AsInt()
}
func (List__Link) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.List__Link"}.AsFloat()
}
func (List__Link) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.List__Link"}.AsString()
}
func (List__Link) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.List__Link"}.AsBytes()
}
func (List__Link) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.List__Link"}.AsLink()
}
func (List__Link) Prototype() datamodel.NodePrototype {
	return _List__Link__Prototype{}
}

type _List__Link__Prototype struct{}

func (_List__Link__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _List__Link__Builder
	nb.Reset()
	return &nb
}

type _List__Link__Builder struct {
	_List__Link__Assembler
}

func (nb *_List__Link__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_List__Link__Builder) Reset() {
	var w _List__Link
	var m schema.Maybe
	*nb = _List__Link__Builder{_List__Link__Assembler{w: &w, m: &m}}
}

type _List__Link__Assembler struct {
	w     *_List__Link
	m     *schema.Maybe
	state laState

	cm schema.Maybe
	va _Link__Assembler
}

func (na *_List__Link__Assembler) reset() {
	na.state = laState_initial
	na.va.reset()
}
func (_List__Link__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link"}.BeginMap(0)
}
func (na *_List__Link__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if sizeHint < 0 {
		sizeHint = 0
	}
	if sizeHint > 0 {
		na.w.x = make([]_Link, 0, sizeHint)
	}
	return na, nil
}
func (na *_List__Link__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.List__Link"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_List__Link__Assembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link"}.AssignBool(false)
}
func (_List__Link__Assembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link"}.AssignInt(0)
}
func (_List__Link__Assembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link"}.AssignFloat(0)
}
func (_List__Link__Assembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link"}.AssignString("")
}
func (_List__Link__Assembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link"}.AssignBytes(nil)
}
func (_List__Link__Assembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link"}.AssignLink(nil)
}
func (na *_List__Link__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_List__Link); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.List__Link", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_List__Link__Assembler) Prototype() datamodel.NodePrototype {
	return _List__Link__Prototype{}
}
func (la *_List__Link__Assembler) valueFinishTidy() bool {
	switch la.cm {
	case schema.Maybe_Value:
		la.va.w = nil
		la.cm = schema.Maybe_Absent
		la.state = laState_initial
		la.va.reset()
		return true
	default:
		return false
	}
}
func (la *_List__Link__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	la.w.x = append(la.w.x, _Link{})
	la.state = laState_midValue
	row := &la.w.x[len(la.w.x)-1]
	la.va.w = row
	la.va.m = &la.cm
	return &la.va
}
func (la *_List__Link__Assembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_List__Link__Assembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	return _Link__Prototype{}
}
func (List__Link) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n List__Link) Representation() datamodel.Node {
	return (*_List__Link__Repr)(n)
}

type _List__Link__Repr _List__Link

var _ datamodel.Node = &_List__Link__Repr{}

func (_List__Link__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_List__Link__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.List__Link.Repr"}.LookupByString("")
}
func (nr *_List__Link__Repr) LookupByNode(k datamodel.Node) (datamodel.Node, error) {
	v, err := (List__Link)(nr).LookupByNode(k)
	if err != nil || v == datamodel.Null {
		return v, err
	}
	return v.(Link).Representation(), nil
}
func (nr *_List__Link__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	v, err := (List__Link)(nr).LookupByIndex(idx)
	if err != nil || v == datamodel.Null {
		return v, err
	}
	return v.(Link).Representation(), nil
}
func (n _List__Link__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.List__Link.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_List__Link__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (nr *_List__Link__Repr) ListIterator() datamodel.ListIterator {
	return &_List__Link__ReprListItr{(List__Link)(nr), 0}
}

type _List__Link__ReprListItr _List__Link__ListItr

func (itr *_List__Link__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	idx, v, err = (*_List__Link__ListItr)(itr).Next()
	if err != nil || v == datamodel.Null {
		return
	}
	return idx, v.(Link).Representation(), nil
}
func (itr *_List__Link__ReprListItr) Done() bool {
	return (*_List__Link__ListItr)(itr).Done()
}

func (rn *_List__Link__Repr) Length() int64 {
	return int64(len(rn.x))
}
func (_List__Link__Repr) IsAbsent() bool {
	return false
}
func (_List__Link__Repr) IsNull() bool {
	return false
}
func (_List__Link__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.List__Link.Repr"}.AsBool()
}
func (_List__Link__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.List__Link.Repr"}.AsInt()
}
func (_List__Link__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.List__Link.Repr"}.AsFloat()
}
func (_List__Link__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.List__Link.Repr"}.AsString()
}
func (_List__Link__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.List__Link.Repr"}.AsBytes()
}
func (_List__Link__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.List__Link.Repr"}.AsLink()
}
func (_List__Link__Repr) Prototype() datamodel.NodePrototype {
	return _List__Link__ReprPrototype{}
}

type _List__Link__ReprPrototype struct{}

func (_List__Link__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _List__Link__ReprBuilder
	nb.Reset()
	return &nb
}

type _List__Link__ReprBuilder struct {
	_List__Link__ReprAssembler
}

func (nb *_List__Link__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_List__Link__ReprBuilder) Reset() {
	var w _List__Link
	var m schema.Maybe
	*nb = _List__Link__ReprBuilder{_List__Link__ReprAssembler{w: &w, m: &m}}
}

type _List__Link__ReprAssembler struct {
	w     *_List__Link
	m     *schema.Maybe
	state laState

	cm schema.Maybe
	va _Link__ReprAssembler
}

func (na *_List__Link__ReprAssembler) reset() {
	na.state = laState_initial
	na.va.reset()
}
func (_List__Link__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link.Repr"}.BeginMap(0)
}
func (na *_List__Link__ReprAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if sizeHint < 0 {
		sizeHint = 0
	}
	if sizeHint > 0 {
		na.w.x = make([]_Link, 0, sizeHint)
	}
	return na, nil
}
func (na *_List__Link__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.List__Link.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_List__Link__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link.Repr"}.AssignBool(false)
}
func (_List__Link__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link.Repr"}.AssignInt(0)
}
func (_List__Link__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link.Repr"}.AssignFloat(0)
}
func (_List__Link__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link.Repr"}.AssignString("")
}
func (_List__Link__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link.Repr"}.AssignBytes(nil)
}
func (_List__Link__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Link.Repr"}.AssignLink(nil)
}
func (na *_List__Link__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_List__Link); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.List__Link.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_List__Link__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _List__Link__ReprPrototype{}
}
func (la *_List__Link__ReprAssembler) valueFinishTidy() bool {
	switch la.cm {
	case schema.Maybe_Value:
		la.va.w = nil
		la.cm = schema.Maybe_Absent
		la.state = laState_initial
		la.va.reset()
		return true
	default:
		return false
	}
}
func (la *_List__Link__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	la.w.x = append(la.w.x, _Link{})
	la.state = laState_midValue
	row := &la.w.x[len(la.w.x)-1]
	la.va.w = row
	la.va.m = &la.cm
	return &la.va
}
func (la *_List__Link__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_List__Link__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	return _Link__ReprPrototype{}
}

func (n *_List__Shredding) Lookup(idx int64) Shredding {
	if n.Length() <= idx {
		return nil
	}
	v := &n.x[idx]
	return v
}
func (n *_List__Shredding) LookupMaybe(idx int64) MaybeShredding {
	if n.Length() <= idx {
		return nil
	}
	v := &n.x[idx]
	return &_Shredding__Maybe{
		m: schema.Maybe_Value,
		v: v,
	}
}

var _List__Shredding__valueAbsent = _Shredding__Maybe{m: schema.Maybe_Absent}

func (n List__Shredding) Iterator() *List__Shredding__Itr {
	return &List__Shredding__Itr{n, 0}
}

type List__Shredding__Itr struct {
	n   List__Shredding
	idx int
}

func (itr *List__Shredding__Itr) Next() (idx int64, v Shredding) {
	if itr.idx >= len(itr.n.x) {
		return -1, nil
	}
	idx = int64(itr.idx)
	v = &itr.n.x[itr.idx]
	itr.idx++
	return
}
func (itr *List__Shredding__Itr) Done() bool {
	return itr.idx >= len(itr.n.x)
}

type _List__Shredding__Maybe struct {
	m schema.Maybe
	v _List__Shredding
}
type MaybeList__Shredding = *_List__Shredding__Maybe

func (m MaybeList__Shredding) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeList__Shredding) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeList__Shredding) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeList__Shredding) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeList__Shredding) Must() List__Shredding {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (List__Shredding)(&_List__Shredding{})
var _ schema.TypedNode = (List__Shredding)(&_List__Shredding{})

func (List__Shredding) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (List__Shredding) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding"}.LookupByString("")
}
func (n List__Shredding) LookupByNode(k datamodel.Node) (datamodel.Node, error) {
	idx, err := k.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(idx)
}
func (n List__Shredding) LookupByIndex(idx int64) (datamodel.Node, error) {
	if n.Length() <= idx {
		return nil, datamodel.ErrNotExists{Segment: datamodel.PathSegmentOfInt(idx)}
	}
	v := &n.x[idx]
	return v, nil
}
func (n List__Shredding) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.List__Shredding", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (List__Shredding) MapIterator() datamodel.MapIterator {
	return nil
}
func (n List__Shredding) ListIterator() datamodel.ListIterator {
	return &_List__Shredding__ListItr{n, 0}
}

type _List__Shredding__ListItr struct {
	n   List__Shredding
	idx int
}

func (itr *_List__Shredding__ListItr) Next() (idx int64, v datamodel.Node, _ error) {
	if itr.idx >= len(itr.n.x) {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	idx = int64(itr.idx)
	x := &itr.n.x[itr.idx]
	v = x
	itr.idx++
	return
}
func (itr *_List__Shredding__ListItr) Done() bool {
	return itr.idx >= len(itr.n.x)
}

func (n List__Shredding) Length() int64 {
	return int64(len(n.x))
}
func (List__Shredding) IsAbsent() bool {
	return false
}
func (List__Shredding) IsNull() bool {
	return false
}
func (List__Shredding) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding"}.AsBool()
}
func (List__Shredding) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding"}.AsInt()
}
func (List__Shredding) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding"}.AsFloat()
}
func (List__Shredding) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding"}.AsString()
}
func (List__Shredding) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding"}.AsBytes()
}
func (List__Shredding) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding"}.AsLink()
}
func (List__Shredding) Prototype() datamodel.NodePrototype {
	return _List__Shredding__Prototype{}
}

type _List__Shredding__Prototype struct{}

func (_List__Shredding__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _List__Shredding__Builder
	nb.Reset()
	return &nb
}

type _List__Shredding__Builder struct {
	_List__Shredding__Assembler
}

func (nb *_List__Shredding__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_List__Shredding__Builder) Reset() {
	var w _List__Shredding
	var m schema.Maybe
	*nb = _List__Shredding__Builder{_List__Shredding__Assembler{w: &w, m: &m}}
}

type _List__Shredding__Assembler struct {
	w     *_List__Shredding
	m     *schema.Maybe
	state laState

	cm schema.Maybe
	va _Shredding__Assembler
}

func (na *_List__Shredding__Assembler) reset() {
	na.state = laState_initial
	na.va.reset()
}
func (_List__Shredding__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding"}.BeginMap(0)
}
func (na *_List__Shredding__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if sizeHint < 0 {
		sizeHint = 0
	}
	if sizeHint > 0 {
		na.w.x = make([]_Shredding, 0, sizeHint)
	}
	return na, nil
}
func (na *_List__Shredding__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_List__Shredding__Assembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding"}.AssignBool(false)
}
func (_List__Shredding__Assembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding"}.AssignInt(0)
}
func (_List__Shredding__Assembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding"}.AssignFloat(0)
}
func (_List__Shredding__Assembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding"}.AssignString("")
}
func (_List__Shredding__Assembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding"}.AssignBytes(nil)
}
func (_List__Shredding__Assembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding"}.AssignLink(nil)
}
func (na *_List__Shredding__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_List__Shredding); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.List__Shredding", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_List__Shredding__Assembler) Prototype() datamodel.NodePrototype {
	return _List__Shredding__Prototype{}
}
func (la *_List__Shredding__Assembler) valueFinishTidy() bool {
	switch la.cm {
	case schema.Maybe_Value:
		la.va.w = nil
		la.cm = schema.Maybe_Absent
		la.state = laState_initial
		la.va.reset()
		return true
	default:
		return false
	}
}
func (la *_List__Shredding__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	la.w.x = append(la.w.x, _Shredding{})
	la.state = laState_midValue
	row := &la.w.x[len(la.w.x)-1]
	la.va.w = row
	la.va.m = &la.cm
	return &la.va
}
func (la *_List__Shredding__Assembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_List__Shredding__Assembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	return _Shredding__Prototype{}
}
func (List__Shredding) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n List__Shredding) Representation() datamodel.Node {
	return (*_List__Shredding__Repr)(n)
}

type _List__Shredding__Repr _List__Shredding

var _ datamodel.Node = &_List__Shredding__Repr{}

func (_List__Shredding__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_List__Shredding__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding.Repr"}.LookupByString("")
}
func (nr *_List__Shredding__Repr) LookupByNode(k datamodel.Node) (datamodel.Node, error) {
	v, err := (List__Shredding)(nr).LookupByNode(k)
	if err != nil || v == datamodel.Null {
		return v, err
	}
	return v.(Shredding).Representation(), nil
}
func (nr *_List__Shredding__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	v, err := (List__Shredding)(nr).LookupByIndex(idx)
	if err != nil || v == datamodel.Null {
		return v, err
	}
	return v.(Shredding).Representation(), nil
}
func (n _List__Shredding__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.List__Shredding.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_List__Shredding__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (nr *_List__Shredding__Repr) ListIterator() datamodel.ListIterator {
	return &_List__Shredding__ReprListItr{(List__Shredding)(nr), 0}
}

type _List__Shredding__ReprListItr _List__Shredding__ListItr

func (itr *_List__Shredding__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	idx, v, err = (*_List__Shredding__ListItr)(itr).Next()
	if err != nil || v == datamodel.Null {
		return
	}
	return idx, v.(Shredding).Representation(), nil
}
func (itr *_List__Shredding__ReprListItr) Done() bool {
	return (*_List__Shredding__ListItr)(itr).Done()
}

func (rn *_List__Shredding__Repr) Length() int64 {
	return int64(len(rn.x))
}
func (_List__Shredding__Repr) IsAbsent() bool {
	return false
}
func (_List__Shredding__Repr) IsNull() bool {
	return false
}
func (_List__Shredding__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding.Repr"}.AsBool()
}
func (_List__Shredding__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding.Repr"}.AsInt()
}
func (_List__Shredding__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding.Repr"}.AsFloat()
}
func (_List__Shredding__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding.Repr"}.AsString()
}
func (_List__Shredding__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding.Repr"}.AsBytes()
}
func (_List__Shredding__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.List__Shredding.Repr"}.AsLink()
}
func (_List__Shredding__Repr) Prototype() datamodel.NodePrototype {
	return _List__Shredding__ReprPrototype{}
}

type _List__Shredding__ReprPrototype struct{}

func (_List__Shredding__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _List__Shredding__ReprBuilder
	nb.Reset()
	return &nb
}

type _List__Shredding__ReprBuilder struct {
	_List__Shredding__ReprAssembler
}

func (nb *_List__Shredding__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_List__Shredding__ReprBuilder) Reset() {
	var w _List__Shredding
	var m schema.Maybe
	*nb = _List__Shredding__ReprBuilder{_List__Shredding__ReprAssembler{w: &w, m: &m}}
}

type _List__Shredding__ReprAssembler struct {
	w     *_List__Shredding
	m     *schema.Maybe
	state laState

	cm schema.Maybe
	va _Shredding__ReprAssembler
}

func (na *_List__Shredding__ReprAssembler) reset() {
	na.state = laState_initial
	na.va.reset()
}
func (_List__Shredding__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding.Repr"}.BeginMap(0)
}
func (na *_List__Shredding__ReprAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if sizeHint < 0 {
		sizeHint = 0
	}
	if sizeHint > 0 {
		na.w.x = make([]_Shredding, 0, sizeHint)
	}
	return na, nil
}
func (na *_List__Shredding__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_List__Shredding__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding.Repr"}.AssignBool(false)
}
func (_List__Shredding__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding.Repr"}.AssignInt(0)
}
func (_List__Shredding__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding.Repr"}.AssignFloat(0)
}
func (_List__Shredding__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding.Repr"}.AssignString("")
}
func (_List__Shredding__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding.Repr"}.AssignBytes(nil)
}
func (_List__Shredding__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.List__Shredding.Repr"}.AssignLink(nil)
}
func (na *_List__Shredding__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_List__Shredding); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.List__Shredding.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_List__Shredding__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _List__Shredding__ReprPrototype{}
}
func (la *_List__Shredding__ReprAssembler) valueFinishTidy() bool {
	switch la.cm {
	case schema.Maybe_Value:
		la.va.w = nil
		la.cm = schema.Maybe_Absent
		la.state = laState_initial
		la.va.reset()
		return true
	default:
		return false
	}
}
func (la *_List__Shredding__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	la.w.x = append(la.w.x, _Shredding{})
	la.state = laState_midValue
	row := &la.w.x[len(la.w.x)-1]
	la.va.w = row
	la.va.m = &la.cm
	return &la.va
}
func (la *_List__Shredding__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_List__Shredding__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	return _Shredding__ReprPrototype{}
}

func (n _Rewards) FieldKind() Int {
	return &n.kind
}
func (n _Rewards) FieldSlot() Int {
	return &n.slot
}
func (n _Rewards) FieldData() DataFrame {
	return &n.data
}

type _Rewards__Maybe struct {
	m schema.Maybe
	v Rewards
}
type MaybeRewards = *_Rewards__Maybe

func (m MaybeRewards) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeRewards) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeRewards) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeRewards) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeRewards) Must() Rewards {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__Rewards_Kind = _String{"kind"}
	fieldName__Rewards_Slot = _String{"slot"}
	fieldName__Rewards_Data = _String{"data"}
)
var _ datamodel.Node = (Rewards)(&_Rewards{})
var _ schema.TypedNode = (Rewards)(&_Rewards{})

func (Rewards) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n Rewards) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "kind":
		return &n.kind, nil
	case "slot":
		return &n.slot, nil
	case "data":
		return &n.data, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n Rewards) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (Rewards) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.Rewards"}.LookupByIndex(0)
}
func (n Rewards) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n Rewards) MapIterator() datamodel.MapIterator {
	return &_Rewards__MapItr{n, 0}
}

type _Rewards__MapItr struct {
	n   Rewards
	idx int
}

func (itr *_Rewards__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 3 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__Rewards_Kind
		v = &itr.n.kind
	case 1:
		k = &fieldName__Rewards_Slot
		v = &itr.n.slot
	case 2:
		k = &fieldName__Rewards_Data
		v = &itr.n.data
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Rewards__MapItr) Done() bool {
	return itr.idx >= 3
}

func (Rewards) ListIterator() datamodel.ListIterator {
	return nil
}
func (Rewards) Length() int64 {
	return 3
}
func (Rewards) IsAbsent() bool {
	return false
}
func (Rewards) IsNull() bool {
	return false
}
func (Rewards) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.Rewards"}.AsBool()
}
func (Rewards) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.Rewards"}.AsInt()
}
func (Rewards) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.Rewards"}.AsFloat()
}
func (Rewards) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.Rewards"}.AsString()
}
func (Rewards) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.Rewards"}.AsBytes()
}
func (Rewards) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.Rewards"}.AsLink()
}
func (Rewards) Prototype() datamodel.NodePrototype {
	return _Rewards__Prototype{}
}

type _Rewards__Prototype struct{}

func (_Rewards__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Rewards__Builder
	nb.Reset()
	return &nb
}

type _Rewards__Builder struct {
	_Rewards__Assembler
}

func (nb *_Rewards__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Rewards__Builder) Reset() {
	var w _Rewards
	var m schema.Maybe
	*nb = _Rewards__Builder{_Rewards__Assembler{w: &w, m: &m}}
}

type _Rewards__Assembler struct {
	w     *_Rewards
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm      schema.Maybe
	ca_kind _Int__Assembler
	ca_slot _Int__Assembler
	ca_data _DataFrame__Assembler
}

func (na *_Rewards__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_kind.reset()
	na.ca_slot.reset()
	na.ca_data.reset()
}

var (
	fieldBit__Rewards_Kind        = 1 << 0
	fieldBit__Rewards_Slot        = 1 << 1
	fieldBit__Rewards_Data        = 1 << 2
	fieldBits__Rewards_sufficient = 0 + 1<<0 + 1<<1 + 1<<2
)

func (na *_Rewards__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Rewards{}
	}
	return na, nil
}
func (_Rewards__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.Rewards"}.BeginList(0)
}
func (na *_Rewards__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.Rewards"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Rewards__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Rewards"}.AssignBool(false)
}
func (_Rewards__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Rewards"}.AssignInt(0)
}
func (_Rewards__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Rewards"}.AssignFloat(0)
}
func (_Rewards__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Rewards"}.AssignString("")
}
func (_Rewards__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Rewards"}.AssignBytes(nil)
}
func (_Rewards__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Rewards"}.AssignLink(nil)
}
func (na *_Rewards__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Rewards); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Rewards", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Rewards__Assembler) Prototype() datamodel.NodePrototype {
	return _Rewards__Prototype{}
}
func (ma *_Rewards__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_kind.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_slot.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 2:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_data.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_Rewards__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "kind":
		if ma.s&fieldBit__Rewards_Kind != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Rewards_Kind}
		}
		ma.s += fieldBit__Rewards_Kind
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind, nil
	case "slot":
		if ma.s&fieldBit__Rewards_Slot != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Rewards_Slot}
		}
		ma.s += fieldBit__Rewards_Slot
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_slot.w = &ma.w.slot
		ma.ca_slot.m = &ma.cm
		return &ma.ca_slot, nil
	case "data":
		if ma.s&fieldBit__Rewards_Data != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Rewards_Data}
		}
		ma.s += fieldBit__Rewards_Data
		ma.state = maState_midValue
		ma.f = 2
		ma.ca_data.w = &ma.w.data
		ma.ca_data.m = &ma.cm
		return &ma.ca_data, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.Rewards", Key: &_String{k}}
}
func (ma *_Rewards__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_Rewards__KeyAssembler)(ma)
}
func (ma *_Rewards__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind
	case 1:
		ma.ca_slot.w = &ma.w.slot
		ma.ca_slot.m = &ma.cm
		return &ma.ca_slot
	case 2:
		ma.ca_data.w = &ma.w.data
		ma.ca_data.m = &ma.cm
		return &ma.ca_data
	default:
		panic("unreachable")
	}
}
func (ma *_Rewards__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__Rewards_sufficient != fieldBits__Rewards_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__Rewards_Kind == 0 {
			err.Missing = append(err.Missing, "kind")
		}
		if ma.s&fieldBit__Rewards_Slot == 0 {
			err.Missing = append(err.Missing, "slot")
		}
		if ma.s&fieldBit__Rewards_Data == 0 {
			err.Missing = append(err.Missing, "data")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_Rewards__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_Rewards__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _Rewards__KeyAssembler _Rewards__Assembler

func (_Rewards__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Rewards.KeyAssembler"}.BeginMap(0)
}
func (_Rewards__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Rewards.KeyAssembler"}.BeginList(0)
}
func (na *_Rewards__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.Rewards.KeyAssembler"}.AssignNull()
}
func (_Rewards__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Rewards.KeyAssembler"}.AssignBool(false)
}
func (_Rewards__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Rewards.KeyAssembler"}.AssignInt(0)
}
func (_Rewards__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Rewards.KeyAssembler"}.AssignFloat(0)
}
func (ka *_Rewards__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "kind":
		if ka.s&fieldBit__Rewards_Kind != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Rewards_Kind}
		}
		ka.s += fieldBit__Rewards_Kind
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "slot":
		if ka.s&fieldBit__Rewards_Slot != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Rewards_Slot}
		}
		ka.s += fieldBit__Rewards_Slot
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	case "data":
		if ka.s&fieldBit__Rewards_Data != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Rewards_Data}
		}
		ka.s += fieldBit__Rewards_Data
		ka.state = maState_expectValue
		ka.f = 2
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.Rewards", Key: &_String{k}}
	}
}
func (_Rewards__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Rewards.KeyAssembler"}.AssignBytes(nil)
}
func (_Rewards__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Rewards.KeyAssembler"}.AssignLink(nil)
}
func (ka *_Rewards__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_Rewards__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (Rewards) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Rewards) Representation() datamodel.Node {
	return (*_Rewards__Repr)(n)
}

type _Rewards__Repr _Rewards

var _ datamodel.Node = &_Rewards__Repr{}

func (_Rewards__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_Rewards__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.Rewards.Repr"}.LookupByString("")
}
func (n *_Rewards__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_Rewards__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.kind.Representation(), nil
	case 1:
		return n.slot.Representation(), nil
	case 2:
		return n.data.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _Rewards__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.Rewards.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_Rewards__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_Rewards__Repr) ListIterator() datamodel.ListIterator {
	return &_Rewards__ReprListItr{n, 0}
}

type _Rewards__ReprListItr struct {
	n   *_Rewards__Repr
	idx int
}

func (itr *_Rewards__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 3 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.kind.Representation()
	case 1:
		idx = int64(itr.idx)
		v = itr.n.slot.Representation()
	case 2:
		idx = int64(itr.idx)
		v = itr.n.data.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Rewards__ReprListItr) Done() bool {
	return itr.idx >= 3
}

func (rn *_Rewards__Repr) Length() int64 {
	l := 3
	return int64(l)
}
func (_Rewards__Repr) IsAbsent() bool {
	return false
}
func (_Rewards__Repr) IsNull() bool {
	return false
}
func (_Rewards__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.Rewards.Repr"}.AsBool()
}
func (_Rewards__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.Rewards.Repr"}.AsInt()
}
func (_Rewards__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.Rewards.Repr"}.AsFloat()
}
func (_Rewards__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.Rewards.Repr"}.AsString()
}
func (_Rewards__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.Rewards.Repr"}.AsBytes()
}
func (_Rewards__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.Rewards.Repr"}.AsLink()
}
func (_Rewards__Repr) Prototype() datamodel.NodePrototype {
	return _Rewards__ReprPrototype{}
}

type _Rewards__ReprPrototype struct{}

func (_Rewards__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Rewards__ReprBuilder
	nb.Reset()
	return &nb
}

type _Rewards__ReprBuilder struct {
	_Rewards__ReprAssembler
}

func (nb *_Rewards__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Rewards__ReprBuilder) Reset() {
	var w _Rewards
	var m schema.Maybe
	*nb = _Rewards__ReprBuilder{_Rewards__ReprAssembler{w: &w, m: &m}}
}

type _Rewards__ReprAssembler struct {
	w     *_Rewards
	m     *schema.Maybe
	state laState
	f     int

	cm      schema.Maybe
	ca_kind _Int__ReprAssembler
	ca_slot _Int__ReprAssembler
	ca_data _DataFrame__ReprAssembler
}

func (na *_Rewards__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_kind.reset()
	na.ca_slot.reset()
	na.ca_data.reset()
}
func (_Rewards__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.Rewards.Repr"}.BeginMap(0)
}
func (na *_Rewards__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Rewards{}
	}
	return na, nil
}
func (na *_Rewards__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.Rewards.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Rewards__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Rewards.Repr"}.AssignBool(false)
}
func (_Rewards__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Rewards.Repr"}.AssignInt(0)
}
func (_Rewards__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Rewards.Repr"}.AssignFloat(0)
}
func (_Rewards__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Rewards.Repr"}.AssignString("")
}
func (_Rewards__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Rewards.Repr"}.AssignBytes(nil)
}
func (_Rewards__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Rewards.Repr"}.AssignLink(nil)
}
func (na *_Rewards__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Rewards); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Rewards.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Rewards__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _Rewards__ReprPrototype{}
}
func (la *_Rewards__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 2:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_Rewards__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 3 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(3)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_kind.w = &la.w.kind
		la.ca_kind.m = &la.cm
		return &la.ca_kind
	case 1:
		la.ca_slot.w = &la.w.slot
		la.ca_slot.m = &la.cm
		return &la.ca_slot
	case 2:
		la.ca_data.w = &la.w.data
		la.ca_data.m = &la.cm
		return &la.ca_data
	default:
		panic("unreachable")
	}
}
func (la *_Rewards__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_Rewards__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}

func (n _Shredding) FieldEntryEndIdx() Int {
	return &n.entryEndIdx
}
func (n _Shredding) FieldShredEndIdx() Int {
	return &n.shredEndIdx
}

type _Shredding__Maybe struct {
	m schema.Maybe
	v Shredding
}
type MaybeShredding = *_Shredding__Maybe

func (m MaybeShredding) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeShredding) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeShredding) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeShredding) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeShredding) Must() Shredding {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__Shredding_EntryEndIdx = _String{"entryEndIdx"}
	fieldName__Shredding_ShredEndIdx = _String{"shredEndIdx"}
)
var _ datamodel.Node = (Shredding)(&_Shredding{})
var _ schema.TypedNode = (Shredding)(&_Shredding{})

func (Shredding) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n Shredding) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "entryEndIdx":
		return &n.entryEndIdx, nil
	case "shredEndIdx":
		return &n.shredEndIdx, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n Shredding) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (Shredding) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.Shredding"}.LookupByIndex(0)
}
func (n Shredding) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n Shredding) MapIterator() datamodel.MapIterator {
	return &_Shredding__MapItr{n, 0}
}

type _Shredding__MapItr struct {
	n   Shredding
	idx int
}

func (itr *_Shredding__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 2 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__Shredding_EntryEndIdx
		v = &itr.n.entryEndIdx
	case 1:
		k = &fieldName__Shredding_ShredEndIdx
		v = &itr.n.shredEndIdx
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Shredding__MapItr) Done() bool {
	return itr.idx >= 2
}

func (Shredding) ListIterator() datamodel.ListIterator {
	return nil
}
func (Shredding) Length() int64 {
	return 2
}
func (Shredding) IsAbsent() bool {
	return false
}
func (Shredding) IsNull() bool {
	return false
}
func (Shredding) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.Shredding"}.AsBool()
}
func (Shredding) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.Shredding"}.AsInt()
}
func (Shredding) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.Shredding"}.AsFloat()
}
func (Shredding) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.Shredding"}.AsString()
}
func (Shredding) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.Shredding"}.AsBytes()
}
func (Shredding) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.Shredding"}.AsLink()
}
func (Shredding) Prototype() datamodel.NodePrototype {
	return _Shredding__Prototype{}
}

type _Shredding__Prototype struct{}

func (_Shredding__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Shredding__Builder
	nb.Reset()
	return &nb
}

type _Shredding__Builder struct {
	_Shredding__Assembler
}

func (nb *_Shredding__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Shredding__Builder) Reset() {
	var w _Shredding
	var m schema.Maybe
	*nb = _Shredding__Builder{_Shredding__Assembler{w: &w, m: &m}}
}

type _Shredding__Assembler struct {
	w     *_Shredding
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm             schema.Maybe
	ca_entryEndIdx _Int__Assembler
	ca_shredEndIdx _Int__Assembler
}

func (na *_Shredding__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_entryEndIdx.reset()
	na.ca_shredEndIdx.reset()
}

var (
	fieldBit__Shredding_EntryEndIdx = 1 << 0
	fieldBit__Shredding_ShredEndIdx = 1 << 1
	fieldBits__Shredding_sufficient = 0 + 1<<0 + 1<<1
)

func (na *_Shredding__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Shredding{}
	}
	return na, nil
}
func (_Shredding__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.Shredding"}.BeginList(0)
}
func (na *_Shredding__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.Shredding"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Shredding__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Shredding"}.AssignBool(false)
}
func (_Shredding__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Shredding"}.AssignInt(0)
}
func (_Shredding__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Shredding"}.AssignFloat(0)
}
func (_Shredding__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Shredding"}.AssignString("")
}
func (_Shredding__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Shredding"}.AssignBytes(nil)
}
func (_Shredding__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Shredding"}.AssignLink(nil)
}
func (na *_Shredding__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Shredding); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Shredding", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Shredding__Assembler) Prototype() datamodel.NodePrototype {
	return _Shredding__Prototype{}
}
func (ma *_Shredding__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_entryEndIdx.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_shredEndIdx.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_Shredding__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "entryEndIdx":
		if ma.s&fieldBit__Shredding_EntryEndIdx != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Shredding_EntryEndIdx}
		}
		ma.s += fieldBit__Shredding_EntryEndIdx
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_entryEndIdx.w = &ma.w.entryEndIdx
		ma.ca_entryEndIdx.m = &ma.cm
		return &ma.ca_entryEndIdx, nil
	case "shredEndIdx":
		if ma.s&fieldBit__Shredding_ShredEndIdx != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Shredding_ShredEndIdx}
		}
		ma.s += fieldBit__Shredding_ShredEndIdx
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_shredEndIdx.w = &ma.w.shredEndIdx
		ma.ca_shredEndIdx.m = &ma.cm
		return &ma.ca_shredEndIdx, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.Shredding", Key: &_String{k}}
}
func (ma *_Shredding__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_Shredding__KeyAssembler)(ma)
}
func (ma *_Shredding__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_entryEndIdx.w = &ma.w.entryEndIdx
		ma.ca_entryEndIdx.m = &ma.cm
		return &ma.ca_entryEndIdx
	case 1:
		ma.ca_shredEndIdx.w = &ma.w.shredEndIdx
		ma.ca_shredEndIdx.m = &ma.cm
		return &ma.ca_shredEndIdx
	default:
		panic("unreachable")
	}
}
func (ma *_Shredding__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__Shredding_sufficient != fieldBits__Shredding_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__Shredding_EntryEndIdx == 0 {
			err.Missing = append(err.Missing, "entryEndIdx")
		}
		if ma.s&fieldBit__Shredding_ShredEndIdx == 0 {
			err.Missing = append(err.Missing, "shredEndIdx")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_Shredding__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_Shredding__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _Shredding__KeyAssembler _Shredding__Assembler

func (_Shredding__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Shredding.KeyAssembler"}.BeginMap(0)
}
func (_Shredding__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Shredding.KeyAssembler"}.BeginList(0)
}
func (na *_Shredding__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.Shredding.KeyAssembler"}.AssignNull()
}
func (_Shredding__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Shredding.KeyAssembler"}.AssignBool(false)
}
func (_Shredding__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Shredding.KeyAssembler"}.AssignInt(0)
}
func (_Shredding__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Shredding.KeyAssembler"}.AssignFloat(0)
}
func (ka *_Shredding__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "entryEndIdx":
		if ka.s&fieldBit__Shredding_EntryEndIdx != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Shredding_EntryEndIdx}
		}
		ka.s += fieldBit__Shredding_EntryEndIdx
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "shredEndIdx":
		if ka.s&fieldBit__Shredding_ShredEndIdx != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Shredding_ShredEndIdx}
		}
		ka.s += fieldBit__Shredding_ShredEndIdx
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.Shredding", Key: &_String{k}}
	}
}
func (_Shredding__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Shredding.KeyAssembler"}.AssignBytes(nil)
}
func (_Shredding__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Shredding.KeyAssembler"}.AssignLink(nil)
}
func (ka *_Shredding__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_Shredding__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (Shredding) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Shredding) Representation() datamodel.Node {
	return (*_Shredding__Repr)(n)
}

type _Shredding__Repr _Shredding

var _ datamodel.Node = &_Shredding__Repr{}

func (_Shredding__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_Shredding__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.Shredding.Repr"}.LookupByString("")
}
func (n *_Shredding__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_Shredding__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.entryEndIdx.Representation(), nil
	case 1:
		return n.shredEndIdx.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _Shredding__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.Shredding.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_Shredding__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_Shredding__Repr) ListIterator() datamodel.ListIterator {
	return &_Shredding__ReprListItr{n, 0}
}

type _Shredding__ReprListItr struct {
	n   *_Shredding__Repr
	idx int
}

func (itr *_Shredding__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 2 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.entryEndIdx.Representation()
	case 1:
		idx = int64(itr.idx)
		v = itr.n.shredEndIdx.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Shredding__ReprListItr) Done() bool {
	return itr.idx >= 2
}

func (rn *_Shredding__Repr) Length() int64 {
	l := 2
	return int64(l)
}
func (_Shredding__Repr) IsAbsent() bool {
	return false
}
func (_Shredding__Repr) IsNull() bool {
	return false
}
func (_Shredding__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.Shredding.Repr"}.AsBool()
}
func (_Shredding__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.Shredding.Repr"}.AsInt()
}
func (_Shredding__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.Shredding.Repr"}.AsFloat()
}
func (_Shredding__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.Shredding.Repr"}.AsString()
}
func (_Shredding__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.Shredding.Repr"}.AsBytes()
}
func (_Shredding__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.Shredding.Repr"}.AsLink()
}
func (_Shredding__Repr) Prototype() datamodel.NodePrototype {
	return _Shredding__ReprPrototype{}
}

type _Shredding__ReprPrototype struct{}

func (_Shredding__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Shredding__ReprBuilder
	nb.Reset()
	return &nb
}

type _Shredding__ReprBuilder struct {
	_Shredding__ReprAssembler
}

func (nb *_Shredding__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Shredding__ReprBuilder) Reset() {
	var w _Shredding
	var m schema.Maybe
	*nb = _Shredding__ReprBuilder{_Shredding__ReprAssembler{w: &w, m: &m}}
}

type _Shredding__ReprAssembler struct {
	w     *_Shredding
	m     *schema.Maybe
	state laState
	f     int

	cm             schema.Maybe
	ca_entryEndIdx _Int__ReprAssembler
	ca_shredEndIdx _Int__ReprAssembler
}

func (na *_Shredding__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_entryEndIdx.reset()
	na.ca_shredEndIdx.reset()
}
func (_Shredding__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.Shredding.Repr"}.BeginMap(0)
}
func (na *_Shredding__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Shredding{}
	}
	return na, nil
}
func (na *_Shredding__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.Shredding.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Shredding__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Shredding.Repr"}.AssignBool(false)
}
func (_Shredding__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Shredding.Repr"}.AssignInt(0)
}
func (_Shredding__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Shredding.Repr"}.AssignFloat(0)
}
func (_Shredding__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Shredding.Repr"}.AssignString("")
}
func (_Shredding__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Shredding.Repr"}.AssignBytes(nil)
}
func (_Shredding__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Shredding.Repr"}.AssignLink(nil)
}
func (na *_Shredding__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Shredding); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Shredding.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Shredding__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _Shredding__ReprPrototype{}
}
func (la *_Shredding__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_Shredding__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 2 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(2)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_entryEndIdx.w = &la.w.entryEndIdx
		la.ca_entryEndIdx.m = &la.cm
		return &la.ca_entryEndIdx
	case 1:
		la.ca_shredEndIdx.w = &la.w.shredEndIdx
		la.ca_shredEndIdx.m = &la.cm
		return &la.ca_shredEndIdx
	default:
		panic("unreachable")
	}
}
func (la *_Shredding__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_Shredding__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}

func (n _SlotMeta) FieldParent_slot() Int {
	return &n.parent_slot
}
func (n _SlotMeta) FieldBlocktime() Int {
	return &n.blocktime
}
func (n _SlotMeta) FieldBlock_height() MaybeInt {
	return &n.block_height
}

type _SlotMeta__Maybe struct {
	m schema.Maybe
	v SlotMeta
}
type MaybeSlotMeta = *_SlotMeta__Maybe

func (m MaybeSlotMeta) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeSlotMeta) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeSlotMeta) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeSlotMeta) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeSlotMeta) Must() SlotMeta {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__SlotMeta_Parent_slot  = _String{"parent_slot"}
	fieldName__SlotMeta_Blocktime    = _String{"blocktime"}
	fieldName__SlotMeta_Block_height = _String{"block_height"}
)
var _ datamodel.Node = (SlotMeta)(&_SlotMeta{})
var _ schema.TypedNode = (SlotMeta)(&_SlotMeta{})

func (SlotMeta) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n SlotMeta) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "parent_slot":
		return &n.parent_slot, nil
	case "blocktime":
		return &n.blocktime, nil
	case "block_height":
		if n.block_height.m == schema.Maybe_Absent {
			return datamodel.Absent, nil
		}
		if n.block_height.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return &n.block_height.v, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n SlotMeta) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (SlotMeta) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.SlotMeta"}.LookupByIndex(0)
}
func (n SlotMeta) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n SlotMeta) MapIterator() datamodel.MapIterator {
	return &_SlotMeta__MapItr{n, 0}
}

type _SlotMeta__MapItr struct {
	n   SlotMeta
	idx int
}

func (itr *_SlotMeta__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 3 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__SlotMeta_Parent_slot
		v = &itr.n.parent_slot
	case 1:
		k = &fieldName__SlotMeta_Blocktime
		v = &itr.n.blocktime
	case 2:
		k = &fieldName__SlotMeta_Block_height
		if itr.n.block_height.m == schema.Maybe_Absent {
			v = datamodel.Absent
			break
		}
		if itr.n.block_height.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = &itr.n.block_height.v
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_SlotMeta__MapItr) Done() bool {
	return itr.idx >= 3
}

func (SlotMeta) ListIterator() datamodel.ListIterator {
	return nil
}
func (SlotMeta) Length() int64 {
	return 3
}
func (SlotMeta) IsAbsent() bool {
	return false
}
func (SlotMeta) IsNull() bool {
	return false
}
func (SlotMeta) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.SlotMeta"}.AsBool()
}
func (SlotMeta) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.SlotMeta"}.AsInt()
}
func (SlotMeta) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.SlotMeta"}.AsFloat()
}
func (SlotMeta) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.SlotMeta"}.AsString()
}
func (SlotMeta) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.SlotMeta"}.AsBytes()
}
func (SlotMeta) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.SlotMeta"}.AsLink()
}
func (SlotMeta) Prototype() datamodel.NodePrototype {
	return _SlotMeta__Prototype{}
}

type _SlotMeta__Prototype struct{}

func (_SlotMeta__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _SlotMeta__Builder
	nb.Reset()
	return &nb
}

type _SlotMeta__Builder struct {
	_SlotMeta__Assembler
}

func (nb *_SlotMeta__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_SlotMeta__Builder) Reset() {
	var w _SlotMeta
	var m schema.Maybe
	*nb = _SlotMeta__Builder{_SlotMeta__Assembler{w: &w, m: &m}}
}

type _SlotMeta__Assembler struct {
	w     *_SlotMeta
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm              schema.Maybe
	ca_parent_slot  _Int__Assembler
	ca_blocktime    _Int__Assembler
	ca_block_height _Int__Assembler
}

func (na *_SlotMeta__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_parent_slot.reset()
	na.ca_blocktime.reset()
	na.ca_block_height.reset()
}

var (
	fieldBit__SlotMeta_Parent_slot  = 1 << 0
	fieldBit__SlotMeta_Blocktime    = 1 << 1
	fieldBit__SlotMeta_Block_height = 1 << 2
	fieldBits__SlotMeta_sufficient  = 0 + 1<<0 + 1<<1
)

func (na *_SlotMeta__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_SlotMeta{}
	}
	return na, nil
}
func (_SlotMeta__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.SlotMeta"}.BeginList(0)
}
func (na *_SlotMeta__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.SlotMeta"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_SlotMeta__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.SlotMeta"}.AssignBool(false)
}
func (_SlotMeta__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.SlotMeta"}.AssignInt(0)
}
func (_SlotMeta__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.SlotMeta"}.AssignFloat(0)
}
func (_SlotMeta__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.SlotMeta"}.AssignString("")
}
func (_SlotMeta__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.SlotMeta"}.AssignBytes(nil)
}
func (_SlotMeta__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.SlotMeta"}.AssignLink(nil)
}
func (na *_SlotMeta__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_SlotMeta); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.SlotMeta", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_SlotMeta__Assembler) Prototype() datamodel.NodePrototype {
	return _SlotMeta__Prototype{}
}
func (ma *_SlotMeta__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_parent_slot.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_blocktime.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 2:
		switch ma.w.block_height.m {
		case schema.Maybe_Null:
			ma.state = maState_initial
			return true
		case schema.Maybe_Value:
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_SlotMeta__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "parent_slot":
		if ma.s&fieldBit__SlotMeta_Parent_slot != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__SlotMeta_Parent_slot}
		}
		ma.s += fieldBit__SlotMeta_Parent_slot
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_parent_slot.w = &ma.w.parent_slot
		ma.ca_parent_slot.m = &ma.cm
		return &ma.ca_parent_slot, nil
	case "blocktime":
		if ma.s&fieldBit__SlotMeta_Blocktime != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__SlotMeta_Blocktime}
		}
		ma.s += fieldBit__SlotMeta_Blocktime
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_blocktime.w = &ma.w.blocktime
		ma.ca_blocktime.m = &ma.cm
		return &ma.ca_blocktime, nil
	case "block_height":
		if ma.s&fieldBit__SlotMeta_Block_height != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__SlotMeta_Block_height}
		}
		ma.s += fieldBit__SlotMeta_Block_height
		ma.state = maState_midValue
		ma.f = 2
		ma.ca_block_height.w = &ma.w.block_height.v
		ma.ca_block_height.m = &ma.w.block_height.m
		ma.w.block_height.m = allowNull
		return &ma.ca_block_height, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.SlotMeta", Key: &_String{k}}
}
func (ma *_SlotMeta__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_SlotMeta__KeyAssembler)(ma)
}
func (ma *_SlotMeta__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_parent_slot.w = &ma.w.parent_slot
		ma.ca_parent_slot.m = &ma.cm
		return &ma.ca_parent_slot
	case 1:
		ma.ca_blocktime.w = &ma.w.blocktime
		ma.ca_blocktime.m = &ma.cm
		return &ma.ca_blocktime
	case 2:
		ma.ca_block_height.w = &ma.w.block_height.v
		ma.ca_block_height.m = &ma.w.block_height.m
		ma.w.block_height.m = allowNull
		return &ma.ca_block_height
	default:
		panic("unreachable")
	}
}
func (ma *_SlotMeta__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__SlotMeta_sufficient != fieldBits__SlotMeta_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__SlotMeta_Parent_slot == 0 {
			err.Missing = append(err.Missing, "parent_slot")
		}
		if ma.s&fieldBit__SlotMeta_Blocktime == 0 {
			err.Missing = append(err.Missing, "blocktime")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_SlotMeta__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_SlotMeta__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _SlotMeta__KeyAssembler _SlotMeta__Assembler

func (_SlotMeta__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.SlotMeta.KeyAssembler"}.BeginMap(0)
}
func (_SlotMeta__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.SlotMeta.KeyAssembler"}.BeginList(0)
}
func (na *_SlotMeta__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.SlotMeta.KeyAssembler"}.AssignNull()
}
func (_SlotMeta__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.SlotMeta.KeyAssembler"}.AssignBool(false)
}
func (_SlotMeta__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.SlotMeta.KeyAssembler"}.AssignInt(0)
}
func (_SlotMeta__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.SlotMeta.KeyAssembler"}.AssignFloat(0)
}
func (ka *_SlotMeta__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "parent_slot":
		if ka.s&fieldBit__SlotMeta_Parent_slot != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__SlotMeta_Parent_slot}
		}
		ka.s += fieldBit__SlotMeta_Parent_slot
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "blocktime":
		if ka.s&fieldBit__SlotMeta_Blocktime != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__SlotMeta_Blocktime}
		}
		ka.s += fieldBit__SlotMeta_Blocktime
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	case "block_height":
		if ka.s&fieldBit__SlotMeta_Block_height != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__SlotMeta_Block_height}
		}
		ka.s += fieldBit__SlotMeta_Block_height
		ka.state = maState_expectValue
		ka.f = 2
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.SlotMeta", Key: &_String{k}}
	}
}
func (_SlotMeta__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.SlotMeta.KeyAssembler"}.AssignBytes(nil)
}
func (_SlotMeta__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.SlotMeta.KeyAssembler"}.AssignLink(nil)
}
func (ka *_SlotMeta__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_SlotMeta__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (SlotMeta) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n SlotMeta) Representation() datamodel.Node {
	return (*_SlotMeta__Repr)(n)
}

type _SlotMeta__Repr _SlotMeta

var _ datamodel.Node = &_SlotMeta__Repr{}

func (_SlotMeta__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_SlotMeta__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.SlotMeta.Repr"}.LookupByString("")
}
func (n *_SlotMeta__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_SlotMeta__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.parent_slot.Representation(), nil
	case 1:
		return n.blocktime.Representation(), nil
	case 2:
		if n.block_height.m == schema.Maybe_Absent {
			return datamodel.Absent, datamodel.ErrNotExists{Segment: datamodel.PathSegmentOfInt(idx)}
		}
		if n.block_height.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return n.block_height.v.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _SlotMeta__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.SlotMeta.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_SlotMeta__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_SlotMeta__Repr) ListIterator() datamodel.ListIterator {
	end := 3
	if n.block_height.m == schema.Maybe_Absent {
		end = 2
	} else {
		goto done
	}
done:
	return &_SlotMeta__ReprListItr{n, 0, end}
}

type _SlotMeta__ReprListItr struct {
	n   *_SlotMeta__Repr
	idx int
	end int
}

func (itr *_SlotMeta__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 3 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.parent_slot.Representation()
	case 1:
		idx = int64(itr.idx)
		v = itr.n.blocktime.Representation()
	case 2:
		idx = int64(itr.idx)
		if itr.n.block_height.m == schema.Maybe_Absent {
			return -1, nil, datamodel.ErrIteratorOverread{}
		}
		if itr.n.block_height.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = itr.n.block_height.v.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_SlotMeta__ReprListItr) Done() bool {
	return itr.idx >= itr.end
}

func (rn *_SlotMeta__Repr) Length() int64 {
	l := 3
	if rn.block_height.m == schema.Maybe_Absent {
		l--
	}
	return int64(l)
}
func (_SlotMeta__Repr) IsAbsent() bool {
	return false
}
func (_SlotMeta__Repr) IsNull() bool {
	return false
}
func (_SlotMeta__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.SlotMeta.Repr"}.AsBool()
}
func (_SlotMeta__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.SlotMeta.Repr"}.AsInt()
}
func (_SlotMeta__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.SlotMeta.Repr"}.AsFloat()
}
func (_SlotMeta__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.SlotMeta.Repr"}.AsString()
}
func (_SlotMeta__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.SlotMeta.Repr"}.AsBytes()
}
func (_SlotMeta__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.SlotMeta.Repr"}.AsLink()
}
func (_SlotMeta__Repr) Prototype() datamodel.NodePrototype {
	return _SlotMeta__ReprPrototype{}
}

type _SlotMeta__ReprPrototype struct{}

func (_SlotMeta__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _SlotMeta__ReprBuilder
	nb.Reset()
	return &nb
}

type _SlotMeta__ReprBuilder struct {
	_SlotMeta__ReprAssembler
}

func (nb *_SlotMeta__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_SlotMeta__ReprBuilder) Reset() {
	var w _SlotMeta
	var m schema.Maybe
	*nb = _SlotMeta__ReprBuilder{_SlotMeta__ReprAssembler{w: &w, m: &m}}
}

type _SlotMeta__ReprAssembler struct {
	w     *_SlotMeta
	m     *schema.Maybe
	state laState
	f     int

	cm              schema.Maybe
	ca_parent_slot  _Int__ReprAssembler
	ca_blocktime    _Int__ReprAssembler
	ca_block_height _Int__ReprAssembler
}

func (na *_SlotMeta__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_parent_slot.reset()
	na.ca_blocktime.reset()
	na.ca_block_height.reset()
}
func (_SlotMeta__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.SlotMeta.Repr"}.BeginMap(0)
}
func (na *_SlotMeta__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_SlotMeta{}
	}
	return na, nil
}
func (na *_SlotMeta__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.SlotMeta.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_SlotMeta__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.SlotMeta.Repr"}.AssignBool(false)
}
func (_SlotMeta__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.SlotMeta.Repr"}.AssignInt(0)
}
func (_SlotMeta__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.SlotMeta.Repr"}.AssignFloat(0)
}
func (_SlotMeta__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.SlotMeta.Repr"}.AssignString("")
}
func (_SlotMeta__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.SlotMeta.Repr"}.AssignBytes(nil)
}
func (_SlotMeta__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.SlotMeta.Repr"}.AssignLink(nil)
}
func (na *_SlotMeta__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_SlotMeta); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.SlotMeta.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_SlotMeta__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _SlotMeta__ReprPrototype{}
}
func (la *_SlotMeta__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 2:
		switch la.w.block_height.m {
		case schema.Maybe_Value:
			la.state = laState_initial
			la.f++
			return true
		case schema.Maybe_Null:
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_SlotMeta__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 3 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(3)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_parent_slot.w = &la.w.parent_slot
		la.ca_parent_slot.m = &la.cm
		return &la.ca_parent_slot
	case 1:
		la.ca_blocktime.w = &la.w.blocktime
		la.ca_blocktime.m = &la.cm
		return &la.ca_blocktime
	case 2:
		la.ca_block_height.w = &la.w.block_height.v
		la.ca_block_height.m = &la.w.block_height.m
		la.w.block_height.m = allowNull
		return &la.ca_block_height
	default:
		panic("unreachable")
	}
}
func (la *_SlotMeta__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_SlotMeta__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}

func (n String) String() string {
	return n.x
}
func (_String__Prototype) fromString(w *_String, v string) error {
	*w = _String{v}
	return nil
}
func (_String__Prototype) FromString(v string) (String, error) {
	n := _String{v}
	return &n, nil
}

type _String__Maybe struct {
	m schema.Maybe
	v _String
}
type MaybeString = *_String__Maybe

func (m MaybeString) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeString) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeString) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeString) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return &m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeString) Must() String {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return &m.v
}

var _ datamodel.Node = (String)(&_String{})
var _ schema.TypedNode = (String)(&_String{})

func (String) Kind() datamodel.Kind {
	return datamodel.Kind_String
}
func (String) LookupByString(string) (datamodel.Node, error) {
	return mixins.String{TypeName: "ipldsch.String"}.LookupByString("")
}
func (String) LookupByNode(datamodel.Node) (datamodel.Node, error) {
	return mixins.String{TypeName: "ipldsch.String"}.LookupByNode(nil)
}
func (String) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.String{TypeName: "ipldsch.String"}.LookupByIndex(0)
}
func (String) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return mixins.String{TypeName: "ipldsch.String"}.LookupBySegment(seg)
}
func (String) MapIterator() datamodel.MapIterator {
	return nil
}
func (String) ListIterator() datamodel.ListIterator {
	return nil
}
func (String) Length() int64 {
	return -1
}
func (String) IsAbsent() bool {
	return false
}
func (String) IsNull() bool {
	return false
}
func (String) AsBool() (bool, error) {
	return mixins.String{TypeName: "ipldsch.String"}.AsBool()
}
func (String) AsInt() (int64, error) {
	return mixins.String{TypeName: "ipldsch.String"}.AsInt()
}
func (String) AsFloat() (float64, error) {
	return mixins.String{TypeName: "ipldsch.String"}.AsFloat()
}
func (n String) AsString() (string, error) {
	return n.x, nil
}
func (String) AsBytes() ([]byte, error) {
	return mixins.String{TypeName: "ipldsch.String"}.AsBytes()
}
func (String) AsLink() (datamodel.Link, error) {
	return mixins.String{TypeName: "ipldsch.String"}.AsLink()
}
func (String) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}

type _String__Prototype struct{}

func (_String__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _String__Builder
	nb.Reset()
	return &nb
}

type _String__Builder struct {
	_String__Assembler
}

func (nb *_String__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_String__Builder) Reset() {
	var w _String
	var m schema.Maybe
	*nb = _String__Builder{_String__Assembler{w: &w, m: &m}}
}

type _String__Assembler struct {
	w *_String
	m *schema.Maybe
}

func (na *_String__Assembler) reset() {}
func (_String__Assembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.String"}.BeginMap(0)
}
func (_String__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.String"}.BeginList(0)
}
func (na *_String__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.StringAssembler{TypeName: "ipldsch.String"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	panic("unreachable")
}
func (_String__Assembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.String"}.AssignBool(false)
}
func (_String__Assembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.String"}.AssignInt(0)
}
func (_String__Assembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.String"}.AssignFloat(0)
}
func (na *_String__Assembler) AssignString(v string) error {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	}
	na.w.x = v
	*na.m = schema.Maybe_Value
	return nil
}
func (_String__Assembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.String"}.AssignBytes(nil)
}
func (_String__Assembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.String"}.AssignLink(nil)
}
func (na *_String__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_String); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return na.AssignString(v2)
	}
}
func (_String__Assembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (String) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n String) Representation() datamodel.Node {
	return (*_String__Repr)(n)
}

type _String__Repr = _String

var _ datamodel.Node = &_String__Repr{}

type _String__ReprPrototype = _String__Prototype
type _String__ReprAssembler = _String__Assembler

func (n _Subset) FieldKind() Int {
	return &n.kind
}
func (n _Subset) FieldFirst() Int {
	return &n.first
}
func (n _Subset) FieldLast() Int {
	return &n.last
}
func (n _Subset) FieldBlocks() List__Link {
	return &n.blocks
}

type _Subset__Maybe struct {
	m schema.Maybe
	v Subset
}
type MaybeSubset = *_Subset__Maybe

func (m MaybeSubset) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeSubset) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeSubset) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeSubset) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeSubset) Must() Subset {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__Subset_Kind   = _String{"kind"}
	fieldName__Subset_First  = _String{"first"}
	fieldName__Subset_Last   = _String{"last"}
	fieldName__Subset_Blocks = _String{"blocks"}
)
var _ datamodel.Node = (Subset)(&_Subset{})
var _ schema.TypedNode = (Subset)(&_Subset{})

func (Subset) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n Subset) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "kind":
		return &n.kind, nil
	case "first":
		return &n.first, nil
	case "last":
		return &n.last, nil
	case "blocks":
		return &n.blocks, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n Subset) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (Subset) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.Subset"}.LookupByIndex(0)
}
func (n Subset) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n Subset) MapIterator() datamodel.MapIterator {
	return &_Subset__MapItr{n, 0}
}

type _Subset__MapItr struct {
	n   Subset
	idx int
}

func (itr *_Subset__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 4 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__Subset_Kind
		v = &itr.n.kind
	case 1:
		k = &fieldName__Subset_First
		v = &itr.n.first
	case 2:
		k = &fieldName__Subset_Last
		v = &itr.n.last
	case 3:
		k = &fieldName__Subset_Blocks
		v = &itr.n.blocks
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Subset__MapItr) Done() bool {
	return itr.idx >= 4
}

func (Subset) ListIterator() datamodel.ListIterator {
	return nil
}
func (Subset) Length() int64 {
	return 4
}
func (Subset) IsAbsent() bool {
	return false
}
func (Subset) IsNull() bool {
	return false
}
func (Subset) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.Subset"}.AsBool()
}
func (Subset) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.Subset"}.AsInt()
}
func (Subset) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.Subset"}.AsFloat()
}
func (Subset) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.Subset"}.AsString()
}
func (Subset) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.Subset"}.AsBytes()
}
func (Subset) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.Subset"}.AsLink()
}
func (Subset) Prototype() datamodel.NodePrototype {
	return _Subset__Prototype{}
}

type _Subset__Prototype struct{}

func (_Subset__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Subset__Builder
	nb.Reset()
	return &nb
}

type _Subset__Builder struct {
	_Subset__Assembler
}

func (nb *_Subset__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Subset__Builder) Reset() {
	var w _Subset
	var m schema.Maybe
	*nb = _Subset__Builder{_Subset__Assembler{w: &w, m: &m}}
}

type _Subset__Assembler struct {
	w     *_Subset
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm        schema.Maybe
	ca_kind   _Int__Assembler
	ca_first  _Int__Assembler
	ca_last   _Int__Assembler
	ca_blocks _List__Link__Assembler
}

func (na *_Subset__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_kind.reset()
	na.ca_first.reset()
	na.ca_last.reset()
	na.ca_blocks.reset()
}

var (
	fieldBit__Subset_Kind        = 1 << 0
	fieldBit__Subset_First       = 1 << 1
	fieldBit__Subset_Last        = 1 << 2
	fieldBit__Subset_Blocks      = 1 << 3
	fieldBits__Subset_sufficient = 0 + 1<<0 + 1<<1 + 1<<2 + 1<<3
)

func (na *_Subset__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Subset{}
	}
	return na, nil
}
func (_Subset__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.Subset"}.BeginList(0)
}
func (na *_Subset__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.Subset"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Subset__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Subset"}.AssignBool(false)
}
func (_Subset__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Subset"}.AssignInt(0)
}
func (_Subset__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Subset"}.AssignFloat(0)
}
func (_Subset__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Subset"}.AssignString("")
}
func (_Subset__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Subset"}.AssignBytes(nil)
}
func (_Subset__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Subset"}.AssignLink(nil)
}
func (na *_Subset__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Subset); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Subset", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Subset__Assembler) Prototype() datamodel.NodePrototype {
	return _Subset__Prototype{}
}
func (ma *_Subset__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_kind.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_first.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 2:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_last.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 3:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_blocks.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_Subset__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "kind":
		if ma.s&fieldBit__Subset_Kind != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Subset_Kind}
		}
		ma.s += fieldBit__Subset_Kind
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind, nil
	case "first":
		if ma.s&fieldBit__Subset_First != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Subset_First}
		}
		ma.s += fieldBit__Subset_First
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_first.w = &ma.w.first
		ma.ca_first.m = &ma.cm
		return &ma.ca_first, nil
	case "last":
		if ma.s&fieldBit__Subset_Last != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Subset_Last}
		}
		ma.s += fieldBit__Subset_Last
		ma.state = maState_midValue
		ma.f = 2
		ma.ca_last.w = &ma.w.last
		ma.ca_last.m = &ma.cm
		return &ma.ca_last, nil
	case "blocks":
		if ma.s&fieldBit__Subset_Blocks != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Subset_Blocks}
		}
		ma.s += fieldBit__Subset_Blocks
		ma.state = maState_midValue
		ma.f = 3
		ma.ca_blocks.w = &ma.w.blocks
		ma.ca_blocks.m = &ma.cm
		return &ma.ca_blocks, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.Subset", Key: &_String{k}}
}
func (ma *_Subset__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_Subset__KeyAssembler)(ma)
}
func (ma *_Subset__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind
	case 1:
		ma.ca_first.w = &ma.w.first
		ma.ca_first.m = &ma.cm
		return &ma.ca_first
	case 2:
		ma.ca_last.w = &ma.w.last
		ma.ca_last.m = &ma.cm
		return &ma.ca_last
	case 3:
		ma.ca_blocks.w = &ma.w.blocks
		ma.ca_blocks.m = &ma.cm
		return &ma.ca_blocks
	default:
		panic("unreachable")
	}
}
func (ma *_Subset__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__Subset_sufficient != fieldBits__Subset_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__Subset_Kind == 0 {
			err.Missing = append(err.Missing, "kind")
		}
		if ma.s&fieldBit__Subset_First == 0 {
			err.Missing = append(err.Missing, "first")
		}
		if ma.s&fieldBit__Subset_Last == 0 {
			err.Missing = append(err.Missing, "last")
		}
		if ma.s&fieldBit__Subset_Blocks == 0 {
			err.Missing = append(err.Missing, "blocks")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_Subset__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_Subset__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _Subset__KeyAssembler _Subset__Assembler

func (_Subset__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Subset.KeyAssembler"}.BeginMap(0)
}
func (_Subset__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Subset.KeyAssembler"}.BeginList(0)
}
func (na *_Subset__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.Subset.KeyAssembler"}.AssignNull()
}
func (_Subset__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Subset.KeyAssembler"}.AssignBool(false)
}
func (_Subset__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Subset.KeyAssembler"}.AssignInt(0)
}
func (_Subset__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Subset.KeyAssembler"}.AssignFloat(0)
}
func (ka *_Subset__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "kind":
		if ka.s&fieldBit__Subset_Kind != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Subset_Kind}
		}
		ka.s += fieldBit__Subset_Kind
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "first":
		if ka.s&fieldBit__Subset_First != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Subset_First}
		}
		ka.s += fieldBit__Subset_First
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	case "last":
		if ka.s&fieldBit__Subset_Last != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Subset_Last}
		}
		ka.s += fieldBit__Subset_Last
		ka.state = maState_expectValue
		ka.f = 2
		return nil
	case "blocks":
		if ka.s&fieldBit__Subset_Blocks != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Subset_Blocks}
		}
		ka.s += fieldBit__Subset_Blocks
		ka.state = maState_expectValue
		ka.f = 3
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.Subset", Key: &_String{k}}
	}
}
func (_Subset__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Subset.KeyAssembler"}.AssignBytes(nil)
}
func (_Subset__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Subset.KeyAssembler"}.AssignLink(nil)
}
func (ka *_Subset__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_Subset__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (Subset) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Subset) Representation() datamodel.Node {
	return (*_Subset__Repr)(n)
}

type _Subset__Repr _Subset

var _ datamodel.Node = &_Subset__Repr{}

func (_Subset__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_Subset__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.Subset.Repr"}.LookupByString("")
}
func (n *_Subset__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_Subset__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.kind.Representation(), nil
	case 1:
		return n.first.Representation(), nil
	case 2:
		return n.last.Representation(), nil
	case 3:
		return n.blocks.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _Subset__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.Subset.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_Subset__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_Subset__Repr) ListIterator() datamodel.ListIterator {
	return &_Subset__ReprListItr{n, 0}
}

type _Subset__ReprListItr struct {
	n   *_Subset__Repr
	idx int
}

func (itr *_Subset__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 4 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.kind.Representation()
	case 1:
		idx = int64(itr.idx)
		v = itr.n.first.Representation()
	case 2:
		idx = int64(itr.idx)
		v = itr.n.last.Representation()
	case 3:
		idx = int64(itr.idx)
		v = itr.n.blocks.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Subset__ReprListItr) Done() bool {
	return itr.idx >= 4
}

func (rn *_Subset__Repr) Length() int64 {
	l := 4
	return int64(l)
}
func (_Subset__Repr) IsAbsent() bool {
	return false
}
func (_Subset__Repr) IsNull() bool {
	return false
}
func (_Subset__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.Subset.Repr"}.AsBool()
}
func (_Subset__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.Subset.Repr"}.AsInt()
}
func (_Subset__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.Subset.Repr"}.AsFloat()
}
func (_Subset__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.Subset.Repr"}.AsString()
}
func (_Subset__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.Subset.Repr"}.AsBytes()
}
func (_Subset__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.Subset.Repr"}.AsLink()
}
func (_Subset__Repr) Prototype() datamodel.NodePrototype {
	return _Subset__ReprPrototype{}
}

type _Subset__ReprPrototype struct{}

func (_Subset__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Subset__ReprBuilder
	nb.Reset()
	return &nb
}

type _Subset__ReprBuilder struct {
	_Subset__ReprAssembler
}

func (nb *_Subset__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Subset__ReprBuilder) Reset() {
	var w _Subset
	var m schema.Maybe
	*nb = _Subset__ReprBuilder{_Subset__ReprAssembler{w: &w, m: &m}}
}

type _Subset__ReprAssembler struct {
	w     *_Subset
	m     *schema.Maybe
	state laState
	f     int

	cm        schema.Maybe
	ca_kind   _Int__ReprAssembler
	ca_first  _Int__ReprAssembler
	ca_last   _Int__ReprAssembler
	ca_blocks _List__Link__ReprAssembler
}

func (na *_Subset__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_kind.reset()
	na.ca_first.reset()
	na.ca_last.reset()
	na.ca_blocks.reset()
}
func (_Subset__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.Subset.Repr"}.BeginMap(0)
}
func (na *_Subset__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Subset{}
	}
	return na, nil
}
func (na *_Subset__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.Subset.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Subset__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Subset.Repr"}.AssignBool(false)
}
func (_Subset__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Subset.Repr"}.AssignInt(0)
}
func (_Subset__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Subset.Repr"}.AssignFloat(0)
}
func (_Subset__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Subset.Repr"}.AssignString("")
}
func (_Subset__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Subset.Repr"}.AssignBytes(nil)
}
func (_Subset__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Subset.Repr"}.AssignLink(nil)
}
func (na *_Subset__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Subset); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Subset.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Subset__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _Subset__ReprPrototype{}
}
func (la *_Subset__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 2:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 3:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_Subset__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 4 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(4)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_kind.w = &la.w.kind
		la.ca_kind.m = &la.cm
		return &la.ca_kind
	case 1:
		la.ca_first.w = &la.w.first
		la.ca_first.m = &la.cm
		return &la.ca_first
	case 2:
		la.ca_last.w = &la.w.last
		la.ca_last.m = &la.cm
		return &la.ca_last
	case 3:
		la.ca_blocks.w = &la.w.blocks
		la.ca_blocks.m = &la.cm
		return &la.ca_blocks
	default:
		panic("unreachable")
	}
}
func (la *_Subset__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_Subset__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}

func (n _Transaction) FieldKind() Int {
	return &n.kind
}
func (n _Transaction) FieldData() DataFrame {
	return &n.data
}
func (n _Transaction) FieldMetadata() DataFrame {
	return &n.metadata
}
func (n _Transaction) FieldSlot() Int {
	return &n.slot
}
func (n _Transaction) FieldIndex() MaybeInt {
	return &n.index
}

type _Transaction__Maybe struct {
	m schema.Maybe
	v Transaction
}
type MaybeTransaction = *_Transaction__Maybe

func (m MaybeTransaction) IsNull() bool {
	return m.m == schema.Maybe_Null
}
func (m MaybeTransaction) IsAbsent() bool {
	return m.m == schema.Maybe_Absent
}
func (m MaybeTransaction) Exists() bool {
	return m.m == schema.Maybe_Value
}
func (m MaybeTransaction) AsNode() datamodel.Node {
	switch m.m {
	case schema.Maybe_Absent:
		return datamodel.Absent
	case schema.Maybe_Null:
		return datamodel.Null
	case schema.Maybe_Value:
		return m.v
	default:
		panic("unreachable")
	}
}
func (m MaybeTransaction) Must() Transaction {
	if !m.Exists() {
		panic("unbox of a maybe rejected")
	}
	return m.v
}

var (
	fieldName__Transaction_Kind     = _String{"kind"}
	fieldName__Transaction_Data     = _String{"data"}
	fieldName__Transaction_Metadata = _String{"metadata"}
	fieldName__Transaction_Slot     = _String{"slot"}
	fieldName__Transaction_Index    = _String{"index"}
)
var _ datamodel.Node = (Transaction)(&_Transaction{})
var _ schema.TypedNode = (Transaction)(&_Transaction{})

func (Transaction) Kind() datamodel.Kind {
	return datamodel.Kind_Map
}
func (n Transaction) LookupByString(key string) (datamodel.Node, error) {
	switch key {
	case "kind":
		return &n.kind, nil
	case "data":
		return &n.data, nil
	case "metadata":
		return &n.metadata, nil
	case "slot":
		return &n.slot, nil
	case "index":
		if n.index.m == schema.Maybe_Absent {
			return datamodel.Absent, nil
		}
		if n.index.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return &n.index.v, nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfString(key)}
	}
}
func (n Transaction) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ks, err := key.AsString()
	if err != nil {
		return nil, err
	}
	return n.LookupByString(ks)
}
func (Transaction) LookupByIndex(idx int64) (datamodel.Node, error) {
	return mixins.Map{TypeName: "ipldsch.Transaction"}.LookupByIndex(0)
}
func (n Transaction) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	return n.LookupByString(seg.String())
}
func (n Transaction) MapIterator() datamodel.MapIterator {
	return &_Transaction__MapItr{n, 0}
}

type _Transaction__MapItr struct {
	n   Transaction
	idx int
}

func (itr *_Transaction__MapItr) Next() (k datamodel.Node, v datamodel.Node, _ error) {
	if itr.idx >= 5 {
		return nil, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		k = &fieldName__Transaction_Kind
		v = &itr.n.kind
	case 1:
		k = &fieldName__Transaction_Data
		v = &itr.n.data
	case 2:
		k = &fieldName__Transaction_Metadata
		v = &itr.n.metadata
	case 3:
		k = &fieldName__Transaction_Slot
		v = &itr.n.slot
	case 4:
		k = &fieldName__Transaction_Index
		if itr.n.index.m == schema.Maybe_Absent {
			v = datamodel.Absent
			break
		}
		if itr.n.index.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = &itr.n.index.v
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Transaction__MapItr) Done() bool {
	return itr.idx >= 5
}

func (Transaction) ListIterator() datamodel.ListIterator {
	return nil
}
func (Transaction) Length() int64 {
	return 5
}
func (Transaction) IsAbsent() bool {
	return false
}
func (Transaction) IsNull() bool {
	return false
}
func (Transaction) AsBool() (bool, error) {
	return mixins.Map{TypeName: "ipldsch.Transaction"}.AsBool()
}
func (Transaction) AsInt() (int64, error) {
	return mixins.Map{TypeName: "ipldsch.Transaction"}.AsInt()
}
func (Transaction) AsFloat() (float64, error) {
	return mixins.Map{TypeName: "ipldsch.Transaction"}.AsFloat()
}
func (Transaction) AsString() (string, error) {
	return mixins.Map{TypeName: "ipldsch.Transaction"}.AsString()
}
func (Transaction) AsBytes() ([]byte, error) {
	return mixins.Map{TypeName: "ipldsch.Transaction"}.AsBytes()
}
func (Transaction) AsLink() (datamodel.Link, error) {
	return mixins.Map{TypeName: "ipldsch.Transaction"}.AsLink()
}
func (Transaction) Prototype() datamodel.NodePrototype {
	return _Transaction__Prototype{}
}

type _Transaction__Prototype struct{}

func (_Transaction__Prototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Transaction__Builder
	nb.Reset()
	return &nb
}

type _Transaction__Builder struct {
	_Transaction__Assembler
}

func (nb *_Transaction__Builder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Transaction__Builder) Reset() {
	var w _Transaction
	var m schema.Maybe
	*nb = _Transaction__Builder{_Transaction__Assembler{w: &w, m: &m}}
}

type _Transaction__Assembler struct {
	w     *_Transaction
	m     *schema.Maybe
	state maState
	s     int
	f     int

	cm          schema.Maybe
	ca_kind     _Int__Assembler
	ca_data     _DataFrame__Assembler
	ca_metadata _DataFrame__Assembler
	ca_slot     _Int__Assembler
	ca_index    _Int__Assembler
}

func (na *_Transaction__Assembler) reset() {
	na.state = maState_initial
	na.s = 0
	na.ca_kind.reset()
	na.ca_data.reset()
	na.ca_metadata.reset()
	na.ca_slot.reset()
	na.ca_index.reset()
}

var (
	fieldBit__Transaction_Kind        = 1 << 0
	fieldBit__Transaction_Data        = 1 << 1
	fieldBit__Transaction_Metadata    = 1 << 2
	fieldBit__Transaction_Slot        = 1 << 3
	fieldBit__Transaction_Index       = 1 << 4
	fieldBits__Transaction_sufficient = 0 + 1<<0 + 1<<1 + 1<<2 + 1<<3
)

func (na *_Transaction__Assembler) BeginMap(int64) (datamodel.MapAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Transaction{}
	}
	return na, nil
}
func (_Transaction__Assembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.MapAssembler{TypeName: "ipldsch.Transaction"}.BeginList(0)
}
func (na *_Transaction__Assembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.MapAssembler{TypeName: "ipldsch.Transaction"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Transaction__Assembler) AssignBool(bool) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Transaction"}.AssignBool(false)
}
func (_Transaction__Assembler) AssignInt(int64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Transaction"}.AssignInt(0)
}
func (_Transaction__Assembler) AssignFloat(float64) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Transaction"}.AssignFloat(0)
}
func (_Transaction__Assembler) AssignString(string) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Transaction"}.AssignString("")
}
func (_Transaction__Assembler) AssignBytes([]byte) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Transaction"}.AssignBytes(nil)
}
func (_Transaction__Assembler) AssignLink(datamodel.Link) error {
	return mixins.MapAssembler{TypeName: "ipldsch.Transaction"}.AssignLink(nil)
}
func (na *_Transaction__Assembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Transaction); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_Map {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Transaction", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustMap, ActualKind: v.Kind()}
	}
	itr := v.MapIterator()
	for !itr.Done() {
		k, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleKey().AssignNode(k); err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Transaction__Assembler) Prototype() datamodel.NodePrototype {
	return _Transaction__Prototype{}
}
func (ma *_Transaction__Assembler) valueFinishTidy() bool {
	switch ma.f {
	case 0:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_kind.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 1:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_data.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 2:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_metadata.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 3:
		switch ma.cm {
		case schema.Maybe_Value:
			ma.ca_slot.w = nil
			ma.cm = schema.Maybe_Absent
			ma.state = maState_initial
			return true
		default:
			return false
		}
	case 4:
		switch ma.w.index.m {
		case schema.Maybe_Null:
			ma.state = maState_initial
			return true
		case schema.Maybe_Value:
			ma.state = maState_initial
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (ma *_Transaction__Assembler) AssembleEntry(k string) (datamodel.NodeAssembler, error) {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleEntry cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleEntry cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleEntry cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleEntry cannot be called on an assembler that's already finished")
	}
	switch k {
	case "kind":
		if ma.s&fieldBit__Transaction_Kind != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Kind}
		}
		ma.s += fieldBit__Transaction_Kind
		ma.state = maState_midValue
		ma.f = 0
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind, nil
	case "data":
		if ma.s&fieldBit__Transaction_Data != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Data}
		}
		ma.s += fieldBit__Transaction_Data
		ma.state = maState_midValue
		ma.f = 1
		ma.ca_data.w = &ma.w.data
		ma.ca_data.m = &ma.cm
		return &ma.ca_data, nil
	case "metadata":
		if ma.s&fieldBit__Transaction_Metadata != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Metadata}
		}
		ma.s += fieldBit__Transaction_Metadata
		ma.state = maState_midValue
		ma.f = 2
		ma.ca_metadata.w = &ma.w.metadata
		ma.ca_metadata.m = &ma.cm
		return &ma.ca_metadata, nil
	case "slot":
		if ma.s&fieldBit__Transaction_Slot != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Slot}
		}
		ma.s += fieldBit__Transaction_Slot
		ma.state = maState_midValue
		ma.f = 3
		ma.ca_slot.w = &ma.w.slot
		ma.ca_slot.m = &ma.cm
		return &ma.ca_slot, nil
	case "index":
		if ma.s&fieldBit__Transaction_Index != 0 {
			return nil, datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Index}
		}
		ma.s += fieldBit__Transaction_Index
		ma.state = maState_midValue
		ma.f = 4
		ma.ca_index.w = &ma.w.index.v
		ma.ca_index.m = &ma.w.index.m
		ma.w.index.m = allowNull
		return &ma.ca_index, nil
	}
	return nil, schema.ErrInvalidKey{TypeName: "ipldsch.Transaction", Key: &_String{k}}
}
func (ma *_Transaction__Assembler) AssembleKey() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: AssembleKey cannot be called when in the middle of assembling another key")
	case maState_expectValue:
		panic("invalid state: AssembleKey cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: AssembleKey cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: AssembleKey cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midKey
	return (*_Transaction__KeyAssembler)(ma)
}
func (ma *_Transaction__Assembler) AssembleValue() datamodel.NodeAssembler {
	switch ma.state {
	case maState_initial:
		panic("invalid state: AssembleValue cannot be called when no key is primed")
	case maState_midKey:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		// carry on
	case maState_midValue:
		panic("invalid state: AssembleValue cannot be called when in the middle of assembling another value")
	case maState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	ma.state = maState_midValue
	switch ma.f {
	case 0:
		ma.ca_kind.w = &ma.w.kind
		ma.ca_kind.m = &ma.cm
		return &ma.ca_kind
	case 1:
		ma.ca_data.w = &ma.w.data
		ma.ca_data.m = &ma.cm
		return &ma.ca_data
	case 2:
		ma.ca_metadata.w = &ma.w.metadata
		ma.ca_metadata.m = &ma.cm
		return &ma.ca_metadata
	case 3:
		ma.ca_slot.w = &ma.w.slot
		ma.ca_slot.m = &ma.cm
		return &ma.ca_slot
	case 4:
		ma.ca_index.w = &ma.w.index.v
		ma.ca_index.m = &ma.w.index.m
		ma.w.index.m = allowNull
		return &ma.ca_index
	default:
		panic("unreachable")
	}
}
func (ma *_Transaction__Assembler) Finish() error {
	switch ma.state {
	case maState_initial:
		// carry on
	case maState_midKey:
		panic("invalid state: Finish cannot be called when in the middle of assembling a key")
	case maState_expectValue:
		panic("invalid state: Finish cannot be called when expecting start of value assembly")
	case maState_midValue:
		if !ma.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case maState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	if ma.s&fieldBits__Transaction_sufficient != fieldBits__Transaction_sufficient {
		err := schema.ErrMissingRequiredField{Missing: make([]string, 0)}
		if ma.s&fieldBit__Transaction_Kind == 0 {
			err.Missing = append(err.Missing, "kind")
		}
		if ma.s&fieldBit__Transaction_Data == 0 {
			err.Missing = append(err.Missing, "data")
		}
		if ma.s&fieldBit__Transaction_Metadata == 0 {
			err.Missing = append(err.Missing, "metadata")
		}
		if ma.s&fieldBit__Transaction_Slot == 0 {
			err.Missing = append(err.Missing, "slot")
		}
		return err
	}
	ma.state = maState_finished
	*ma.m = schema.Maybe_Value
	return nil
}
func (ma *_Transaction__Assembler) KeyPrototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (ma *_Transaction__Assembler) ValuePrototype(k string) datamodel.NodePrototype {
	panic("todo structbuilder mapassembler valueprototype")
}

type _Transaction__KeyAssembler _Transaction__Assembler

func (_Transaction__KeyAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Transaction.KeyAssembler"}.BeginMap(0)
}
func (_Transaction__KeyAssembler) BeginList(sizeHint int64) (datamodel.ListAssembler, error) {
	return mixins.StringAssembler{TypeName: "ipldsch.Transaction.KeyAssembler"}.BeginList(0)
}
func (na *_Transaction__KeyAssembler) AssignNull() error {
	return mixins.StringAssembler{TypeName: "ipldsch.Transaction.KeyAssembler"}.AssignNull()
}
func (_Transaction__KeyAssembler) AssignBool(bool) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Transaction.KeyAssembler"}.AssignBool(false)
}
func (_Transaction__KeyAssembler) AssignInt(int64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Transaction.KeyAssembler"}.AssignInt(0)
}
func (_Transaction__KeyAssembler) AssignFloat(float64) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Transaction.KeyAssembler"}.AssignFloat(0)
}
func (ka *_Transaction__KeyAssembler) AssignString(k string) error {
	if ka.state != maState_midKey {
		panic("misuse: KeyAssembler held beyond its valid lifetime")
	}
	switch k {
	case "kind":
		if ka.s&fieldBit__Transaction_Kind != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Kind}
		}
		ka.s += fieldBit__Transaction_Kind
		ka.state = maState_expectValue
		ka.f = 0
		return nil
	case "data":
		if ka.s&fieldBit__Transaction_Data != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Data}
		}
		ka.s += fieldBit__Transaction_Data
		ka.state = maState_expectValue
		ka.f = 1
		return nil
	case "metadata":
		if ka.s&fieldBit__Transaction_Metadata != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Metadata}
		}
		ka.s += fieldBit__Transaction_Metadata
		ka.state = maState_expectValue
		ka.f = 2
		return nil
	case "slot":
		if ka.s&fieldBit__Transaction_Slot != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Slot}
		}
		ka.s += fieldBit__Transaction_Slot
		ka.state = maState_expectValue
		ka.f = 3
		return nil
	case "index":
		if ka.s&fieldBit__Transaction_Index != 0 {
			return datamodel.ErrRepeatedMapKey{Key: &fieldName__Transaction_Index}
		}
		ka.s += fieldBit__Transaction_Index
		ka.state = maState_expectValue
		ka.f = 4
		return nil
	default:
		return schema.ErrInvalidKey{TypeName: "ipldsch.Transaction", Key: &_String{k}}
	}
}
func (_Transaction__KeyAssembler) AssignBytes([]byte) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Transaction.KeyAssembler"}.AssignBytes(nil)
}
func (_Transaction__KeyAssembler) AssignLink(datamodel.Link) error {
	return mixins.StringAssembler{TypeName: "ipldsch.Transaction.KeyAssembler"}.AssignLink(nil)
}
func (ka *_Transaction__KeyAssembler) AssignNode(v datamodel.Node) error {
	if v2, err := v.AsString(); err != nil {
		return err
	} else {
		return ka.AssignString(v2)
	}
}
func (_Transaction__KeyAssembler) Prototype() datamodel.NodePrototype {
	return _String__Prototype{}
}
func (Transaction) Type() schema.Type {
	return nil /*TODO:typelit*/
}
func (n Transaction) Representation() datamodel.Node {
	return (*_Transaction__Repr)(n)
}

type _Transaction__Repr _Transaction

var _ datamodel.Node = &_Transaction__Repr{}

func (_Transaction__Repr) Kind() datamodel.Kind {
	return datamodel.Kind_List
}
func (_Transaction__Repr) LookupByString(string) (datamodel.Node, error) {
	return mixins.List{TypeName: "ipldsch.Transaction.Repr"}.LookupByString("")
}
func (n *_Transaction__Repr) LookupByNode(key datamodel.Node) (datamodel.Node, error) {
	ki, err := key.AsInt()
	if err != nil {
		return nil, err
	}
	return n.LookupByIndex(ki)
}
func (n *_Transaction__Repr) LookupByIndex(idx int64) (datamodel.Node, error) {
	switch idx {
	case 0:
		return n.kind.Representation(), nil
	case 1:
		return n.data.Representation(), nil
	case 2:
		return n.metadata.Representation(), nil
	case 3:
		return n.slot.Representation(), nil
	case 4:
		if n.index.m == schema.Maybe_Absent {
			return datamodel.Absent, datamodel.ErrNotExists{Segment: datamodel.PathSegmentOfInt(idx)}
		}
		if n.index.m == schema.Maybe_Null {
			return datamodel.Null, nil
		}
		return n.index.v.Representation(), nil
	default:
		return nil, schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(idx)}
	}
}
func (n _Transaction__Repr) LookupBySegment(seg datamodel.PathSegment) (datamodel.Node, error) {
	i, err := seg.Index()
	if err != nil {
		return nil, datamodel.ErrInvalidSegmentForList{TypeName: "ipldsch.Transaction.Repr", TroubleSegment: seg, Reason: err}
	}
	return n.LookupByIndex(i)
}
func (_Transaction__Repr) MapIterator() datamodel.MapIterator {
	return nil
}
func (n *_Transaction__Repr) ListIterator() datamodel.ListIterator {
	end := 5
	if n.index.m == schema.Maybe_Absent {
		end = 4
	} else {
		goto done
	}
done:
	return &_Transaction__ReprListItr{n, 0, end}
}

type _Transaction__ReprListItr struct {
	n   *_Transaction__Repr
	idx int
	end int
}

func (itr *_Transaction__ReprListItr) Next() (idx int64, v datamodel.Node, err error) {
	if itr.idx >= 5 {
		return -1, nil, datamodel.ErrIteratorOverread{}
	}
	switch itr.idx {
	case 0:
		idx = int64(itr.idx)
		v = itr.n.kind.Representation()
	case 1:
		idx = int64(itr.idx)
		v = itr.n.data.Representation()
	case 2:
		idx = int64(itr.idx)
		v = itr.n.metadata.Representation()
	case 3:
		idx = int64(itr.idx)
		v = itr.n.slot.Representation()
	case 4:
		idx = int64(itr.idx)
		if itr.n.index.m == schema.Maybe_Absent {
			return -1, nil, datamodel.ErrIteratorOverread{}
		}
		if itr.n.index.m == schema.Maybe_Null {
			v = datamodel.Null
			break
		}
		v = itr.n.index.v.Representation()
	default:
		panic("unreachable")
	}
	itr.idx++
	return
}
func (itr *_Transaction__ReprListItr) Done() bool {
	return itr.idx >= itr.end
}

func (rn *_Transaction__Repr) Length() int64 {
	l := 5
	if rn.index.m == schema.Maybe_Absent {
		l--
	}
	return int64(l)
}
func (_Transaction__Repr) IsAbsent() bool {
	return false
}
func (_Transaction__Repr) IsNull() bool {
	return false
}
func (_Transaction__Repr) AsBool() (bool, error) {
	return mixins.List{TypeName: "ipldsch.Transaction.Repr"}.AsBool()
}
func (_Transaction__Repr) AsInt() (int64, error) {
	return mixins.List{TypeName: "ipldsch.Transaction.Repr"}.AsInt()
}
func (_Transaction__Repr) AsFloat() (float64, error) {
	return mixins.List{TypeName: "ipldsch.Transaction.Repr"}.AsFloat()
}
func (_Transaction__Repr) AsString() (string, error) {
	return mixins.List{TypeName: "ipldsch.Transaction.Repr"}.AsString()
}
func (_Transaction__Repr) AsBytes() ([]byte, error) {
	return mixins.List{TypeName: "ipldsch.Transaction.Repr"}.AsBytes()
}
func (_Transaction__Repr) AsLink() (datamodel.Link, error) {
	return mixins.List{TypeName: "ipldsch.Transaction.Repr"}.AsLink()
}
func (_Transaction__Repr) Prototype() datamodel.NodePrototype {
	return _Transaction__ReprPrototype{}
}

type _Transaction__ReprPrototype struct{}

func (_Transaction__ReprPrototype) NewBuilder() datamodel.NodeBuilder {
	var nb _Transaction__ReprBuilder
	nb.Reset()
	return &nb
}

type _Transaction__ReprBuilder struct {
	_Transaction__ReprAssembler
}

func (nb *_Transaction__ReprBuilder) Build() datamodel.Node {
	if *nb.m != schema.Maybe_Value {
		panic("invalid state: cannot call Build on an assembler that's not finished")
	}
	return nb.w
}
func (nb *_Transaction__ReprBuilder) Reset() {
	var w _Transaction
	var m schema.Maybe
	*nb = _Transaction__ReprBuilder{_Transaction__ReprAssembler{w: &w, m: &m}}
}

type _Transaction__ReprAssembler struct {
	w     *_Transaction
	m     *schema.Maybe
	state laState
	f     int

	cm          schema.Maybe
	ca_kind     _Int__ReprAssembler
	ca_data     _DataFrame__ReprAssembler
	ca_metadata _DataFrame__ReprAssembler
	ca_slot     _Int__ReprAssembler
	ca_index    _Int__ReprAssembler
}

func (na *_Transaction__ReprAssembler) reset() {
	na.state = laState_initial
	na.f = 0
	na.ca_kind.reset()
	na.ca_data.reset()
	na.ca_metadata.reset()
	na.ca_slot.reset()
	na.ca_index.reset()
}
func (_Transaction__ReprAssembler) BeginMap(sizeHint int64) (datamodel.MapAssembler, error) {
	return mixins.ListAssembler{TypeName: "ipldsch.Transaction.Repr"}.BeginMap(0)
}
func (na *_Transaction__ReprAssembler) BeginList(int64) (datamodel.ListAssembler, error) {
	switch *na.m {
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: it makes no sense to 'begin' twice on the same assembler!")
	}
	*na.m = midvalue
	if na.w == nil {
		na.w = &_Transaction{}
	}
	return na, nil
}
func (na *_Transaction__ReprAssembler) AssignNull() error {
	switch *na.m {
	case allowNull:
		*na.m = schema.Maybe_Null
		return nil
	case schema.Maybe_Absent:
		return mixins.ListAssembler{TypeName: "ipldsch.Transaction.Repr.Repr"}.AssignNull()
	case schema.Maybe_Value, schema.Maybe_Null:
		panic("invalid state: cannot assign into assembler that's already finished")
	case midvalue:
		panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
	}
	panic("unreachable")
}
func (_Transaction__ReprAssembler) AssignBool(bool) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Transaction.Repr"}.AssignBool(false)
}
func (_Transaction__ReprAssembler) AssignInt(int64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Transaction.Repr"}.AssignInt(0)
}
func (_Transaction__ReprAssembler) AssignFloat(float64) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Transaction.Repr"}.AssignFloat(0)
}
func (_Transaction__ReprAssembler) AssignString(string) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Transaction.Repr"}.AssignString("")
}
func (_Transaction__ReprAssembler) AssignBytes([]byte) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Transaction.Repr"}.AssignBytes(nil)
}
func (_Transaction__ReprAssembler) AssignLink(datamodel.Link) error {
	return mixins.ListAssembler{TypeName: "ipldsch.Transaction.Repr"}.AssignLink(nil)
}
func (na *_Transaction__ReprAssembler) AssignNode(v datamodel.Node) error {
	if v.IsNull() {
		return na.AssignNull()
	}
	if v2, ok := v.(*_Transaction); ok {
		switch *na.m {
		case schema.Maybe_Value, schema.Maybe_Null:
			panic("invalid state: cannot assign into assembler that's already finished")
		case midvalue:
			panic("invalid state: cannot assign null into an assembler that's already begun working on recursive structures!")
		}
		if na.w == nil {
			na.w = v2
			*na.m = schema.Maybe_Value
			return nil
		}
		*na.w = *v2
		*na.m = schema.Maybe_Value
		return nil
	}
	if v.Kind() != datamodel.Kind_List {
		return datamodel.ErrWrongKind{TypeName: "ipldsch.Transaction.Repr", MethodName: "AssignNode", AppropriateKind: datamodel.KindSet_JustList, ActualKind: v.Kind()}
	}
	itr := v.ListIterator()
	for !itr.Done() {
		_, v, err := itr.Next()
		if err != nil {
			return err
		}
		if err := na.AssembleValue().AssignNode(v); err != nil {
			return err
		}
	}
	return na.Finish()
}
func (_Transaction__ReprAssembler) Prototype() datamodel.NodePrototype {
	return _Transaction__ReprPrototype{}
}
func (la *_Transaction__ReprAssembler) valueFinishTidy() bool {
	switch la.f {
	case 0:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 1:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 2:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 3:
		switch la.cm {
		case schema.Maybe_Value:
			la.cm = schema.Maybe_Absent
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	case 4:
		switch la.w.index.m {
		case schema.Maybe_Value:
			la.state = laState_initial
			la.f++
			return true
		case schema.Maybe_Null:
			la.state = laState_initial
			la.f++
			return true
		default:
			return false
		}
	default:
		panic("unreachable")
	}
}
func (la *_Transaction__ReprAssembler) AssembleValue() datamodel.NodeAssembler {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: AssembleValue cannot be called when still in the middle of assembling the previous value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: AssembleValue cannot be called on an assembler that's already finished")
	}
	if la.f >= 5 {
		return _ErrorThunkAssembler{schema.ErrNoSuchField{Type: nil /*TODO*/, Field: datamodel.PathSegmentOfInt(5)}}
	}
	la.state = laState_midValue
	switch la.f {
	case 0:
		la.ca_kind.w = &la.w.kind
		la.ca_kind.m = &la.cm
		return &la.ca_kind
	case 1:
		la.ca_data.w = &la.w.data
		la.ca_data.m = &la.cm
		return &la.ca_data
	case 2:
		la.ca_metadata.w = &la.w.metadata
		la.ca_metadata.m = &la.cm
		return &la.ca_metadata
	case 3:
		la.ca_slot.w = &la.w.slot
		la.ca_slot.m = &la.cm
		return &la.ca_slot
	case 4:
		la.ca_index.w = &la.w.index.v
		la.ca_index.m = &la.w.index.m
		la.w.index.m = allowNull
		return &la.ca_index
	default:
		panic("unreachable")
	}
}
func (la *_Transaction__ReprAssembler) Finish() error {
	switch la.state {
	case laState_initial:
		// carry on
	case laState_midValue:
		if !la.valueFinishTidy() {
			panic("invalid state: Finish cannot be called when in the middle of assembling a value")
		} // if tidy success: carry on
	case laState_finished:
		panic("invalid state: Finish cannot be called on an assembler that's already finished")
	}
	la.state = laState_finished
	*la.m = schema.Maybe_Value
	return nil
}
func (la *_Transaction__ReprAssembler) ValuePrototype(_ int64) datamodel.NodePrototype {
	panic("todo structbuilder tuplerepr valueprototype")
}
