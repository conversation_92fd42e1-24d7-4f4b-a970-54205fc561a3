# Welcome to <PERSON><PERSON><PERSON>!
#
# This config file is meant for settings that affect your whole blog, values
# which you are expected to set up once and rarely edit after that. If you find
# yourself editing this file very often, consider using <PERSON><PERSON><PERSON>'s data files
# feature for the data you need to update frequently.
#
# For technical reasons, this file is *NOT* reloaded automatically when you use
# 'bundle exec jekyll serve'. If you change this file, please restart the server process.
#
# If you need help with YAML syntax, here are some quick references for you:
# https://learn-the-web.algonquindesign.ca/topics/markdown-yaml-cheat-sheet/#yaml
# https://learnxinyminutes.com/docs/yaml/
#
# Site settings
# These are used to personalize your new site. If you look in the HTML files,
# you will see them accessed via {{ site.title }}, {{ site.email }}, and so on.
# You can create any custom variable you would like, and they will be accessible
# in the templates via {{ site.myvariable }}.

title: ♨️ Old Faithful
email: <EMAIL>
description: >- # this means to ignore newlines until "baseurl:"
    Old Faithful is part of Project Yellowstone, reinventing the Solana RPC stack.
baseurl: "" # the subpath of your site, e.g. /blog
url: "https://old-faithful.net" # the base hostname & protocol for your site, e.g. http://example.com
twitter_username: triton_one
github_username: rpcpool

# Build settings
theme: just-the-docs
plugins:
  - jekyll-feed
  - jemoji

callouts:
  warning:
    title: Warning
    color: red
  important:
    title: Important
    color: blue


aux_links:
    "Github":
      - "https://github.com/rpcpool/yellowstone-faithful"

# Exclude from processing.
# The following items will not be processed, by default.
# Any item listed under the `exclude:` key here will be automatically added to
# the internal "default list".
#
# Excluded items can be processed by explicitly listing the directories or
# their entries' file path in the `include:` list.
#
# exclude:
#   - .sass-cache/
#   - .jekyll-cache/
#   - gemfiles/
#   - Gemfile
#   - Gemfile.lock
#   - node_modules/
#   - vendor/bundle/
#   - vendor/cache/
#   - vendor/gems/
#   - vendor/ruby/
