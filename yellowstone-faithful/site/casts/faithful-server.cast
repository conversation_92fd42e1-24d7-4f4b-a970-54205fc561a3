{"version": 2, "width": 108, "height": 22, "timestamp": 1720043601, "env": {"SHELL": "/bin/bash", "TERM": "xterm-256color"}}
[0.438341, "o", "\u001b[0;32m@l<PERSON><PERSON><PERSON>l \u001b[0m➜ \u001b[1;34m/workspaces/yellowstone-faithful \u001b[0;36m(\u001b[1;31mmain\u001b[0;36m) \u001b[0m$ "]
[0.872981, "o", "."]
[1.189407, "o", "/"]
[1.262537, "o", "b"]
[1.372871, "o", "i"]
[1.514268, "o", "n"]
[1.895046, "o", "/"]
[2.014078, "o", "f"]
[2.223046, "o", "a"]
[2.223466, "o", "i"]
[2.369595, "o", "t"]
[2.46555, "o", "h"]
[2.577895, "o", "f"]
[2.656888, "o", "u"]
[2.836453, "o", "l"]
[3.061928, "o", "-"]
[3.195133, "o", "c"]
[3.289981, "o", "l"]
[3.403987, "o", "i"]
[3.758388, "o", " "]
[4.03714, "o", "r"]
[4.123142, "o", "p"]
[4.247963, "o", "c"]
[4.475683, "o", " "]
[4.508567, "o", "-"]
[4.718347, "o", "-"]
[5.245269, "o", "l"]
[5.370289, "o", "i"]
[5.483563, "o", "s"]
[5.546512, "o", "t"]
[5.626658, "o", "e"]
[5.640101, "o", "n"]
[5.932594, "o", " "]
[7.008748, "o", ":"]
[7.291596, "o", "8"]
[7.463547, "o", "8"]
[7.656623, "o", "9"]
[7.821498, "o", "9"]
[8.069117, "o", " "]
[8.759618, "o", "e"]
[8.780997, "o", "p"]
[9.002152, "o", "\u0007och"]
[9.777018, "o", "-"]
[9.904432, "o", "1"]
[10.193989, "o", "0"]
[10.400647, "o", "0.yml "]
[11.442892, "o", "\r\n"]
[11.458785, "o", "I0703 21:53:32.977215   41011 cmd-rpc.go:127] Found 1 config files:\r\n"]
[11.488237, "o", "I0703 21:53:33.008392   41011 cmd-rpc.go:153] Loaded 1 epoch configs\r\nI0703 21:53:33.008412   41011 cmd-rpc.go:154] Initializing epochs async...\r\n"]
[11.488654, "o", "I0703 21:53:33.008478   41011 multiepoch.go:232] RPC server listening on :8899\r\n"]
[11.489833, "o", "I0703 21:53:33.008670   41011 storage.go:31] opening index file from \"https://files.old-faithful.net/100/epoch-100-bafyreibqt2nvroysxlxctgb52xxn27ectsllv2xyka4qar7ga6vupmbs3i-mainnet-cid-to-offset-and-size.index\" as HTTP remote file\r\n"]
[13.206508, "o", "I0703 21:53:34.726664   41011 storage.go:31] opening index file from \"https://files.old-faithful.net/100/epoch-100-bafyreibqt2nvroysxlxctgb52xxn27ectsllv2xyka4qar7ga6vupmbs3i-mainnet-slot-to-cid.index\" as HTTP remote file\r\n"]
[13.615212, "o", "I0703 21:53:35.135383   41011 storage.go:31] opening index file from \"https://files.old-faithful.net/100/epoch-100-bafyreibqt2nvroysxlxctgb52xxn27ectsllv2xyka4qar7ga6vupmbs3i-mainnet-sig-to-cid.index\" as HTTP remote file\r\n"]
[15.158005, "o", "I0703 21:53:36.678197   41011 storage.go:65] opening CAR file from \"https://files.old-faithful.net/100/epoch-100.car\" as HTTP remote file\r\n"]
[15.637433, "o", "I0703 21:53:37.157604   41011 storage.go:31] opening index file from \"https://files.old-faithful.net/100/epoch-100-bafyreibqt2nvroysxlxctgb52xxn27ectsllv2xyka4qar7ga6vupmbs3i-mainnet-sig-exists.index\" as HTTP remote file\r\n"]
[16.429338, "o", "I0703 21:53:37.949539   41011 cmd-rpc.go:220] Initialized 1/1 epochs in 4.941076402s\r\n"]
