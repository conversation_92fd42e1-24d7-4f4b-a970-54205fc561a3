---
# Feel free to add content and custom Front Matter to this file.
# To modify the layout, see https://jekyllrb.com/docs/themes/#overriding-theme-defaults

layout: home
nav_order: 1
---
# :hotsprings: Project Yellowstone: Old Faithful

Old Faithful is the project to make all of Solana's history accessible, content addressable and available via a variety of means. The goal of this project is to completely replace the Bigtable dependency for Solana history access with a self-hostable, decentralized history archive that is usable for infrastructure providers, individual Solana users, explorers, indexers, and anyone else in need of historical access.

## Getting Started

- [The project report from Triton One](https://docs.triton.one/project-yellowstone/old-faithful-historical-archive/old-faithful-public-report)
- [Read the documentation](https://docs.old-faithful.net)
- [Install from Github](https://github.com/rpcpool/yellowstone-faithful)

{: .important-title }
> First production release
>
> The project has now got its first production release. This release is intended for wide deploy, but so far this is still early days. Expect some rough corners and glass chewing while the project matures.
>
> We appreciate all and any testing, questions, feedback, code submissions as issues via [github.com/rpcpool/yellowstone-faithful](https://github.com/rpcpool/yellowstone-faithful/issues). We can also be found on the [Solana tech discord](https://discord.com/invite/kBbATFA7PW) (e.g. @linuskendall).
