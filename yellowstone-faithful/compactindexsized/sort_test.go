package compactindexsized

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSort(t *testing.T) {
	entries := make([]uint, 50)
	for i := range entries {
		entries[i] = uint(i)
	}

	// Sort entries.
	sortWithCompare(entries, func(i, j int) bool {
		return entries[i] < entries[j]
	})

	sorted := []uint{0x1f, 0xf, 0x2a, 0x7, 0x17, 0x26, 0x2e, 0x3, 0xb, 0x13, 0x1b, 0x23, 0x28, 0x2c, 0x30, 0x1, 0x5, 0x9, 0xd, 0x11, 0x15, 0x19, 0x1d, 0x21, 0x25, 0x27, 0x29, 0x2b, 0x2d, 0x2f, 0x31, 0x0, 0x2, 0x4, 0x6, 0x8, 0xa, 0xc, 0xe, 0x10, 0x12, 0x14, 0x16, 0x18, 0x1a, 0x1c, 0x1e, 0x20, 0x22, 0x24}

	assert.Equal(t, sorted, entries)
}
