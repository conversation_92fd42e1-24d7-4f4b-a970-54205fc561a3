[workspace]
members = [
    "geyser-plugin-runner",
    "geyser-plugin-runner/demo-plugin",
    "old-faithful-proto",
    "txstatus",
]
resolver = "2"

[workspace.dependencies]
anyhow = "1.0.79"
base64 = "0.22.1"
bincode = "1.3.3"
bs58 = "0.5.0"
cbor = "0.4.1"
cid = "0.11.0"
colored = "2.1.0"
const-hex = "1.12.0"
crc = "3.0.1"
crossbeam-channel = "0.5.8"
fnv = "1.0.7"
multihash = "0.19.1"
prost = { package = "prost", version = "0.13.3" }
prost_011 = { package = "prost", version = "0.11.9" }
protobuf-src = "1.1.0"
serde = "1.0.188"
serde_cbor = "0.11"
serde_json = "1.0.108"
agave-geyser-plugin-interface = "~2.2.7"
solana-accounts-db = "~2.2.7"
solana-entry = "~2.2.7"
solana-geyser-plugin-manager = "~2.2.7"
solana-rpc = "~2.2.7"
solana-runtime = "~2.2.7"
solana-storage-proto = "~2.2.7"
solana-transaction-status = "~2.2.7"
# Solana SDK is separated now, see: https://github.com/anza-xyz/solana-sdk
solana-reward-info = "2.2.1"
solana-hash = "2.2.1"
solana-transaction = "2.2.1"
solana-pubkey = "2.2.1"
solana-message = "2.2.1"
solana-clock = "2.2.1"
solana-signature = "2.2.1"
solana-commitment-config = "2.2.1"
solana-transaction-error = "2.2.1"
# End of Solana SDK
tonic = "0.12.3"
tonic-build = "0.12.3"
tracing = "0.1.40"
zstd = "0.13.0"

[workspace.lints.clippy]
clone_on_ref_ptr = "deny"
missing_const_for_fn = "deny"
trivially_copy_pass_by_ref = "deny"

[profile.release]
lto = true
codegen-units = 1
