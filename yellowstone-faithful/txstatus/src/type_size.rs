#[allow(dead_code)]
pub const BOOL: usize = 1;
pub const BYTE: usize = 1;
#[allow(dead_code)]
pub const INT8: usize = 1;
#[allow(dead_code)]
pub const INT16: usize = 2;
#[allow(dead_code)]
pub const UINT8: usize = 1;
#[allow(dead_code)]
pub const UINT16: usize = 2;
pub const UINT32: usize = 4;
#[allow(dead_code)]
pub const UINT64: usize = 8;
#[allow(dead_code)]
pub const UINT128: usize = 16;
#[allow(dead_code)]
pub const FLOAT32: usize = 4;
#[allow(dead_code)]
pub const FLOAT64: usize = 8;
#[allow(dead_code)]
pub const PUBLIC_KEY: usize = 32;
#[allow(dead_code)]
pub const SIGNATURE: usize = 64;
