concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

name: Data Report Generator
on:
  push:
  schedule:
    - cron: '3 */4 * * *'  # Run every 4 hours
  workflow_dispatch:  # Allow manual trigger

jobs:
  generate-report:
    runs-on: ubuntu-latest
    
    permissions:
      # Give the default GITHUB_TOKEN write permission to commit and push the
      # added or changed files to the repository.
      contents: write

    steps:
      - uses: actions/checkout@v4
        with:
          ref: gha-report  # Checkout the gha-report branch
          fetch-depth: 0   # Fetch all history to ensure we have the latest changes
        
      - name: Generate Report
        run: |
          mkdir -p docs
          python3 -m pip install aiohttp
          python3 .github/faithful-data-report.py > docs/CAR-REPORT.md
          
      # Commit changed files back to the repository
      - uses: EndBug/add-and-commit@v9
        with:
          message: Old Faithful CAR data report update
          add: 'docs/CAR-REPORT.md'  # Only add this specific file
          author_name: github-actions[bot]
          author_email: 41898282+github-actions[bot]@users.noreply.github.com
          push: origin gha-report

