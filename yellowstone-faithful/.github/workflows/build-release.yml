name: main

on:
  push:
    tags:
      - 'v*.*.*'

jobs:
  build:
    runs-on: ubuntu-20.04

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup go env
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'
          check-latest: true

      - name: Build cli
        run: make compile-all

      - name: Publish to release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/')
        with:
          token: ${{ github.token }}
          generate_release_notes: true
          files: |
            bin/darwin/amd64/faithful-cli_darwin_amd64
            bin/darwin/arm64/faithful-cli_darwin_arm64
            bin/linux/amd64/faithful-cli_linux_amd64
            bin/windows/amd64/faithful-cli_windows_amd64.exe
