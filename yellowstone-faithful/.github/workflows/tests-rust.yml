concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    paths:
      - "Cargo.toml"
      - "Cargo.lock"
      - "rust-toolchain.toml"
      - ".github/workflows/tests-rust.yml"
      - "txstatus/**"
      - "old-faithful-proto/**"
      - "geyser-plugin-runner/**"
      - "tests/rust/**"
  workflow_dispatch:
    paths:
      - "Cargo.toml"
      - "Cargo.lock"
      - "rust-toolchain.toml"
      - ".github/workflows/tests-rust.yml"
      - "txstatus/**"
      - "old-faithful-proto/**"
      - "geyser-plugin-runner/**"
      - "tests/rust/**"

env:
  CARGO_TERM_COLOR: always

name: tests_rust
jobs:
  test_rust:
    strategy:
      matrix:
        os: [ubuntu-22.04]

    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4

      - name: Install protoc
        run: |
          sudo apt-get update
          sudo apt-get install -y protobuf-compiler libudev-dev

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target
          key: ${{ matrix.os }}-cargo-${{ hashFiles('rust-toolchain.toml') }}-${{ hashFiles('**/Cargo.lock') }}-0000
          restore-keys: |
            ${{ matrix.os }}-cargo-${{ hashFiles('rust-toolchain.toml') }}

      - name: Set rust version
        run: |
          RUST_VERSION="$(grep -oP 'channel = "\K\d\.\d+\.\d+(?=")' rust-toolchain.toml)"
          echo "RUST_VERSION=$RUST_VERSION" >> "$GITHUB_ENV"

      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: nightly
          components: rustfmt

      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ env.RUST_VERSION }}
          components: clippy

      - name: cargo tree
        run: |
          cargo tree
          git checkout Cargo.lock
          cargo tree --frozen --offline

      - name: cargo fmt
        run: cargo +nightly fmt --all -- --check

      - name: Run clippy
        run: cargo clippy --workspace --all-targets --tests -- -Dwarnings

      - name: Run test
        run: cargo test --all-targets
