// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.27.1
// source: transaction_by_addr.proto

// https://github.com/solana-labs/solana/blob/63baab57fc324937dc1d72b288ba55279c8e793e/storage-proto/proto/transaction_by_addr.proto

package transaction_by_addr

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TransactionErrorType int32

const (
	TransactionErrorType_ACCOUNT_IN_USE                           TransactionErrorType = 0
	TransactionErrorType_ACCOUNT_LOADED_TWICE                     TransactionErrorType = 1
	TransactionErrorType_ACCOUNT_NOT_FOUND                        TransactionErrorType = 2
	TransactionErrorType_PROGRAM_ACCOUNT_NOT_FOUND                TransactionErrorType = 3
	TransactionErrorType_INSUFFICIENT_FUNDS_FOR_FEE               TransactionErrorType = 4
	TransactionErrorType_INVALID_ACCOUNT_FOR_FEE                  TransactionErrorType = 5
	TransactionErrorType_ALREADY_PROCESSED                        TransactionErrorType = 6
	TransactionErrorType_BLOCKHASH_NOT_FOUND                      TransactionErrorType = 7
	TransactionErrorType_INSTRUCTION_ERROR                        TransactionErrorType = 8
	TransactionErrorType_CALL_CHAIN_TOO_DEEP                      TransactionErrorType = 9
	TransactionErrorType_MISSING_SIGNATURE_FOR_FEE                TransactionErrorType = 10
	TransactionErrorType_INVALID_ACCOUNT_INDEX                    TransactionErrorType = 11
	TransactionErrorType_SIGNATURE_FAILURE                        TransactionErrorType = 12
	TransactionErrorType_INVALID_PROGRAM_FOR_EXECUTION            TransactionErrorType = 13
	TransactionErrorType_SANITIZE_FAILURE                         TransactionErrorType = 14
	TransactionErrorType_CLUSTER_MAINTENANCE                      TransactionErrorType = 15
	TransactionErrorType_ACCOUNT_BORROW_OUTSTANDING_TX            TransactionErrorType = 16
	TransactionErrorType_WOULD_EXCEED_MAX_BLOCK_COST_LIMIT        TransactionErrorType = 17
	TransactionErrorType_UNSUPPORTED_VERSION                      TransactionErrorType = 18
	TransactionErrorType_INVALID_WRITABLE_ACCOUNT                 TransactionErrorType = 19
	TransactionErrorType_WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT      TransactionErrorType = 20
	TransactionErrorType_WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT    TransactionErrorType = 21
	TransactionErrorType_TOO_MANY_ACCOUNT_LOCKS                   TransactionErrorType = 22
	TransactionErrorType_ADDRESS_LOOKUP_TABLE_NOT_FOUND           TransactionErrorType = 23
	TransactionErrorType_INVALID_ADDRESS_LOOKUP_TABLE_OWNER       TransactionErrorType = 24
	TransactionErrorType_INVALID_ADDRESS_LOOKUP_TABLE_DATA        TransactionErrorType = 25
	TransactionErrorType_INVALID_ADDRESS_LOOKUP_TABLE_INDEX       TransactionErrorType = 26
	TransactionErrorType_INVALID_RENT_PAYING_ACCOUNT              TransactionErrorType = 27
	TransactionErrorType_WOULD_EXCEED_MAX_VOTE_COST_LIMIT         TransactionErrorType = 28
	TransactionErrorType_WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT    TransactionErrorType = 29
	TransactionErrorType_DUPLICATE_INSTRUCTION                    TransactionErrorType = 30
	TransactionErrorType_INSUFFICIENT_FUNDS_FOR_RENT              TransactionErrorType = 31
	TransactionErrorType_MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED   TransactionErrorType = 32
	TransactionErrorType_INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT  TransactionErrorType = 33
	TransactionErrorType_RESANITIZATION_NEEDED                    TransactionErrorType = 34
	TransactionErrorType_PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED TransactionErrorType = 35
	TransactionErrorType_UNBALANCED_TRANSACTION                   TransactionErrorType = 36
	TransactionErrorType_PROGRAM_CACHE_HIT_MAX_LIMIT              TransactionErrorType = 37
)

// Enum value maps for TransactionErrorType.
var (
	TransactionErrorType_name = map[int32]string{
		0:  "ACCOUNT_IN_USE",
		1:  "ACCOUNT_LOADED_TWICE",
		2:  "ACCOUNT_NOT_FOUND",
		3:  "PROGRAM_ACCOUNT_NOT_FOUND",
		4:  "INSUFFICIENT_FUNDS_FOR_FEE",
		5:  "INVALID_ACCOUNT_FOR_FEE",
		6:  "ALREADY_PROCESSED",
		7:  "BLOCKHASH_NOT_FOUND",
		8:  "INSTRUCTION_ERROR",
		9:  "CALL_CHAIN_TOO_DEEP",
		10: "MISSING_SIGNATURE_FOR_FEE",
		11: "INVALID_ACCOUNT_INDEX",
		12: "SIGNATURE_FAILURE",
		13: "INVALID_PROGRAM_FOR_EXECUTION",
		14: "SANITIZE_FAILURE",
		15: "CLUSTER_MAINTENANCE",
		16: "ACCOUNT_BORROW_OUTSTANDING_TX",
		17: "WOULD_EXCEED_MAX_BLOCK_COST_LIMIT",
		18: "UNSUPPORTED_VERSION",
		19: "INVALID_WRITABLE_ACCOUNT",
		20: "WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT",
		21: "WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT",
		22: "TOO_MANY_ACCOUNT_LOCKS",
		23: "ADDRESS_LOOKUP_TABLE_NOT_FOUND",
		24: "INVALID_ADDRESS_LOOKUP_TABLE_OWNER",
		25: "INVALID_ADDRESS_LOOKUP_TABLE_DATA",
		26: "INVALID_ADDRESS_LOOKUP_TABLE_INDEX",
		27: "INVALID_RENT_PAYING_ACCOUNT",
		28: "WOULD_EXCEED_MAX_VOTE_COST_LIMIT",
		29: "WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT",
		30: "DUPLICATE_INSTRUCTION",
		31: "INSUFFICIENT_FUNDS_FOR_RENT",
		32: "MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED",
		33: "INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT",
		34: "RESANITIZATION_NEEDED",
		35: "PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED",
		36: "UNBALANCED_TRANSACTION",
		37: "PROGRAM_CACHE_HIT_MAX_LIMIT",
	}
	TransactionErrorType_value = map[string]int32{
		"ACCOUNT_IN_USE":                           0,
		"ACCOUNT_LOADED_TWICE":                     1,
		"ACCOUNT_NOT_FOUND":                        2,
		"PROGRAM_ACCOUNT_NOT_FOUND":                3,
		"INSUFFICIENT_FUNDS_FOR_FEE":               4,
		"INVALID_ACCOUNT_FOR_FEE":                  5,
		"ALREADY_PROCESSED":                        6,
		"BLOCKHASH_NOT_FOUND":                      7,
		"INSTRUCTION_ERROR":                        8,
		"CALL_CHAIN_TOO_DEEP":                      9,
		"MISSING_SIGNATURE_FOR_FEE":                10,
		"INVALID_ACCOUNT_INDEX":                    11,
		"SIGNATURE_FAILURE":                        12,
		"INVALID_PROGRAM_FOR_EXECUTION":            13,
		"SANITIZE_FAILURE":                         14,
		"CLUSTER_MAINTENANCE":                      15,
		"ACCOUNT_BORROW_OUTSTANDING_TX":            16,
		"WOULD_EXCEED_MAX_BLOCK_COST_LIMIT":        17,
		"UNSUPPORTED_VERSION":                      18,
		"INVALID_WRITABLE_ACCOUNT":                 19,
		"WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT":      20,
		"WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT":    21,
		"TOO_MANY_ACCOUNT_LOCKS":                   22,
		"ADDRESS_LOOKUP_TABLE_NOT_FOUND":           23,
		"INVALID_ADDRESS_LOOKUP_TABLE_OWNER":       24,
		"INVALID_ADDRESS_LOOKUP_TABLE_DATA":        25,
		"INVALID_ADDRESS_LOOKUP_TABLE_INDEX":       26,
		"INVALID_RENT_PAYING_ACCOUNT":              27,
		"WOULD_EXCEED_MAX_VOTE_COST_LIMIT":         28,
		"WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT":    29,
		"DUPLICATE_INSTRUCTION":                    30,
		"INSUFFICIENT_FUNDS_FOR_RENT":              31,
		"MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED":   32,
		"INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT":  33,
		"RESANITIZATION_NEEDED":                    34,
		"PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED": 35,
		"UNBALANCED_TRANSACTION":                   36,
		"PROGRAM_CACHE_HIT_MAX_LIMIT":              37,
	}
)

func (x TransactionErrorType) Enum() *TransactionErrorType {
	p := new(TransactionErrorType)
	*p = x
	return p
}

func (x TransactionErrorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionErrorType) Descriptor() protoreflect.EnumDescriptor {
	return file_transaction_by_addr_proto_enumTypes[0].Descriptor()
}

func (TransactionErrorType) Type() protoreflect.EnumType {
	return &file_transaction_by_addr_proto_enumTypes[0]
}

func (x TransactionErrorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionErrorType.Descriptor instead.
func (TransactionErrorType) EnumDescriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{0}
}

type InstructionErrorType int32

const (
	InstructionErrorType_GENERIC_ERROR                               InstructionErrorType = 0
	InstructionErrorType_INVALID_ARGUMENT                            InstructionErrorType = 1
	InstructionErrorType_INVALID_INSTRUCTION_DATA                    InstructionErrorType = 2
	InstructionErrorType_INVALID_ACCOUNT_DATA                        InstructionErrorType = 3
	InstructionErrorType_ACCOUNT_DATA_TOO_SMALL                      InstructionErrorType = 4
	InstructionErrorType_INSUFFICIENT_FUNDS                          InstructionErrorType = 5
	InstructionErrorType_INCORRECT_PROGRAM_ID                        InstructionErrorType = 6
	InstructionErrorType_MISSING_REQUIRED_SIGNATURE                  InstructionErrorType = 7
	InstructionErrorType_ACCOUNT_ALREADY_INITIALIZED                 InstructionErrorType = 8
	InstructionErrorType_UNINITIALIZED_ACCOUNT                       InstructionErrorType = 9
	InstructionErrorType_UNBALANCED_INSTRUCTION                      InstructionErrorType = 10
	InstructionErrorType_MODIFIED_PROGRAM_ID                         InstructionErrorType = 11
	InstructionErrorType_EXTERNAL_ACCOUNT_LAMPORT_SPEND              InstructionErrorType = 12
	InstructionErrorType_EXTERNAL_ACCOUNT_DATA_MODIFIED              InstructionErrorType = 13
	InstructionErrorType_READONLY_LAMPORT_CHANGE                     InstructionErrorType = 14
	InstructionErrorType_READONLY_DATA_MODIFIED                      InstructionErrorType = 15
	InstructionErrorType_DUPLICATE_ACCOUNT_INDEX                     InstructionErrorType = 16
	InstructionErrorType_EXECUTABLE_MODIFIED                         InstructionErrorType = 17
	InstructionErrorType_RENT_EPOCH_MODIFIED                         InstructionErrorType = 18
	InstructionErrorType_NOT_ENOUGH_ACCOUNT_KEYS                     InstructionErrorType = 19
	InstructionErrorType_ACCOUNT_DATA_SIZE_CHANGED                   InstructionErrorType = 20
	InstructionErrorType_ACCOUNT_NOT_EXECUTABLE                      InstructionErrorType = 21
	InstructionErrorType_ACCOUNT_BORROW_FAILED                       InstructionErrorType = 22
	InstructionErrorType_ACCOUNT_BORROW_OUTSTANDING                  InstructionErrorType = 23
	InstructionErrorType_DUPLICATE_ACCOUNT_OUT_OF_SYNC               InstructionErrorType = 24
	InstructionErrorType_CUSTOM                                      InstructionErrorType = 25
	InstructionErrorType_INVALID_ERROR                               InstructionErrorType = 26
	InstructionErrorType_EXECUTABLE_DATA_MODIFIED                    InstructionErrorType = 27
	InstructionErrorType_EXECUTABLE_LAMPORT_CHANGE                   InstructionErrorType = 28
	InstructionErrorType_EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT          InstructionErrorType = 29
	InstructionErrorType_UNSUPPORTED_PROGRAM_ID                      InstructionErrorType = 30
	InstructionErrorType_CALL_DEPTH                                  InstructionErrorType = 31
	InstructionErrorType_MISSING_ACCOUNT                             InstructionErrorType = 32
	InstructionErrorType_REENTRANCY_NOT_ALLOWED                      InstructionErrorType = 33
	InstructionErrorType_MAX_SEED_LENGTH_EXCEEDED                    InstructionErrorType = 34
	InstructionErrorType_INVALID_SEEDS                               InstructionErrorType = 35
	InstructionErrorType_INVALID_REALLOC                             InstructionErrorType = 36
	InstructionErrorType_COMPUTATIONAL_BUDGET_EXCEEDED               InstructionErrorType = 37
	InstructionErrorType_PRIVILEGE_ESCALATION                        InstructionErrorType = 38
	InstructionErrorType_PROGRAM_ENVIRONMENT_SETUP_FAILURE           InstructionErrorType = 39
	InstructionErrorType_PROGRAM_FAILED_TO_COMPLETE                  InstructionErrorType = 40
	InstructionErrorType_PROGRAM_FAILED_TO_COMPILE                   InstructionErrorType = 41
	InstructionErrorType_IMMUTABLE                                   InstructionErrorType = 42
	InstructionErrorType_INCORRECT_AUTHORITY                         InstructionErrorType = 43
	InstructionErrorType_BORSH_IO_ERROR                              InstructionErrorType = 44
	InstructionErrorType_ACCOUNT_NOT_RENT_EXEMPT                     InstructionErrorType = 45
	InstructionErrorType_INVALID_ACCOUNT_OWNER                       InstructionErrorType = 46
	InstructionErrorType_ARITHMETIC_OVERFLOW                         InstructionErrorType = 47
	InstructionErrorType_UNSUPPORTED_SYSVAR                          InstructionErrorType = 48
	InstructionErrorType_ILLEGAL_OWNER                               InstructionErrorType = 49
	InstructionErrorType_MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED      InstructionErrorType = 50
	InstructionErrorType_MAX_ACCOUNTS_EXCEEDED                       InstructionErrorType = 51
	InstructionErrorType_MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED       InstructionErrorType = 52
	InstructionErrorType_BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS InstructionErrorType = 53
)

// Enum value maps for InstructionErrorType.
var (
	InstructionErrorType_name = map[int32]string{
		0:  "GENERIC_ERROR",
		1:  "INVALID_ARGUMENT",
		2:  "INVALID_INSTRUCTION_DATA",
		3:  "INVALID_ACCOUNT_DATA",
		4:  "ACCOUNT_DATA_TOO_SMALL",
		5:  "INSUFFICIENT_FUNDS",
		6:  "INCORRECT_PROGRAM_ID",
		7:  "MISSING_REQUIRED_SIGNATURE",
		8:  "ACCOUNT_ALREADY_INITIALIZED",
		9:  "UNINITIALIZED_ACCOUNT",
		10: "UNBALANCED_INSTRUCTION",
		11: "MODIFIED_PROGRAM_ID",
		12: "EXTERNAL_ACCOUNT_LAMPORT_SPEND",
		13: "EXTERNAL_ACCOUNT_DATA_MODIFIED",
		14: "READONLY_LAMPORT_CHANGE",
		15: "READONLY_DATA_MODIFIED",
		16: "DUPLICATE_ACCOUNT_INDEX",
		17: "EXECUTABLE_MODIFIED",
		18: "RENT_EPOCH_MODIFIED",
		19: "NOT_ENOUGH_ACCOUNT_KEYS",
		20: "ACCOUNT_DATA_SIZE_CHANGED",
		21: "ACCOUNT_NOT_EXECUTABLE",
		22: "ACCOUNT_BORROW_FAILED",
		23: "ACCOUNT_BORROW_OUTSTANDING",
		24: "DUPLICATE_ACCOUNT_OUT_OF_SYNC",
		25: "CUSTOM",
		26: "INVALID_ERROR",
		27: "EXECUTABLE_DATA_MODIFIED",
		28: "EXECUTABLE_LAMPORT_CHANGE",
		29: "EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT",
		30: "UNSUPPORTED_PROGRAM_ID",
		31: "CALL_DEPTH",
		32: "MISSING_ACCOUNT",
		33: "REENTRANCY_NOT_ALLOWED",
		34: "MAX_SEED_LENGTH_EXCEEDED",
		35: "INVALID_SEEDS",
		36: "INVALID_REALLOC",
		37: "COMPUTATIONAL_BUDGET_EXCEEDED",
		38: "PRIVILEGE_ESCALATION",
		39: "PROGRAM_ENVIRONMENT_SETUP_FAILURE",
		40: "PROGRAM_FAILED_TO_COMPLETE",
		41: "PROGRAM_FAILED_TO_COMPILE",
		42: "IMMUTABLE",
		43: "INCORRECT_AUTHORITY",
		44: "BORSH_IO_ERROR",
		45: "ACCOUNT_NOT_RENT_EXEMPT",
		46: "INVALID_ACCOUNT_OWNER",
		47: "ARITHMETIC_OVERFLOW",
		48: "UNSUPPORTED_SYSVAR",
		49: "ILLEGAL_OWNER",
		50: "MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED",
		51: "MAX_ACCOUNTS_EXCEEDED",
		52: "MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED",
		53: "BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS",
	}
	InstructionErrorType_value = map[string]int32{
		"GENERIC_ERROR":                               0,
		"INVALID_ARGUMENT":                            1,
		"INVALID_INSTRUCTION_DATA":                    2,
		"INVALID_ACCOUNT_DATA":                        3,
		"ACCOUNT_DATA_TOO_SMALL":                      4,
		"INSUFFICIENT_FUNDS":                          5,
		"INCORRECT_PROGRAM_ID":                        6,
		"MISSING_REQUIRED_SIGNATURE":                  7,
		"ACCOUNT_ALREADY_INITIALIZED":                 8,
		"UNINITIALIZED_ACCOUNT":                       9,
		"UNBALANCED_INSTRUCTION":                      10,
		"MODIFIED_PROGRAM_ID":                         11,
		"EXTERNAL_ACCOUNT_LAMPORT_SPEND":              12,
		"EXTERNAL_ACCOUNT_DATA_MODIFIED":              13,
		"READONLY_LAMPORT_CHANGE":                     14,
		"READONLY_DATA_MODIFIED":                      15,
		"DUPLICATE_ACCOUNT_INDEX":                     16,
		"EXECUTABLE_MODIFIED":                         17,
		"RENT_EPOCH_MODIFIED":                         18,
		"NOT_ENOUGH_ACCOUNT_KEYS":                     19,
		"ACCOUNT_DATA_SIZE_CHANGED":                   20,
		"ACCOUNT_NOT_EXECUTABLE":                      21,
		"ACCOUNT_BORROW_FAILED":                       22,
		"ACCOUNT_BORROW_OUTSTANDING":                  23,
		"DUPLICATE_ACCOUNT_OUT_OF_SYNC":               24,
		"CUSTOM":                                      25,
		"INVALID_ERROR":                               26,
		"EXECUTABLE_DATA_MODIFIED":                    27,
		"EXECUTABLE_LAMPORT_CHANGE":                   28,
		"EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT":          29,
		"UNSUPPORTED_PROGRAM_ID":                      30,
		"CALL_DEPTH":                                  31,
		"MISSING_ACCOUNT":                             32,
		"REENTRANCY_NOT_ALLOWED":                      33,
		"MAX_SEED_LENGTH_EXCEEDED":                    34,
		"INVALID_SEEDS":                               35,
		"INVALID_REALLOC":                             36,
		"COMPUTATIONAL_BUDGET_EXCEEDED":               37,
		"PRIVILEGE_ESCALATION":                        38,
		"PROGRAM_ENVIRONMENT_SETUP_FAILURE":           39,
		"PROGRAM_FAILED_TO_COMPLETE":                  40,
		"PROGRAM_FAILED_TO_COMPILE":                   41,
		"IMMUTABLE":                                   42,
		"INCORRECT_AUTHORITY":                         43,
		"BORSH_IO_ERROR":                              44,
		"ACCOUNT_NOT_RENT_EXEMPT":                     45,
		"INVALID_ACCOUNT_OWNER":                       46,
		"ARITHMETIC_OVERFLOW":                         47,
		"UNSUPPORTED_SYSVAR":                          48,
		"ILLEGAL_OWNER":                               49,
		"MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED":      50,
		"MAX_ACCOUNTS_EXCEEDED":                       51,
		"MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED":       52,
		"BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS": 53,
	}
)

func (x InstructionErrorType) Enum() *InstructionErrorType {
	p := new(InstructionErrorType)
	*p = x
	return p
}

func (x InstructionErrorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InstructionErrorType) Descriptor() protoreflect.EnumDescriptor {
	return file_transaction_by_addr_proto_enumTypes[1].Descriptor()
}

func (InstructionErrorType) Type() protoreflect.EnumType {
	return &file_transaction_by_addr_proto_enumTypes[1]
}

func (x InstructionErrorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InstructionErrorType.Descriptor instead.
func (InstructionErrorType) EnumDescriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{1}
}

type TransactionByAddr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxByAddrs []*TransactionByAddrInfo `protobuf:"bytes,1,rep,name=tx_by_addrs,json=txByAddrs,proto3" json:"tx_by_addrs,omitempty"`
}

func (x *TransactionByAddr) Reset() {
	*x = TransactionByAddr{}
	mi := &file_transaction_by_addr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionByAddr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionByAddr) ProtoMessage() {}

func (x *TransactionByAddr) ProtoReflect() protoreflect.Message {
	mi := &file_transaction_by_addr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionByAddr.ProtoReflect.Descriptor instead.
func (*TransactionByAddr) Descriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{0}
}

func (x *TransactionByAddr) GetTxByAddrs() []*TransactionByAddrInfo {
	if x != nil {
		return x.TxByAddrs
	}
	return nil
}

type TransactionByAddrInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Signature []byte            `protobuf:"bytes,1,opt,name=signature,proto3" json:"signature,omitempty"`
	Err       *TransactionError `protobuf:"bytes,2,opt,name=err,proto3" json:"err,omitempty"`
	Index     uint32            `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	Memo      *Memo             `protobuf:"bytes,4,opt,name=memo,proto3" json:"memo,omitempty"`
	BlockTime *UnixTimestamp    `protobuf:"bytes,5,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
}

func (x *TransactionByAddrInfo) Reset() {
	*x = TransactionByAddrInfo{}
	mi := &file_transaction_by_addr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionByAddrInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionByAddrInfo) ProtoMessage() {}

func (x *TransactionByAddrInfo) ProtoReflect() protoreflect.Message {
	mi := &file_transaction_by_addr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionByAddrInfo.ProtoReflect.Descriptor instead.
func (*TransactionByAddrInfo) Descriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{1}
}

func (x *TransactionByAddrInfo) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *TransactionByAddrInfo) GetErr() *TransactionError {
	if x != nil {
		return x.Err
	}
	return nil
}

func (x *TransactionByAddrInfo) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *TransactionByAddrInfo) GetMemo() *Memo {
	if x != nil {
		return x.Memo
	}
	return nil
}

func (x *TransactionByAddrInfo) GetBlockTime() *UnixTimestamp {
	if x != nil {
		return x.BlockTime
	}
	return nil
}

type Memo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Memo string `protobuf:"bytes,1,opt,name=memo,proto3" json:"memo,omitempty"`
}

func (x *Memo) Reset() {
	*x = Memo{}
	mi := &file_transaction_by_addr_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Memo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Memo) ProtoMessage() {}

func (x *Memo) ProtoReflect() protoreflect.Message {
	mi := &file_transaction_by_addr_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Memo.ProtoReflect.Descriptor instead.
func (*Memo) Descriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{2}
}

func (x *Memo) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

type TransactionError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionError   TransactionErrorType `protobuf:"varint,1,opt,name=transaction_error,json=transactionError,proto3,enum=solana.storage.TransactionByAddr.TransactionErrorType" json:"transaction_error,omitempty"`
	InstructionError   *InstructionError    `protobuf:"bytes,2,opt,name=instruction_error,json=instructionError,proto3" json:"instruction_error,omitempty"`
	TransactionDetails *TransactionDetails  `protobuf:"bytes,3,opt,name=transaction_details,json=transactionDetails,proto3" json:"transaction_details,omitempty"`
}

func (x *TransactionError) Reset() {
	*x = TransactionError{}
	mi := &file_transaction_by_addr_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionError) ProtoMessage() {}

func (x *TransactionError) ProtoReflect() protoreflect.Message {
	mi := &file_transaction_by_addr_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionError.ProtoReflect.Descriptor instead.
func (*TransactionError) Descriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{3}
}

func (x *TransactionError) GetTransactionError() TransactionErrorType {
	if x != nil {
		return x.TransactionError
	}
	return TransactionErrorType_ACCOUNT_IN_USE
}

func (x *TransactionError) GetInstructionError() *InstructionError {
	if x != nil {
		return x.InstructionError
	}
	return nil
}

func (x *TransactionError) GetTransactionDetails() *TransactionDetails {
	if x != nil {
		return x.TransactionDetails
	}
	return nil
}

type InstructionError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index  uint32               `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Error  InstructionErrorType `protobuf:"varint,2,opt,name=error,proto3,enum=solana.storage.TransactionByAddr.InstructionErrorType" json:"error,omitempty"`
	Custom *CustomError         `protobuf:"bytes,3,opt,name=custom,proto3" json:"custom,omitempty"`
}

func (x *InstructionError) Reset() {
	*x = InstructionError{}
	mi := &file_transaction_by_addr_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstructionError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstructionError) ProtoMessage() {}

func (x *InstructionError) ProtoReflect() protoreflect.Message {
	mi := &file_transaction_by_addr_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstructionError.ProtoReflect.Descriptor instead.
func (*InstructionError) Descriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{4}
}

func (x *InstructionError) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *InstructionError) GetError() InstructionErrorType {
	if x != nil {
		return x.Error
	}
	return InstructionErrorType_GENERIC_ERROR
}

func (x *InstructionError) GetCustom() *CustomError {
	if x != nil {
		return x.Custom
	}
	return nil
}

type TransactionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
}

func (x *TransactionDetails) Reset() {
	*x = TransactionDetails{}
	mi := &file_transaction_by_addr_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetails) ProtoMessage() {}

func (x *TransactionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_transaction_by_addr_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetails.ProtoReflect.Descriptor instead.
func (*TransactionDetails) Descriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{5}
}

func (x *TransactionDetails) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type UnixTimestamp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp int64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *UnixTimestamp) Reset() {
	*x = UnixTimestamp{}
	mi := &file_transaction_by_addr_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnixTimestamp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnixTimestamp) ProtoMessage() {}

func (x *UnixTimestamp) ProtoReflect() protoreflect.Message {
	mi := &file_transaction_by_addr_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnixTimestamp.ProtoReflect.Descriptor instead.
func (*UnixTimestamp) Descriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{6}
}

func (x *UnixTimestamp) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type CustomError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Custom uint32 `protobuf:"varint,1,opt,name=custom,proto3" json:"custom,omitempty"`
}

func (x *CustomError) Reset() {
	*x = CustomError{}
	mi := &file_transaction_by_addr_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomError) ProtoMessage() {}

func (x *CustomError) ProtoReflect() protoreflect.Message {
	mi := &file_transaction_by_addr_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomError.ProtoReflect.Descriptor instead.
func (*CustomError) Descriptor() ([]byte, []int) {
	return file_transaction_by_addr_proto_rawDescGZIP(), []int{7}
}

func (x *CustomError) GetCustom() uint32 {
	if x != nil {
		return x.Custom
	}
	return 0
}

var File_transaction_by_addr_proto protoreflect.FileDescriptor

var file_transaction_by_addr_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x79,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x73, 0x6f, 0x6c,
	0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x22, 0x6c, 0x0a,
	0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64,
	0x64, 0x72, 0x12, 0x57, 0x0a, 0x0b, 0x74, 0x78, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x74, 0x78, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x73, 0x22, 0x9d, 0x02, 0x0a, 0x15,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x64,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x12, 0x44, 0x0a, 0x03, 0x65, 0x72, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x03, 0x65, 0x72, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x3a, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72,
	0x2e, 0x4d, 0x65, 0x6d, 0x6f, 0x52, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x12, 0x4e, 0x0a, 0x0a, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64,
	0x64, 0x72, 0x2e, 0x55, 0x6e, 0x69, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x1a, 0x0a, 0x04, 0x4d,
	0x65, 0x6d, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x22, 0xbf, 0x02, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x63, 0x0a, 0x11,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x5f, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73,
	0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x2e,
	0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x10, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x65, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x64,
	0x64, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xbd, 0x01, 0x0a, 0x10, 0x49, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x4c, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x79, 0x41, 0x64, 0x64, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x45, 0x0a, 0x06, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x79, 0x41, 0x64, 0x64, 0x72, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x06, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x22, 0x2a, 0x0a, 0x12, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x2d, 0x0a, 0x0d, 0x55, 0x6e, 0x69, 0x78, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x22, 0x25, 0x0a, 0x0b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x2a, 0xd3, 0x09, 0x0a, 0x14,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x49, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x45, 0x44, 0x5f, 0x54, 0x57, 0x49, 0x43, 0x45,
	0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x4e, 0x53, 0x55,
	0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x46, 0x45, 0x45, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x48, 0x41, 0x53, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x08, 0x12, 0x17, 0x0a, 0x13,
	0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x48, 0x41, 0x49, 0x4e, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x44,
	0x45, 0x45, 0x50, 0x10, 0x09, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x46,
	0x45, 0x45, 0x10, 0x0a, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x10, 0x0b, 0x12,
	0x15, 0x0a, 0x11, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x10, 0x0c, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x41, 0x4e,
	0x49, 0x54, 0x49, 0x5a, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x0e, 0x12,
	0x17, 0x0a, 0x13, 0x43, 0x4c, 0x55, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54,
	0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x0f, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x42, 0x4f, 0x52, 0x52, 0x4f, 0x57, 0x5f, 0x4f, 0x55, 0x54, 0x53, 0x54,
	0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x58, 0x10, 0x10, 0x12, 0x25, 0x0a, 0x21, 0x57,
	0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x58, 0x5f,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x4f, 0x53, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x10, 0x11, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45,
	0x44, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x12, 0x12, 0x1c, 0x0a, 0x18, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x13, 0x12, 0x27, 0x0a, 0x23, 0x57, 0x4f, 0x55,
	0x4c, 0x44, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x53, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x10, 0x14, 0x12, 0x29, 0x0a, 0x25, 0x57, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x45, 0x58, 0x43, 0x45,
	0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x15, 0x12, 0x1a, 0x0a,
	0x16, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x16, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x44, 0x44,
	0x52, 0x45, 0x53, 0x53, 0x5f, 0x4c, 0x4f, 0x4f, 0x4b, 0x55, 0x50, 0x5f, 0x54, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x17, 0x12, 0x26, 0x0a,
	0x22, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x4c, 0x4f, 0x4f, 0x4b, 0x55, 0x50, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4f, 0x57,
	0x4e, 0x45, 0x52, 0x10, 0x18, 0x12, 0x25, 0x0a, 0x21, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x4c, 0x4f, 0x4f, 0x4b, 0x55, 0x50, 0x5f,
	0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x19, 0x12, 0x26, 0x0a, 0x22,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f,
	0x4c, 0x4f, 0x4f, 0x4b, 0x55, 0x50, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x44,
	0x45, 0x58, 0x10, 0x1a, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x52, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x1b, 0x12, 0x24, 0x0a, 0x20, 0x57, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x45,
	0x58, 0x43, 0x45, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x56, 0x4f, 0x54, 0x45, 0x5f, 0x43,
	0x4f, 0x53, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x1c, 0x12, 0x29, 0x0a, 0x25, 0x57,
	0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x10, 0x1d, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x1e, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x4e, 0x54,
	0x10, 0x1f, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x41, 0x58, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x45, 0x44,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53,
	0x49, 0x5a, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x20, 0x12, 0x2b,
	0x0a, 0x27, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x45, 0x44,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53,
	0x49, 0x5a, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x21, 0x12, 0x19, 0x0a, 0x15, 0x52,
	0x45, 0x53, 0x41, 0x4e, 0x49, 0x54, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x45,
	0x45, 0x44, 0x45, 0x44, 0x10, 0x22, 0x12, 0x2c, 0x0a, 0x28, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41,
	0x4d, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x45, 0x4d, 0x50,
	0x4f, 0x52, 0x41, 0x52, 0x49, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54,
	0x45, 0x44, 0x10, 0x23, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x4e, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43,
	0x45, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x24,
	0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x43, 0x41, 0x43, 0x48,
	0x45, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10,
	0x25, 0x2a, 0x81, 0x0c, 0x0a, 0x14, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x45,
	0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49,
	0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10,
	0x02, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x4f, 0x4f, 0x5f,
	0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x53, 0x55, 0x46,
	0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x05, 0x12,
	0x18, 0x0a, 0x14, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x5f, 0x49, 0x44, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x49, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x5f, 0x53, 0x49,
	0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x08, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x4e,
	0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x4e, 0x42, 0x41, 0x4c, 0x41, 0x4e,
	0x43, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x0a, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x49, 0x44, 0x10, 0x0b, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x58,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4c,
	0x41, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x10, 0x0c, 0x12, 0x22,
	0x0a, 0x1e, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x0d, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x41, 0x44, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4c,
	0x41, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x0e, 0x12,
	0x1a, 0x0a, 0x16, 0x52, 0x45, 0x41, 0x44, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x1b, 0x0a, 0x17, 0x44,
	0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x10, 0x10, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x58, 0x45, 0x43,
	0x55, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x11, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x50, 0x4f, 0x43, 0x48, 0x5f,
	0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x12, 0x12, 0x1b, 0x0a, 0x17, 0x4e, 0x4f,
	0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x4b, 0x45, 0x59, 0x53, 0x10, 0x13, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x43, 0x48, 0x41,
	0x4e, 0x47, 0x45, 0x44, 0x10, 0x14, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x15, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x42, 0x4f,
	0x52, 0x52, 0x4f, 0x57, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x16, 0x12, 0x1e, 0x0a,
	0x1a, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x42, 0x4f, 0x52, 0x52, 0x4f, 0x57, 0x5f,
	0x4f, 0x55, 0x54, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x17, 0x12, 0x21, 0x0a,
	0x1d, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x10, 0x18,
	0x12, 0x0a, 0x0a, 0x06, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x19, 0x12, 0x11, 0x0a, 0x0d,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x1a, 0x12,
	0x1c, 0x0a, 0x18, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x1b, 0x12, 0x1d, 0x0a,
	0x19, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4c, 0x41, 0x4d, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x1c, 0x12, 0x26, 0x0a, 0x22,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x58, 0x45, 0x4d,
	0x50, 0x54, 0x10, 0x1d, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52,
	0x54, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x49, 0x44, 0x10, 0x1e,
	0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x50, 0x54, 0x48, 0x10, 0x1f,
	0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x20, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x45, 0x4e, 0x54, 0x52, 0x41,
	0x4e, 0x43, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10,
	0x21, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x41, 0x58, 0x5f, 0x53, 0x45, 0x45, 0x44, 0x5f, 0x4c, 0x45,
	0x4e, 0x47, 0x54, 0x48, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x22, 0x12,
	0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x45, 0x44, 0x53,
	0x10, 0x23, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45,
	0x41, 0x4c, 0x4c, 0x4f, 0x43, 0x10, 0x24, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x50, 0x55,
	0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x42, 0x55, 0x44, 0x47, 0x45, 0x54, 0x5f,
	0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x25, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52,
	0x49, 0x56, 0x49, 0x4c, 0x45, 0x47, 0x45, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x26, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f,
	0x45, 0x4e, 0x56, 0x49, 0x52, 0x4f, 0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x55,
	0x50, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x27, 0x12, 0x1e, 0x0a, 0x1a, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x28, 0x12, 0x1d, 0x0a, 0x19, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x49, 0x4c, 0x45, 0x10, 0x29, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4d,
	0x4d, 0x55, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x2a, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e, 0x43,
	0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x54, 0x59,
	0x10, 0x2b, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x4f, 0x52, 0x53, 0x48, 0x5f, 0x49, 0x4f, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x2c, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x58, 0x45, 0x4d, 0x50,
	0x54, 0x10, 0x2d, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52, 0x10, 0x2e, 0x12, 0x17,
	0x0a, 0x13, 0x41, 0x52, 0x49, 0x54, 0x48, 0x4d, 0x45, 0x54, 0x49, 0x43, 0x5f, 0x4f, 0x56, 0x45,
	0x52, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x2f, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x4e, 0x53, 0x55, 0x50,
	0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x59, 0x53, 0x56, 0x41, 0x52, 0x10, 0x30, 0x12,
	0x11, 0x0a, 0x0d, 0x49, 0x4c, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52,
	0x10, 0x31, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x32, 0x12, 0x19,
	0x0a, 0x15, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x45,
	0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x33, 0x12, 0x29, 0x0a, 0x25, 0x4d, 0x41, 0x58,
	0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x45, 0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44,
	0x45, 0x44, 0x10, 0x34, 0x12, 0x2f, 0x0a, 0x2b, 0x42, 0x55, 0x49, 0x4c, 0x54, 0x49, 0x4e, 0x5f,
	0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x53, 0x5f, 0x4d, 0x55, 0x53, 0x54, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x55, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x4e,
	0x49, 0x54, 0x53, 0x10, 0x35, 0x42, 0x56, 0x5a, 0x54, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x70, 0x63, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x79, 0x65, 0x6c, 0x6c,
	0x6f, 0x77, 0x73, 0x74, 0x6f, 0x6e, 0x65, 0x2d, 0x66, 0x61, 0x69, 0x74, 0x68, 0x66, 0x75, 0x6c,
	0x2f, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x79, 0x2f, 0x73, 0x6f, 0x6c,
	0x61, 0x6e, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_transaction_by_addr_proto_rawDescOnce sync.Once
	file_transaction_by_addr_proto_rawDescData = file_transaction_by_addr_proto_rawDesc
)

func file_transaction_by_addr_proto_rawDescGZIP() []byte {
	file_transaction_by_addr_proto_rawDescOnce.Do(func() {
		file_transaction_by_addr_proto_rawDescData = protoimpl.X.CompressGZIP(file_transaction_by_addr_proto_rawDescData)
	})
	return file_transaction_by_addr_proto_rawDescData
}

var file_transaction_by_addr_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_transaction_by_addr_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_transaction_by_addr_proto_goTypes = []any{
	(TransactionErrorType)(0),     // 0: solana.storage.TransactionByAddr.TransactionErrorType
	(InstructionErrorType)(0),     // 1: solana.storage.TransactionByAddr.InstructionErrorType
	(*TransactionByAddr)(nil),     // 2: solana.storage.TransactionByAddr.TransactionByAddr
	(*TransactionByAddrInfo)(nil), // 3: solana.storage.TransactionByAddr.TransactionByAddrInfo
	(*Memo)(nil),                  // 4: solana.storage.TransactionByAddr.Memo
	(*TransactionError)(nil),      // 5: solana.storage.TransactionByAddr.TransactionError
	(*InstructionError)(nil),      // 6: solana.storage.TransactionByAddr.InstructionError
	(*TransactionDetails)(nil),    // 7: solana.storage.TransactionByAddr.TransactionDetails
	(*UnixTimestamp)(nil),         // 8: solana.storage.TransactionByAddr.UnixTimestamp
	(*CustomError)(nil),           // 9: solana.storage.TransactionByAddr.CustomError
}
var file_transaction_by_addr_proto_depIdxs = []int32{
	3, // 0: solana.storage.TransactionByAddr.TransactionByAddr.tx_by_addrs:type_name -> solana.storage.TransactionByAddr.TransactionByAddrInfo
	5, // 1: solana.storage.TransactionByAddr.TransactionByAddrInfo.err:type_name -> solana.storage.TransactionByAddr.TransactionError
	4, // 2: solana.storage.TransactionByAddr.TransactionByAddrInfo.memo:type_name -> solana.storage.TransactionByAddr.Memo
	8, // 3: solana.storage.TransactionByAddr.TransactionByAddrInfo.block_time:type_name -> solana.storage.TransactionByAddr.UnixTimestamp
	0, // 4: solana.storage.TransactionByAddr.TransactionError.transaction_error:type_name -> solana.storage.TransactionByAddr.TransactionErrorType
	6, // 5: solana.storage.TransactionByAddr.TransactionError.instruction_error:type_name -> solana.storage.TransactionByAddr.InstructionError
	7, // 6: solana.storage.TransactionByAddr.TransactionError.transaction_details:type_name -> solana.storage.TransactionByAddr.TransactionDetails
	1, // 7: solana.storage.TransactionByAddr.InstructionError.error:type_name -> solana.storage.TransactionByAddr.InstructionErrorType
	9, // 8: solana.storage.TransactionByAddr.InstructionError.custom:type_name -> solana.storage.TransactionByAddr.CustomError
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_transaction_by_addr_proto_init() }
func file_transaction_by_addr_proto_init() {
	if File_transaction_by_addr_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_transaction_by_addr_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_transaction_by_addr_proto_goTypes,
		DependencyIndexes: file_transaction_by_addr_proto_depIdxs,
		EnumInfos:         file_transaction_by_addr_proto_enumTypes,
		MessageInfos:      file_transaction_by_addr_proto_msgTypes,
	}.Build()
	File_transaction_by_addr_proto = out.File
	file_transaction_by_addr_proto_rawDesc = nil
	file_transaction_by_addr_proto_goTypes = nil
	file_transaction_by_addr_proto_depIdxs = nil
}
