GEM
  remote: https://rubygems.org/
  specs:
    activesupport (7.0.6)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.4)
      public_suffix (>= 2.0.2, < 6.0)
    colorator (1.1.0)
    concurrent-ruby (1.2.2)
    em-websocket (0.5.3)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0)
    eventmachine (1.2.7)
    ffi (1.15.5)
    forwardable-extended (2.6.0)
    gemoji (3.0.1)
    html-pipeline (2.14.3)
      activesupport (>= 2)
      nokogiri (>= 1.4)
    http_parser.rb (0.8.0)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    jekyll (4.3.2)
      addressable (~> 2.4)
      colorator (~> 1.0)
      em-websocket (~> 0.5)
      i18n (~> 1.0)
      jekyll-sass-converter (>= 2.0, < 4.0)
      jekyll-watch (~> 2.0)
      kramdown (~> 2.3, >= 2.3.1)
      kramdown-parser-gfm (~> 1.0)
      liquid (~> 4.0)
      mercenary (>= 0.3.6, < 0.5)
      pathutil (~> 0.9)
      rouge (>= 3.0, < 5.0)
      safe_yaml (~> 1.0)
      terminal-table (>= 1.8, < 4.0)
      webrick (~> 1.7)
    jekyll-feed (0.15.1)
      jekyll (>= 3.7, < 5.0)
    jekyll-sass-converter (2.1.0)
      sassc (> 2.0.1, < 3.0)
    jekyll-seo-tag (2.8.0)
      jekyll (>= 3.8, < 5.0)
    jekyll-watch (2.2.1)
      listen (~> 3.0)
    jemoji (0.12.0)
      gemoji (~> 3.0)
      html-pipeline (~> 2.2)
      jekyll (>= 3.0, < 5.0)
    just-the-docs (0.5.4)
      jekyll (>= 3.8.5)
      jekyll-seo-tag (>= 2.0)
      rake (>= 12.3.1)
    kramdown (2.3.2)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    liquid (4.0.4)
    listen (3.8.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    mercenary (0.4.0)
    minima (2.5.1)
      jekyll (>= 3.5, < 5.0)
      jekyll-feed (~> 0.9)
      jekyll-seo-tag (~> 2.1)
    minitest (5.18.1)
    nokogiri (1.15.3-x86_64-linux)
      racc (~> 1.4)
    pathutil (0.16.2)
      forwardable-extended (~> 2.6)
    public_suffix (4.0.7)
    racc (1.7.1)
    rake (13.0.6)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rexml (3.2.5)
    rouge (3.26.0)
    safe_yaml (1.0.5)
    sassc (2.4.0)
      ffi (~> 1.9)
    terminal-table (2.0.0)
      unicode-display_width (~> 1.1, >= 1.1.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (1.8.0)
    webrick (1.8.1)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  http_parser.rb (~> 0.6.0)
  jekyll (~> 4.3.2)
  jekyll-feed (~> 0.12)
  jemoji
  just-the-docs
  minima (~> 2.5)
  tzinfo (>= 1, < 3)
  tzinfo-data
  wdm (~> 0.1.1)

BUNDLED WITH
   2.2.16
