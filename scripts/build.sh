#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to print colored messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a Docker image exists in the registry
check_image_exists() {
    local image_tag="$1"
    if docker pull "${image_tag}" >/dev/null 2>&1; then
        return 0  # Image exists
    else
        return 1  # Image doesn't exist
    fi
}

# Parse command line arguments
FORCE_YELLOWSTONE=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --force-yellowstone)
            FORCE_YELLOWSTONE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [--force-yellowstone]"
            echo ""
            echo "Options:"
            echo "  --force-yellowstone   Force rebuild yellowstone-builder even if hash exists"
            echo "  --help                Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if required environment variables are set
if [ -z "$PROJECT_ID" ] || [ -z "$REPOSITORY" ]; then
    log_error "PROJECT_ID and REPOSITORY environment variables must be set"
    exit 1
fi

# Initialize and update submodules
log_info "Initializing and updating submodules..."
git submodule update --init --recursive

# Get yellowstone-faithful submodule hash
if [ ! -d "yellowstone-faithful" ]; then
    log_error "yellowstone-faithful submodule directory not found"
    exit 1
fi

YELLOWSTONE_HASH=$(cd yellowstone-faithful && git rev-parse HEAD)
if [ $? -ne 0 ]; then
    log_error "Failed to get yellowstone-faithful submodule hash"
    exit 1
fi
log_info "Yellowstone-faithful submodule hash: ${YELLOWSTONE_HASH}"

# Check if yellowstone-builder image with this hash already exists
YELLOWSTONE_IMAGE_TAG="asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/yellowstone-builder:${YELLOWSTONE_HASH}"
log_info "Checking if yellowstone-builder image exists: ${YELLOWSTONE_IMAGE_TAG}"

# Check if we should force rebuild
if [ "$FORCE_YELLOWSTONE" = true ]; then
    log_warning "Force rebuild requested for yellowstone-builder"
    BUILD_YELLOWSTONE=true
else
    # Try to pull the image to check if it exists
    if check_image_exists "${YELLOWSTONE_IMAGE_TAG}"; then
        log_info "Yellowstone-builder image with hash ${YELLOWSTONE_HASH} already exists"
        BUILD_YELLOWSTONE=false
    else
        log_info "Yellowstone-builder image with hash ${YELLOWSTONE_HASH} not found, will build"
        BUILD_YELLOWSTONE=true
    fi
fi

# Build and push yellowstone-builder (only if submodule hash changed)
if [ "$BUILD_YELLOWSTONE" = true ]; then
    log_info "Building and pushing yellowstone-builder image for hash: ${YELLOWSTONE_HASH}..."
    docker buildx build \
        . \
        --file docker/yellowstone-builder.Dockerfile \
        --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/yellowstone-builder:latest" \
        --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/yellowstone-builder:${YELLOWSTONE_HASH}" \
        --push \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --platform linux/amd64 \
        --provenance=false \
        --sbom=false
else
    log_info "Skipping yellowstone-builder build (image with hash ${YELLOWSTONE_HASH} already exists)"
fi

# Build and push rust-builder (stream-processor components)
log_info "Building and pushing rust-builder image..."
docker buildx build \
    . \
    --file docker/build.Dockerfile \
    --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/rust-builder:latest" \
    --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/rust-builder:${GITHUB_SHA}" \
    --push \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --platform linux/amd64 \
    --provenance=false \
    --sbom=false

# Build and push stream-processor (reusing rust-builder from registry)
log_info "Building and pushing stream-processor image..."
docker buildx build \
    ./stream-processor \
    --file docker/stream-processor/Dockerfile \
    --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/stream-processor:latest" \
    --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/stream-processor:${GITHUB_SHA}" \
    --push \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --build-arg PROJECT_ID="${PROJECT_ID}" \
    --build-arg REPOSITORY="${REPOSITORY}" \
    --platform linux/amd64 \
    --provenance=false \
    --sbom=false

# Build and push backfiller (reusing both builders from registry)
log_info "Building and pushing backfiller image..."
docker buildx build \
    . \
    --file docker/backfiller/Dockerfile \
    --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/backfiller:latest" \
    --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/backfiller:${GITHUB_SHA}" \
    --push \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --build-arg PROJECT_ID="${PROJECT_ID}" \
    --build-arg REPOSITORY="${REPOSITORY}" \
    --platform linux/amd64 \
    --provenance=false \
    --sbom=false

# Build and push data-writer
log_info "Building and pushing data-writer image..."
docker buildx build \
    ./data-writer \
    --file docker/data-writer/Dockerfile \
    --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/data-writer:latest" \
    --tag "asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/data-writer:${GITHUB_SHA}" \
    --push \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --platform linux/amd64 \
    --provenance=false \
    --sbom=false

log_success "All images built and pushed successfully!"

