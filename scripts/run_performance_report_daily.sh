#!/bin/bash

# Detect OS
if date --version >/dev/null 2>&1; then
    # GNU date (Linux)
    get_today_noon() {
        TZ=Asia/Singapore date -d "today 12:00:00" +%s
    }
    get_tomorrow_noon() {
        TZ=Asia/Singapore date -d "tomorrow 12:00:00" +%s
    }
else
    # BSD date (macOS)
    get_today_noon() {
        TZ=Asia/Singapore date -j -f "%Y-%m-%d %H:%M:%S" "$(TZ=Asia/Singapore date +%Y-%m-%d) 12:00:00" +%s
    }
    get_tomorrow_noon() {
        TZ=Asia/Singapore date -j -f "%Y-%m-%d %H:%M:%S" "$(TZ=Asia/Singapore date -v+1d +%Y-%m-%d) 12:00:00" +%s
    }
fi

while true; do
    # Get current time in UTC+8
    now=$(TZ=Asia/Singapore date +%s)
    # Get today's 12:00 UTC+8 in epoch
    today_noon=$(get_today_noon)

    if [ "$now" -ge "$today_noon" ]; then
        # If past noon, schedule for tomorrow
        next_noon=$(get_tomorrow_noon)
    else
        # If before noon, schedule for today
        next_noon=$today_noon
    fi

    sleep_seconds=$((next_noon - now))
    echo "Sleeping for $sleep_seconds seconds until next 12:00 UTC+8..."
    sleep $sleep_seconds

    # Run the report (adjust python version if needed)
    echo "Running performance report at $(TZ=Asia/Singapore date)..."
    python3 py/generate_performance_report.py --days 1 --chat_id -1001167958195

    # Sleep for 24 hours until next run
    echo "Sleeping for 24 hours until next run..."
    sleep 86100
done