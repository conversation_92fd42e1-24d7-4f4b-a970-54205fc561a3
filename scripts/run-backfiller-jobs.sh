#!/bin/bash

set -e  # Exit on any error

# Function to check if a job has failed
check_job_failed() {
    local job_name=$1
    local status=$(kubectl get job "$job_name" -n solana-data -o jsonpath='{.status.conditions[?(@.type=="Failed")].status}' 2>/dev/null || echo "False")
    if [ "$status" == "True" ]; then
        return 0  # Job failed
    else
        return 1  # Job not failed
    fi
}

# Function to check if a job has succeeded
check_job_succeeded() {
    local job_name=$1
    local status=$(kubectl get job "$job_name" -n solana-data -o jsonpath='{.status.conditions[?(@.type=="Complete")].status}' 2>/dev/null || echo "False")
    if [ "$status" == "True" ]; then
        return 0  # Job succeeded
    else
        return 1  # Job not succeeded
    fi
}

# Function to count running jobs
count_running_jobs() {
    kubectl get jobs -n solana-data -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.status.active}{"\n"}{end}' 2>/dev/null | \
    grep -E '^backfiller-epoch-' | \
    awk '$2 > 0' | \
    wc -l || echo "0"
}

# Main execution
for epoch in $(seq 801 -1 700); do
    echo "Processing epoch $epoch"
    
    # Generate the job YAML
    if ! ./scripts/generate-backfiller-jobs.sh "$epoch"; then
        echo "Failed to generate job for epoch $epoch"
        exit 1
    fi
    
    # Wait until we have less than 10 running jobs
    while [ $(count_running_jobs) -ge 10 ]; do
        echo "Waiting for running jobs to complete... Current running jobs: $(count_running_jobs)"
        sleep 30
    done
    
    # Apply the job
    job_name="backfiller-epoch-${epoch}"
    if ! kubectl apply -f "jobs/${job_name}.yaml"; then
        echo "Failed to apply job for epoch $epoch"
        exit 1
    fi
done

echo "All epochs processed successfully!"
