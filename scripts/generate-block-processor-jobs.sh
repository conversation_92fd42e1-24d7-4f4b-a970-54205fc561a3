#!/bin/bash

# Check if required arguments are provided
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <from_block> <to_block> <num_jobs>"
    echo "Example: $0 347328000 347570000 10"
    exit 1
fi

# Get arguments
FROM_BLOCK=$1
TO_BLOCK=$2
NUM_JOBS=$3

TOTAL_BLOCKS=$((TO_BLOCK - FROM_BLOCK))

# Calculate block range per job
BLOCKS_PER_JOB=$((TOTAL_BLOCKS / NUM_JOBS))

# Create jobs directory if it doesn't exist
mkdir -p jobs

# Generate jobs
for i in $(seq 0 $((NUM_JOBS-1))); do
    JOB_FROM=$((FROM_BLOCK + (i * BLOCKS_PER_JOB)))
    JOB_TO=$((JOB_FROM + BLOCKS_PER_JOB - 1))
    
    # For the last job, ensure we cover up to TO_BLOCK
    if [ $i -eq $((NUM_JOBS-1)) ]; then
        JOB_TO=$TO_BLOCK
    fi

    cat > "jobs/block-processor-${JOB_FROM}-${JOB_TO}.yaml" << EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: block-processor-${JOB_FROM}-${JOB_TO}
  namespace: solana-data
spec:
  template:
    metadata:
      annotations:
        "cluster-autoscaler.kubernetes.io/safe-to-evict": "false"
    spec:
      serviceAccountName: default-ksa
      containers:
        - name: block-processor
          image: asia-northeast1-docker.pkg.dev/kryptogo-wallet-data/kg-solana-data/stream-processor:latest
          workingDir: /app
          command: ["/app/block-processor"]
          args:
            - "--from-block"
            - "${JOB_FROM}"
            - "--to-block"
            - "${JOB_TO}"
            - "--threads"
            - "10"
          resources:
            requests:
              cpu: "0.3"
              memory: "1000Mi"
            limits:
              cpu: "0.5"
              memory: "1500Mi"
          env:
            - name: RPC_URL
              value: "https://wiser-smart-firefly.solana-mainnet.quiknode.pro/0be5fa44c1f086febd940bee2f9296b79a960c25"
            - name: RUST_LOG
              value: "info"
      restartPolicy: Never
  backoffLimit: 0
EOF

    echo "Created job for blocks ${JOB_FROM} to ${JOB_TO}"
done

echo "Generated ${NUM_JOBS} block processor jobs"
