#!/bin/bash

# Check if epoch number is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <epoch_number>"
    exit 1
fi

EPOCH_NUMBER=$1

# Create jobs directory if it doesn't exist
mkdir -p jobs

cat > "jobs/backfiller-epoch-${EPOCH_NUMBER}.yaml" << EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: backfiller-epoch-${EPOCH_NUMBER}
  namespace: solana-data
spec:
  template:
    metadata:
      annotations:
        "cluster-autoscaler.kubernetes.io/safe-to-evict": "false"
        "gke-gcsfuse/volumes": "true"
    spec:
      serviceAccountName: default-ksa
      containers:
        - name: backfiller
          image: asia-northeast1-docker.pkg.dev/kryptogo-wallet-data/kg-solana-data/backfiller:latest
          volumeMounts:
            - name: gcs-fuse-csi-ephemeral
              mountPath: /data
          env:
            - name: CAR_FILE_PATH
              value: "/data/epoch-${EPOCH_NUMBER}.car"
            - name: RUNNER_PATH
              value: "/app/bin/geyser-plugin-runner"
            - name: CONFIG_PATH
              value: "/app/config/yellowstone_config.json"
            - name: EPOCH
              value: "${EPOCH_NUMBER}"
            - name: DB_HOST
              value: "**********"
            - name: DB_PORT
              value: "5432"
            - name: DB_USER
              value: "solana_data"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: password
            - name: DB_NAME
              value: "solana_data"
            - name: DB_SSL_MODE
              value: "require"
          resources:
            requests:
              cpu: "1"
              memory: "300Mi"
            limits:
              cpu: "2"
              memory: "600Mi"
      volumes:
        - name: gcs-fuse-csi-ephemeral
          csi:
            driver: gcsfuse.csi.storage.gke.io
            volumeAttributes:
              bucketName: kg-old-faithful
      restartPolicy: Never
  backoffLimit: 0
EOF

echo "Created job for epoch ${EPOCH_NUMBER}"
