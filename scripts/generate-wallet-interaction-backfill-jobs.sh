#!/bin/bash

# Check if required arguments are provided
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <from_slot> <to_slot> <num_jobs>"
    echo "Example: $0 348600000 348720000 2"
    exit 1
fi

# Get arguments
FROM_SLOT=$1
TO_SLOT=$2
NUM_JOBS=$3

TOTAL_SLOTS=$((TO_SLOT - FROM_SLOT))

# Calculate slot range per job
SLOTS_PER_JOB=$((TOTAL_SLOTS / NUM_JOBS))

# Create jobs directory if it doesn't exist
mkdir -p jobs

# Generate jobs
for i in $(seq 0 $((NUM_JOBS-1))); do
    JOB_FROM=$((FROM_SLOT + (i * SLOTS_PER_JOB)))
    JOB_TO=$((JOB_FROM + SLOTS_PER_JOB - 1))
    
    # For the last job, ensure we cover up to TO_SLOT
    if [ $i -eq $((NUM_JOBS-1)) ]; then
        JOB_TO=$TO_SLOT
    fi

    cat > "jobs/interaction-backfill-${JOB_FROM}-${JOB_TO}.yaml" << EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: interaction-backfill-${JOB_FROM}-${JOB_TO}
  namespace: solana-data
spec:
  template:
    metadata:
      annotations:
        "cluster-autoscaler.kubernetes.io/safe-to-evict": "false"
    spec:
      serviceAccountName: default-ksa
      containers:
        - name: wallet-interaction-backfill
          image: asia-northeast1-docker.pkg.dev/kryptogo-wallet-data/kg-solana-data/data-writer:latest
          command: ["./wallet-interaction-map-backfill"]
          env:
            - name: FROM_SLOT
              value: "${JOB_FROM}"
            - name: TO_SLOT
              value: "${JOB_TO}"
            - name: DB_HOST
              value: "**********"
            - name: DB_PORT
              value: "5432"
            - name: DB_USER
              value: "solana_data"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: password
            - name: DB_NAME
              value: "solana_data"
            - name: DB_SSL_MODE
              value: "require"
          resources:
            requests:
              cpu: "0.3"
              memory: "200Mi"
            limits:
              cpu: "0.5"
              memory: "400Mi"
      restartPolicy: Never
  backoffLimit: 0
EOF

    echo "Created job for wallet interaction backfill: slots ${JOB_FROM} to ${JOB_TO}"
done

echo "Generated ${NUM_JOBS} wallet interaction backfill jobs" 