#!/bin/bash

# Check if required arguments are provided
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <to_slot>"
    echo "Example: $0 *********"
    exit 1
fi

# Get arguments
TO_SLOT=$1

# Create jobs directory if it doesn't exist
mkdir -p jobs

cat > "jobs/balance-history-backfill-${TO_SLOT}.yaml" << EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: balance-history-backfill-${TO_SLOT}
  namespace: solana-data
spec:
  template:
    metadata:
      annotations:
        "cluster-autoscaler.kubernetes.io/safe-to-evict": "false"
    spec:
      serviceAccountName: default-ksa
      containers:
        - name: balance-history-backfill
          image: asia-northeast1-docker.pkg.dev/kryptogo-wallet-data/kg-solana-data/data-writer:latest
          imagePullPolicy: Always
          workingDir: /app
          command: ["/app/balance-history-backfill"]
          env:
            - name: TARGET_END_SLOT
              value: "${TO_SLOT}"
            - name: DB_HOST
              value: "**********"
            - name: DB_PORT
              value: "5432"
            - name: DB_USER
              value: "solana_data"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: password
            - name: DB_NAME
              value: "solana_data"
            - name: DB_SSL_MODE
              value: "require"
          resources:
            requests:
              cpu: "0.3"
              memory: "300Mi"
            limits:
              cpu: "0.5"
              memory: "500Mi"
      restartPolicy: Never
  backoffLimit: 0
EOF

echo "Created job for balance history backfill: target end slot ${TO_SLOT}"