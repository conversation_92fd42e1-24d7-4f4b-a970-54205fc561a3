# Solana Data Processor

This repo handles the infra for streaming, processing and storage of Solana data

## Prerequisites

Rust latest stable

## Setup

1. Install dependencies

    ```bash
    brew install protobuf
    ```

## Features

### Multi-Period Balance History

The system maintains token balance history across multiple time granularities (1m, 5m, 15m, 1h, 4h, 1d) with efficient GIST indexes for range queries.

**Key Features:**

- 6 separate tables optimized for different time periods
- Parallel processing of all periods
- Automatic zero-range cleanup
- Efficient GIST indexes for range queries
- Chunked processing for large datasets

**Commands:**

```bash
# Backfill historical data
TARGET_END_SLOT=432000000 ./balance-history-backfill

# Process real-time updates
./balance-history-delta

# Query balance history
./balance-history-query -wallet <address> -token <mint> -from <time> -to <time> -period 5m
```

For detailed documentation, see [Multi-Period Balance History](docs/multi-period-balance-history.md).
