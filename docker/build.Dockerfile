# Build stage for Rust dependencies (stream-processor only)
FROM rust:1.87.0-slim-bullseye AS rust-builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libclang-dev \
    clang \
    llvm-dev \
    gcc \
    git \
    protobuf-compiler \
    make \
    perl \
    libudev-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /build

# Copy stream-processor workspace files
COPY stream-processor/ ./stream-processor/

# Build the binaries directly
RUN cd stream-processor && cargo build --release --bin stream-processor --bin block-processor && cd ..

# Build the kryptogo plugin
RUN cd stream-processor/kryptogo-oldfaithful-plugin && cargo build --release
