# Use the stream-processor builder from registry
ARG BUILDPLATFORM=linux/amd64
ARG PROJECT_ID
ARG REPOSITORY
FROM --platform=${BUILDPLATFORM} asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/rust-builder:latest AS builder

# Runtime stage
FROM debian:bullseye-slim

WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl1.1 \
    && rm -rf /var/lib/apt/lists/*

# Copy the binary from builder
COPY --from=builder /build/stream-processor/target/release/stream-processor /app/stream-processor
COPY --from=builder /build/stream-processor/target/release/block-processor /app/block-processor

# Run as non-root user
RUN useradd -m -u 1000 appuser
USER appuser

# Expose health check port
EXPOSE 8080

# Run the application
CMD ["/app/stream-processor"]
