# Yellowstone-faithful builder stage
FROM rust:1.87.0-slim-bullseye AS yellowstone-builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libclang-dev \
    clang \
    llvm-dev \
    gcc \
    git \
    protobuf-compiler \
    make \
    perl \
    libudev-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /build

# Copy the entire yellowstone-faithful directory
COPY yellowstone-faithful/ ./yellowstone-faithful/

# Change the working directory to the workspace root
WORKDIR /build/yellowstone-faithful

# Build the binary directly
RUN cd geyser-plugin-runner && cargo build --release