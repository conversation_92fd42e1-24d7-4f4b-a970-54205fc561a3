# Use the yellowstone-builder from registry
ARG BUILDPLATFORM=linux/amd64
ARG PROJECT_ID
ARG REPOSITORY
FROM --platform=${BUILDPLATFORM} asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/yellowstone-builder:latest AS yellowstone-builder

# Use the stream-processor builder from registry
FROM --platform=${BUILDPLATFORM} asia-northeast1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/rust-builder:latest AS rust-builder

# 執行階段
FROM debian:bullseye-slim

# 安裝執行時間依賴
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    fuse \
    procps \
    jq \
    && rm -rf /var/lib/apt/lists/*

# 建立應用程式目錄
RUN mkdir -p /app/bin /app/config /app/plugins /mnt/gcs

# 從建置階段複製二進位檔案 (using pre-built binaries from registry)
COPY --from=yellowstone-builder /build/yellowstone-faithful/target/release/demo-rust-ipld-car /app/bin/geyser-plugin-runner
COPY --from=rust-builder /build/stream-processor/target/release/libkryptogo_oldfaithful_plugin.so /app/plugins/

# 複製配置檔案
COPY backfiller/config/ /app/config/
COPY docker/backfiller/entrypoint.sh /app/entrypoint.sh

# 設定權限
RUN chmod +x /app/entrypoint.sh

# 設定環境變數
ENV RUST_LOG=info
ENV CONFIG_PATH=/app/config/yellowstone_config.json
ENV RUNNER_PATH=/app/bin/geyser-plugin-runner

# 工作目錄
WORKDIR /app

# 入口點
ENTRYPOINT ["/app/entrypoint.sh"]
