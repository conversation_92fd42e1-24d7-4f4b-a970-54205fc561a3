#!/bin/bash

set -e

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查必要的環境變數
check_env_vars() {
    log_info "檢查環境變數..."
    
    if [ -z "$RUNNER_PATH" ]; then
        log_error "RUNNER_PATH 環境變數未設定"
        exit 1
    fi

    if [ -z "$CAR_FILE_PATH" ]; then
        log_error "CAR_FILE_PATH 環境變數未設定"
        exit 1
    fi

    if [ -z "$CONFIG_PATH" ]; then
        log_error "CONFIG_PATH 環境變數未設定"
        exit 1
    fi

    if [ -z "$EPOCH" ]; then
        log_error "EPOCH 環境變數未設定"
        exit 1
    fi

    # Check database environment variables
    if [ -z "$DB_HOST" ]; then
        log_error "DB_HOST 環境變數未設定"
        exit 1
    fi

    if [ -z "$DB_PORT" ]; then
        log_error "DB_PORT 環境變數未設定"
        exit 1
    fi

    if [ -z "$DB_USER" ]; then
        log_error "DB_USER 環境變數未設定"
        exit 1
    fi

    if [ -z "$DB_PASSWORD" ]; then
        log_error "DB_PASSWORD 環境變數未設定"
        exit 1
    fi

    if [ -z "$DB_NAME" ]; then
        log_error "DB_NAME 環境變數未設定"
        exit 1
    fi

    if [ -z "$DB_SSL_MODE" ]; then
        log_warning "DB_SSL_MODE 環境變數未設定，使用預設值 'disable'"
        export DB_SSL_MODE="disable"
    fi
    
    log_success "環境變數檢查完成"
}

# 更新配置檔案
update_config() {
    log_info "更新配置檔案..."
    
    # Update epoch in config file
    jq ".epoch = $EPOCH" "$CONFIG_PATH" > "$CONFIG_PATH.tmp" && mv "$CONFIG_PATH.tmp" "$CONFIG_PATH"
    
    # Add database configuration
    jq ".db = {
        host: \"$DB_HOST\",
        port: $DB_PORT,
        user: \"$DB_USER\",
        password: \"$DB_PASSWORD\",
        database: \"$DB_NAME\",
        sslmode: \"$DB_SSL_MODE\"
    }" "$CONFIG_PATH" > "$CONFIG_PATH.tmp" && mv "$CONFIG_PATH.tmp" "$CONFIG_PATH"
    
    log_success "配置檔案更新完成"
}

# 執行 geyser-plugin-runner
run_processor() {
    log_info "開始執行 geyser-plugin-runner"
    RUNNER_CMD="$RUNNER_PATH $CAR_FILE_PATH $CONFIG_PATH"
    exec $RUNNER_CMD
}

# 主函數
main() {
    log_info "Solana Backfiller 容器啟動..."
    
    # 顯示系統資訊
    log_info "容器資訊:"
    log_info "  主機名: $(hostname)"
    log_info "  CPU: $(nproc) 核心"
    log_info "  記憶體: $(free -h | awk '/^Mem:/ {print $2}')"
    log_info "  磁碟空間: $(df -h / | awk 'NR==2 {print $4}')"
    
    # 檢查環境變數
    check_env_vars
    
    # 更新配置檔案
    update_config
    
    # 執行處理器
    run_processor
}

# 執行主函數
main "$@" 