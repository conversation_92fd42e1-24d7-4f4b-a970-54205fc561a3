# Rust build artifacts
**/target/

# Git and version control
.git/
.gitignore
.gitmodules
.github/

# Documentation
*.md
docs/

# Kubernetes and deployment
kubernetes/
terraform/

# Scripts and tools
scripts/
parser-generator/

# Node.js artifacts
node_modules/
build/
dist/

# Logs and temporary files
*.log
log.txt
*.tmp

# Large data files
*.tar
*.tar.gz
*.tar.bz2
*.zip

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Test artifacts
**/tests/fixtures/
**/tests/data/

# Build artifacts
**/build/
**/dist/
**/out/

# Cache directories
.cache/
.cargo/
