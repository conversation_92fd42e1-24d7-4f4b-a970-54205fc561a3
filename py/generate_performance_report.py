from telethon import TelegramClient
import re
import asyncio
import os
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import pandas as pd
from dotenv import load_dotenv
import json
import requests
import time
import argparse
import telegram

# Load environment variables
load_dotenv()

# Telegram API credentials from environment variables
api_id = os.getenv('TELEGRAM_API_ID')
api_hash = os.getenv('TELEGRAM_API_HASH')
phone_number = os.getenv('TELEGRAM_PHONE_NUMBER')
pump_hound_channel = "Pump Hound Signal"

# Validate required environment variables
if not all([api_id, api_hash, phone_number]):
    raise ValueError("Missing required environment variables. Please set TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE_NUMBER in .env file")

# Regex patterns to extract data
patterns = {
    'buy_signal': re.compile(r'🟢 Buy \*\*(.+?)\*\*(?:\n|$)'),
    'token_address': re.compile(r'📝 CA: `([0-9a-zA-Z]{42,})`'),
    'price': re.compile(r'Price: \$([0-9.]+)')
}

# API endpoint for sell signals
SELL_SIGNAL_API = "https://wallet-dev.kryptogo.app/v1/token_signal/sell"
BUY_SIGNAL_API = "https://wallet-dev.kryptogo.app/v1/token_signal/buy"

async def get_token_info_from_telegram(token_addresses):
    """Get token symbols and entry prices from Telegram buy messages for the given token addresses"""
    # Connect to Telegram
    client = TelegramClient('pump_hound_session', api_id, api_hash)
    await client.start(phone_number)
    
    print(f"Connected to Telegram. Fetching token info for {len(token_addresses)} tokens...")
    
    # Find the Pump Hound Signal channel
    pump_hound_channel_entity = None
    async for dialog in client.iter_dialogs():
        if pump_hound_channel.lower() in dialog.name.lower():
            pump_hound_channel_entity = dialog
            break
    
    if not pump_hound_channel_entity:
        print(f"Could not find {pump_hound_channel} channel")
        await client.disconnect()
        return {}

    # Dictionary to store token info
    token_info_map = {}
    token_addresses_set = set(token_addresses)
    
    # Get messages from the channel (more than enough to cover recent signals)
    messages = await client.get_messages(
        pump_hound_channel_entity,
        limit=2000  # Adjust as needed
    )
    
    print(f"Fetched {len(messages)} messages from Telegram to find token info")
    
    # Find buy signals for the token addresses we're interested in
    for message in messages:
        if not message.text:
            continue
            
        token_address_match = patterns['token_address'].search(message.text)
        if not token_address_match:
            continue
            
        token_address = token_address_match.group(1)
        
        # Only process if this is a token address we're looking for and we haven't found its info yet
        if token_address in token_addresses_set and token_address not in token_info_map:
            buy_signal_match = patterns['buy_signal'].search(message.text)
            if buy_signal_match:
                token_symbol = buy_signal_match.group(1).strip()
                price_match = patterns['price'].search(message.text)
                if price_match:
                    try:
                        entry_price = float(price_match.group(1))
                    except ValueError:
                        entry_price = None
                else:
                    entry_price = None
                token_info_map[token_address] = {
                    'symbol': token_symbol,
                    'entry_price': entry_price
                }
    
    await client.disconnect()
    
    # For tokens where we couldn't find the info, use placeholders
    for token_address in token_addresses:
        if token_address not in token_info_map:
            token_info_map[token_address] = {
                'symbol': "Unknown",
                'entry_price': None
            }
    
    return token_info_map

def fetch_sell_signals():
    """Fetch sell signals from the API"""
    try:
        response = requests.get(SELL_SIGNAL_API)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error fetching data from API: {e}")
        return {"code": -1, "data": []}

def fetch_buy_signals():
    """Fetch buy signals from the API"""
    try:
        response = requests.get(BUY_SIGNAL_API)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error fetching buy signals from API: {e}")
        return {"code": -1, "data": []}

def is_in_time_range(timestamp, yesterday_noon_timestamp, today_noon_timestamp):
    """Check if the timestamp is within the specified time range"""
    return yesterday_noon_timestamp <= timestamp <= today_noon_timestamp

def format_percentage(percentage):
    """Format percentage with proper sign and decimal places"""
    if percentage >= 0:
        return f"+{percentage:.0f}%"
    else:
        return f"{percentage:.0f}%"

def get_emoji_for_rank(rank):
    """Get emoji for ranking position"""
    emojis = {
        1: "🥇",
        2: "🥈", 
        3: "🥉",
        4: "4️⃣",
        5: "5️⃣",
        6: "6️⃣",
        7: "7️⃣",
        8: "8️⃣",
        9: "9️⃣",
        10: "🔟"
    }
    return emojis.get(rank, f"{rank}️⃣")

def get_emoji_for_performance(percentage):
    """Get emoji based on performance percentage"""
    if percentage >= 1000:
        return "🚀"
    elif percentage >= 500:
        return "🔥"
    elif percentage >= 200:
        return "💎"
    elif percentage >= 100:
        return "📈"
    elif percentage >= 50:
        return "✨"
    else:
        return "💰"

def format_market_cap(price):
    """Format market cap in a readable format (e.g., 2.1M, 45K)"""
    if price is None:
        return "Unknown"
    
    # Calculate market cap (price * 1e9)
    mcap = price * 1e9
    
    if mcap >= 1e9:
        return f"{mcap/1e9:.1f}B"
    elif mcap >= 1e6:
        return f"{mcap/1e6:.1f}M"
    elif mcap >= 1e3:
        return f"{mcap/1e3:.0f}K"
    else:
        return f"{mcap:.0f}"

def generate_performance_message(top_tokens, all_tokens=None):
    """Generate the formatted performance report message"""
    if not top_tokens:
        return "No data available for the specified time range."
    
    # Calculate average gain (top 10)
    total_gain = sum(token['highest_gain'] for token in top_tokens)
    avg_gain = total_gain / len(top_tokens)
    
    # Use all tokens for doubled count/percentage if provided
    if all_tokens is None:
        all_tokens = top_tokens
    doubled_count = sum(1 for token in all_tokens if token['highest_gain'] >= 100)
    doubled_percentage = (doubled_count / len(all_tokens)) * 100 if all_tokens else 0
    
    # Start building the message
    message = "🏆 *金狗訊號戰績日報* 🏆\n\n"
    message += f"📊 Top 10 平均漲幅: *{format_percentage(avg_gain)}*\n"
    message += f"🎯 翻倍訊號比例: *{doubled_percentage:.0f}%* ({doubled_count}/{len(all_tokens)})\n\n"
    
    # Add each token
    for i, token in enumerate(top_tokens, 1):
        rank_emoji = get_emoji_for_rank(i)
        performance_emoji = get_emoji_for_performance(token['highest_gain'])
        formatted_gain = format_percentage(token['highest_gain'])
        token_url = f"https://www.kryptogo.xyz/token/sol/{token['token_address']}/top"
        
        # Format market cap range
        entry_mcap = format_market_cap(token.get('entry_price'))
        if token.get('entry_price') is not None and token.get('highest_gain') is not None:
            # Calculate top market cap: entry_price * (1 + max_ratio)
            top_price = token['entry_price'] * (1 + token['highest_gain'] / 100)
            top_mcap = format_market_cap(top_price)
            mcap_range = f"{entry_mcap} → {top_mcap}"
        else:
            mcap_range = "Unknown → Unknown"
        
        # Format the token line
        if i <= 3:
            # Top 3 get special formatting
            message += f"{rank_emoji} [{token['token_symbol']}]({token_url}) | {mcap_range} | *{formatted_gain}* {performance_emoji}\n"
        else:
            # Others get regular formatting
            message += f"{rank_emoji}  [{token['token_symbol']}]({token_url})  | {mcap_range} |  *{formatted_gain}* {performance_emoji}\n"
    
    # Add footer
    message += "\n不想錯過下一個 Alpha？\n"
    message += "👉 訊號頻道: @pump\\_hound\\_signal\n"
    message += "👉 中文社群: @crypto\\_godfather\\_zh"
    
    return message

async def analyze_signals_and_generate_report(days=2):
    """Analyze signals from the API and generate performance report"""
    # Calculate date range (yesterday 12pm to today 12pm in UTC+8)
    utc_plus_8 = timezone(timedelta(hours=8))
    now = datetime.now(utc_plus_8)
    today_noon = now.replace(hour=12, minute=0, second=0, microsecond=0)
    yesterday_noon = today_noon - timedelta(days=days)
    
    # Convert to Unix timestamps for comparison with API data
    today_noon_timestamp = int(today_noon.timestamp())
    yesterday_noon_timestamp = int(yesterday_noon.timestamp())
    
    print(f"Fetching signals from {yesterday_noon} to {today_noon} (UTC+8)...")
    
    # Fetch both buy and sell signals from the API
    sell_api_response = fetch_sell_signals()
    buy_api_response = fetch_buy_signals()
    
    if (sell_api_response["code"] != 0 and buy_api_response["code"] != 0) or (not sell_api_response["data"] and not buy_api_response["data"]):
        print("No valid data received from the APIs")
        return "No valid data received from the APIs"
    
    # Combine signals from both APIs
    signals = []
    
    # Process sell signals
    for signal in sell_api_response.get("data", []):
        if signal.get("buy_entry_time") is None:
            continue
        if is_in_time_range(signal["buy_entry_time"], yesterday_noon_timestamp, today_noon_timestamp):
            signals.append({
                "token_address": signal["token_address"],
                "highest_gain": signal["highest_gain"],
                "buy_entry_time": signal["buy_entry_time"],
                "type": "sell"
            })
    
    # Process buy signals
    for signal in buy_api_response.get("data", []):
        if signal.get("emit_time") is None:
            continue
        if is_in_time_range(signal["emit_time"], yesterday_noon_timestamp, today_noon_timestamp):
            # Calculate gain using highest_price/buy_entry_price-1
            gain = (signal["highest_price"] / signal["buy_entry_price"]) - 1
            signals.append({
                "token_address": signal["token_address"],
                "highest_gain": gain,
                "buy_entry_time": signal["emit_time"],
                "type": "buy"
            })
    
    if not signals:
        print("No signals found in the specified time range")
        return "No signals found in the specified time range"
    
    print(f"Found {len(signals)} signals within the specified time range")
    
    # Extract token addresses to fetch their symbols and prices
    token_addresses = [signal["token_address"] for signal in signals]
    
    # Get token info from Telegram
    token_info_map = await get_token_info_from_telegram(token_addresses)
    
    # Prepare data for analysis
    analyzed_tokens = []
    
    for signal in signals:
        token_address = signal["token_address"]
        highest_gain = signal["highest_gain"] * 100  # Convert to percentage
        buy_time = datetime.fromtimestamp(signal["buy_entry_time"], tz=timezone.utc)
        
        token_info = token_info_map.get(token_address, {})
        token_symbol = token_info.get('symbol', "Unknown")
        entry_price = token_info.get('entry_price')
        
        analyzed_tokens.append({
            'token_symbol': token_symbol,
            'token_address': token_address,
            'highest_gain': highest_gain,
            'buy_time': buy_time,
            'signal_type': signal["type"],
            'entry_price': entry_price
        })
    
    # Sort by highest gain in descending order
    analyzed_tokens.sort(key=lambda x: x['highest_gain'], reverse=True)
    
    # Take top 10
    top_tokens = analyzed_tokens[:10]
    
    # Generate the performance report message
    message = generate_performance_message(top_tokens, all_tokens=analyzed_tokens)
    
    return message

async def main():
    """Main function to run the analysis and generate report"""
    parser = argparse.ArgumentParser(description='Generate performance report from pump signals')
    parser.add_argument('--days', type=int, default=2, help='Number of days to analyze (default: 2)')
    parser.add_argument('--output', type=str, help='Output file to save the message (optional)')
    parser.add_argument('--chat_id', type=int, default=-1002625916750, help='Telegram chat ID (default: -1002625916750)')
    args = parser.parse_args()
    
    message = await analyze_signals_and_generate_report(args.days)
    
    # Print the message
    print("\n" + "="*80)
    print("GENERATED PERFORMANCE REPORT:")
    print("="*80)
    print(message)
    print("="*80)
    
    # Save to file if specified
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(message)
        print(f"\nMessage saved to {args.output}")
    
    # Get Telegram bot credentials from environment
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not bot_token:
        raise ValueError("TELEGRAM_BOT_TOKEN environment variable not set")
    
    # Initialize bot
    bot = telegram.Bot(token=bot_token)
    
    # Send message to Telegram group
    try:
        await bot.send_message(chat_id=args.chat_id, text=message, parse_mode="Markdown")
        print("\nMessage sent to Telegram group successfully")
    except Exception as e:
        print(f"\nError sending message to Telegram: {e}")

if __name__ == "__main__":
    asyncio.run(main())