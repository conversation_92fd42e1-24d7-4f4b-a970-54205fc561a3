#!/usr/bin/env python3
import sys
import json
import time
import traceback
import os
import urllib.parse
from urllib.parse import unquote
import random
import subprocess

try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriver<PERSON><PERSON>
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException
    from selenium.webdriver.common.action_chains import ActionChains
except ImportError:
    print("Error: undetected-chromedriver not installed. Please install it with: pip install undetected-chromedriver")
    sys.exit(1)

def log(message):
    """Print debug messages to stderr"""
    print(message, file=sys.stderr)

def human_like_mouse_move(driver, element):
    """Simulate human-like mouse movement to an element"""
    action = ActionChains(driver)
    
    # Get element location
    location = element.location
    size = element.size
    
    # Calculate center of element
    center_x = location['x'] + size['width'] / 2
    center_y = location['y'] + size['height'] / 2
    
    # Move mouse in a natural way with some randomness
    current_x = 0
    current_y = 0
    
    # Generate 3-5 points for the mouse to pass through
    num_points = random.randint(3, 5)
    for i in range(num_points):
        # Calculate intermediate point with some randomness
        progress = (i + 1) / (num_points + 1)
        target_x = center_x * progress + random.randint(-50, 50)
        target_y = center_y * progress + random.randint(-50, 50)
        
        # Move to the point
        action.move_by_offset(target_x - current_x, target_y - current_y)
        current_x = target_x
        current_y = target_y
        
        # Add small random delay
        time.sleep(random.uniform(0.1, 0.3))
    
    # Final move to the element
    action.move_to_element(element)
    action.perform()
    
    # Add a small delay before clicking
    time.sleep(random.uniform(0.2, 0.5))

def get_solscan_cookies():
    """Get Solscan cookies and token using undetected-chromedriver"""
    try:
        # Configure Chrome options
        options = uc.ChromeOptions()
        
        # Basic options
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        display = os.environ.get("DISPLAY", None)
        if display:
            options.add_argument(f"--display={display}")
        proxy_url = os.environ.get("PROXY_URL_SCRIPT", None)
        if proxy_url:
            options.add_argument(f"--proxy-server={proxy_url}")
        
        # Get Chrome Beta version and use it
        try:
            chrome_version = subprocess.check_output(['google-chrome-beta', '--version']).decode().strip()
            version_main = int(chrome_version.split()[2].split('.')[0])  # Extract major version
            log(f"Using Chrome Beta version: {version_main}")
        except:
            version_main = None
            log("Could not get Chrome Beta version, using default")
        
        # Create driver with Chrome Beta version
        driver = uc.Chrome(options=options, version_main=version_main)
        
        # Set page load timeout
        driver.set_page_load_timeout(30)
        
        try:
            # Navigate to Solscan
            log("Navigating to Solscan...")
            # First open a new tab with JavaScript
            driver.execute_script('''window.open("https://solscan.io/","_blank");''')
            time.sleep(1)  # Wait for page to load
            
            # Switch to the new tab (it will be the second tab)
            driver.switch_to.window(window_name=driver.window_handles[1])
            
            # Close the original tab
            driver.switch_to.window(window_name=driver.window_handles[0])
            driver.close()
            
            # Switch back to the new tab (now it will be the first tab)
            driver.switch_to.window(window_name=driver.window_handles[0])
            
            # Now navigate to the actual URL
            driver.get("https://solscan.io/")
            
            # Wait for the page to load initially
            log("Waiting for initial page load...")
            time.sleep(1)
            
            # Try to find and interact with the Cloudflare checkbox
            # try:
            #     # Wait for the checkbox to be present
            #     checkbox = WebDriverWait(driver, 10).until(
            #         EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='checkbox']"))
            #     )
                
            #     # Simulate human-like mouse movement to the checkbox
            #     log("Moving mouse to checkbox...")
            #     human_like_mouse_move(driver, checkbox)
                
            #     # Click the checkbox
            #     log("Clicking checkbox...")
            #     checkbox.click()
                
            #     # Wait for potential challenge to complete
            #     time.sleep(5)
                
            # except TimeoutException:
            #     log("No checkbox found, continuing...")
            
            # Initialize result dictionary
            result = {
                "success": False,
                "cf_clearance": "",
                "token": "",  # Keep token field for compatibility
                "user_agent": driver.execute_script("return navigator.userAgent")
            }
            
            # Check for cf_clearance cookie every second for up to 1 minute
            log("Checking for cf_clearance cookie (polling for 1 minute)...")
            start_time = time.time()
            while time.time() - start_time < 60:  # 1 minute timeout
                cookies = driver.get_cookies()
                
                # Find the cf_clearance cookie
                for cookie in cookies:
                    if cookie["name"] == "cf_clearance":
                        result["cf_clearance"] = cookie["value"]
                        log(f"Found cf_clearance cookie after {int(time.time() - start_time)} seconds")
                        break
                if result["cf_clearance"]:
                    break

                # if "Explore Solana Blockchain" appears, cf_clearance is not needed so we can break
                if "Explore Solana Blockchain" in driver.page_source:
                    log("Explore Solana Blockchain found, cf_clearance is not needed so we can break")
                    result["cf_clearance"] = "na"
                    break
                
                # Add a small random delay between checks
                time.sleep(0.5)
            
            # Check if we got the required value
            if result["cf_clearance"]:
                result["success"] = True
                
                # Create cache directory if it doesn't exist
                os.makedirs("cache", exist_ok=True)
                
                # Prepare token data
                token_data = {
                    "tokens": [{
                        "cf_clearance": result["cf_clearance"],
                        "user_agent": result["user_agent"],
                        "host": "solscan.io",
                        "expiry": int(time.time()) + 43200  # 12 hours from now
                    }]
                }
                
                # Write to cache file
                with open("cache/cf_token_cache.json", "w") as f:
                    json.dump(token_data, f)
                
                log("Token data written to cache/cf_token_cache.json")
            else:
                result["error"] = "Missing required cookie (cf_clearance)"
            
            return result
            
        finally:
            # Always close the driver
            driver.quit()
            
    except Exception as e:
        log(f"Error: {e}")
        log(traceback.format_exc())
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    # Get cookies and token
    result = get_solscan_cookies()
    
    # Print the result as JSON
    print(json.dumps(result))