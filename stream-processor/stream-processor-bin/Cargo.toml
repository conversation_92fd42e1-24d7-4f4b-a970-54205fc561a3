[package]
name = "stream-processor-bin"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "stream-processor"
path = "src/main.rs"

[dependencies]
axum.workspace = true
clap.workspace = true
tokio.workspace = true
tracing.workspace = true
yellowstone-vixen.workspace = true
yellowstone-vixen-core.workspace = true
common-program-parsers.workspace = true
event-handlers = { path = "../event-handlers" }
rustls.workspace = true
logging = { path = "../logging" }

[dev-dependencies]
yellowstone-vixen-mock.workspace = true
