use std::borrow::Cow;
use yellowstone_vixen_core::{
    <PERSON><PERSON><PERSON>, Prefi<PERSON>, ProgramParser, Pubkey, instruction::InstructionUpdate,
};

// System program is the zero address - using lazy evaluation since Pubkey::new is not const
fn system_program_id() -> Pubkey {
    Pubkey::new([0; 32])
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct SolTransfer {
    pub slot: u64,
    pub tx_index: u32,
    pub signature: Vec<u8>,
    pub ix_index: u32,
    pub from: Pubkey,
    pub to: Pubkey,
    pub lamports: u64,
    pub from_post_balance: u64,
    pub to_post_balance: u64,
}

#[derive(Debug, <PERSON><PERSON>, Copy)]
pub struct SystemProgramParser;

impl Parser for SystemProgramParser {
    type Input = InstructionUpdate;
    type Output = SolTransfer;

    fn id(&self) -> Cow<str> {
        "common_program_parsers::system_program_parser".into()
    }

    fn prefilter(&self) -> Prefilter {
        Prefilter::builder()
            .transaction_accounts([system_program_id()])
            .build()
            .unwrap()
    }

    async fn parse(&self, ix: &Self::Input) -> yellowstone_vixen_core::ParseResult<Self::Output> {
        // filter out failed transactions
        if ix.shared.err.is_some() {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }

        // System transfer instruction has discriminator [2, 0, 0, 0] followed by u64 lamports
        if ix.data.len() < 12 {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }

        // Check if this is a transfer instruction (discriminator = 2)
        let instruction_type = u32::from_le_bytes([ix.data[0], ix.data[1], ix.data[2], ix.data[3]]);
        if instruction_type != 2 {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }

        // Extract lamports (8 bytes after the 4-byte discriminator)
        let lamports = u64::from_le_bytes([
            ix.data[4],
            ix.data[5],
            ix.data[6],
            ix.data[7],
            ix.data[8],
            ix.data[9],
            ix.data[10],
            ix.data[11],
        ]);

        if ix.accounts.len() < 2 {
            return Err(yellowstone_vixen_core::ParseError::from(
                "Invalid transfer instruction: not enough accounts".to_owned(),
            ));
        }

        let from = ix.accounts[0];
        let to = ix.accounts[1];

        let mut from_idx = None;
        let mut to_idx = None;
        for i in 0.. {
            let acc: yellowstone_vixen_core::KeyBytes<32> = match ix.shared.accounts.get(i) {
                Ok(acc) => acc,
                Err(_) => break,
            };
            if acc == from {
                from_idx = Some(i);
                if to_idx.is_some() {
                    break;
                }
            }
            if acc == to {
                to_idx = Some(i);
                if from_idx.is_some() {
                    break;
                }
            }
        }
        if from_idx.is_none()
            || to_idx.is_none()
            || from_idx.unwrap() >= ix.shared.post_balances.len()
            || to_idx.unwrap() >= ix.shared.post_balances.len()
        {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }

        let from_post_balance = ix.shared.post_balances[from_idx.unwrap()];
        let to_post_balance = ix.shared.post_balances[to_idx.unwrap()];

        Ok(SolTransfer {
            slot: ix.shared.slot,
            tx_index: ix.shared.txn_index as u32,
            signature: ix.shared.signature.clone(),
            ix_index: ix.ix_index as u32,
            from,
            to,
            lamports,
            from_post_balance,
            to_post_balance,
        })
    }
}

impl ProgramParser for SystemProgramParser {
    fn program_id(&self) -> Pubkey {
        system_program_id()
    }
}
