use std::borrow::Cow;
use yellowstone_vixen_core::{
    AccountUpdate, Pa<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Prefilter, ProgramPars<PERSON>, Pub<PERSON>,
};

#[derive(Debug, <PERSON>lone)]
pub struct SolBalance {
    pub slot: u64,
    pub account: Vec<u8>,
    pub lamports: u64,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct SolBalanceParser;

impl Parser for SolBalanceParser {
    type Input = AccountUpdate;
    type Output = SolBalance;

    fn id(&self) -> Cow<str> {
        "common_program_parsers::sol_balance_parser".into()
    }

    fn prefilter(&self) -> Prefilter {
        Prefilter::builder()
            .account_owners([system_program_id()])
            .build()
            .unwrap()
    }

    async fn parse(&self, acct: &AccountUpdate) -> ParseResult<Self::Output> {
        let inner = acct
            .account
            .as_ref()
            .ok_or(yellowstone_vixen_core::ParseError::from(
                "Account data not available".to_owned(),
            ))?;

        // For SOL balance, we only care about the lamports amount
        // The account data itself is not relevant for SOL balance tracking
        Ok(SolBalance {
            slot: acct.slot,
            account: acct.account.as_ref().unwrap().pubkey.clone(),
            lamports: inner.lamports,
        })
    }
}

impl ProgramParser for SolBalanceParser {
    fn program_id(&self) -> Pubkey {
        system_program_id()
    }
}

// System program is the zero address - using lazy evaluation since Pubkey::new is not const
fn system_program_id() -> Pubkey {
    Pubkey::new([0; 32])
}

#[cfg(test)]
mod tests {
    use super::*;
    use yellowstone_vixen_mock::{FixtureData, account_fixture, run_account_parse};

    #[tokio::test]
    async fn test_sol_balance_parsing() {
        let parser = SolBalanceParser;
        let account = account_fixture!("111111111111111111111111111111111", &parser);

        assert_eq!(account.slot, *********);
        assert_eq!(account.lamports, 1000000);
    }
}
