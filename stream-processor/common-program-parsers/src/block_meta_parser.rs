use std::borrow::Cow;
use yellowstone_vixen_core::{<PERSON><PERSON><PERSON>Update, Parse<PERSON>, Prefilter, ProgramParser, Pubkey};

#[derive(<PERSON>bug, <PERSON><PERSON>)]
pub struct SlotTime {
    pub slot: u64,
    pub timestamp: u64,
}

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>)]
pub struct BlockMetaParser;

impl Parser for BlockMetaParser {
    type Input = BlockMetaUpdate;
    type Output = SlotTime;

    fn id(&self) -> Cow<str> {
        "common_program_parsers::block_meta_parser".into()
    }

    fn prefilter(&self) -> Prefilter {
        Prefilter::builder().build().unwrap()
    }

    async fn parse(
        &self,
        block_meta: &Self::Input,
    ) -> yellowstone_vixen_core::ParseResult<Self::Output> {
        if block_meta.block_time.is_none() {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }
        Ok(SlotTime {
            slot: block_meta.slot,
            timestamp: block_meta.block_time.unwrap().timestamp as u64,
        })
    }
}

impl ProgramParser for BlockMetaParser {
    /// "B111111111111111111111111111111111111111112"
    #[inline]
    fn program_id(&self) -> Pubkey {
        Pubkey::new([
            2, 143, 206, 223, 9, 17, 53, 163, 33, 32, 251, 255, 120, 243, 177, 49, 160, 203, 100,
            118, 223, 255, 122, 65, 91, 88, 104, 0, 0, 0, 0, 1,
        ])
    }
}
