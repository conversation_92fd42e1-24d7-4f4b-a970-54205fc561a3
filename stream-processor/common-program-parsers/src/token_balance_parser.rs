//! Common logic for parsing token balances from instruction updates
//!
//! This module provides reusable functions for extracting token account owners
//! and post-transfer balances from Solana instruction updates. It eliminates
//! code duplication across different token transfer instruction parsers.
//!
//! # Examples
//!
//! ```rust
//! use common_program_parsers::TokenBalanceParser;
//!
//! // Get transfer data (owners and post balances) for source and destination accounts
//! let (source_owner, dest_owner, source_balance, dest_balance) =
//!     TokenBalanceParser::get_transfer_data(ix, 0, 2);
//!
//! // Or get individual components
//! let source_owner = TokenBalanceParser::get_account_owner(ix, 0);
//! let source_balance = TokenBalanceParser::get_post_balance(ix, 0);
//! ```

use yellowstone_grpc_proto::solana::storage::confirmed_block::UiTokenAmount;
use yellowstone_vixen_core::{KeyBytes, Pubkey, instruction::InstructionUpdate};

/// Common logic for parsing token balances from instruction updates
///
/// This struct provides static methods for extracting token account information
/// from Solana instruction updates, specifically for transfer operations.
///
/// The methods handle the complexity of searching through pre/post token balances
/// and parsing UI token amounts into raw u64 values.
pub struct TokenBalanceParser;

impl TokenBalanceParser {
    /// Get the owner of a token account from pre/post token balances
    ///
    /// Searches through both pre and post token balances to find the owner
    /// of the account at the specified index.
    ///
    /// # Arguments
    ///
    /// * `ix` - The instruction update containing token balance information
    /// * `account_index` - The index of the account in the instruction's accounts array
    ///
    /// # Returns
    ///
    /// * `Some(Pubkey)` - The owner's public key if found
    /// * `None` - If the account owner cannot be determined
    pub fn get_account_owner(ix: &InstructionUpdate, account_index: usize) -> Option<Pubkey> {
        let token_balances: Vec<_> = ix
            .shared
            .pre_token_balances
            .iter()
            .chain(ix.shared.post_token_balances.iter())
            .collect();

        token_balances
            .iter()
            .find(|b| {
                ix.shared
                    .accounts
                    .get(b.account_index)
                    .unwrap_or(KeyBytes::new([0; 32]))
                    == ix.accounts[account_index]
            })
            .and_then(|b| b.owner.parse::<Pubkey>().ok())
    }

    /// Get the post balance of a token account
    ///
    /// Searches through post token balances to find the balance of the account
    /// at the specified index after the instruction execution.
    ///
    /// # Arguments
    ///
    /// * `ix` - The instruction update containing token balance information
    /// * `account_index` - The index of the account in the instruction's accounts array
    ///
    /// # Returns
    ///
    /// * `u64` - The post-transfer balance, or 0 if not found
    pub fn get_post_balance(ix: &InstructionUpdate, account_index: usize) -> u64 {
        ix.shared
            .post_token_balances
            .iter()
            .find(|b| {
                ix.shared
                    .accounts
                    .get(b.account_index)
                    .unwrap_or(KeyBytes::new([0; 32]))
                    == ix.accounts[account_index]
            })
            .map(|b| {
                b.ui_token_amount
                    .as_ref()
                    .unwrap_or(&UiTokenAmount::default())
                    .amount
                    .parse::<u64>()
                    .unwrap_or(0)
            })
            .unwrap_or(0)
    }

    /// Get both source and destination post balances for transfer instructions
    ///
    /// Convenience method to get post balances for both source and destination
    /// accounts in a single call.
    ///
    /// # Arguments
    ///
    /// * `ix` - The instruction update containing token balance information
    /// * `source_index` - The index of the source account
    /// * `dest_index` - The index of the destination account
    ///
    /// # Returns
    ///
    /// * `(u64, u64)` - Tuple of (source_post_balance, dest_post_balance)
    pub fn get_transfer_post_balances(
        ix: &InstructionUpdate,
        source_index: usize,
        dest_index: usize,
    ) -> (u64, u64) {
        let source_post_balance = Self::get_post_balance(ix, source_index);
        let dest_post_balance = Self::get_post_balance(ix, dest_index);
        (source_post_balance, dest_post_balance)
    }

    /// Get both source and destination owners for transfer instructions
    ///
    /// Convenience method to get owners for both source and destination
    /// accounts in a single call.
    ///
    /// # Arguments
    ///
    /// * `ix` - The instruction update containing token balance information
    /// * `source_index` - The index of the source account
    /// * `dest_index` - The index of the destination account
    ///
    /// # Returns
    ///
    /// * `(Option<Pubkey>, Option<Pubkey>)` - Tuple of (source_owner, dest_owner)
    pub fn get_transfer_owners(
        ix: &InstructionUpdate,
        source_index: usize,
        dest_index: usize,
    ) -> (Option<Pubkey>, Option<Pubkey>) {
        let source_owner = Self::get_account_owner(ix, source_index);
        let dest_owner = Self::get_account_owner(ix, dest_index);
        (source_owner, dest_owner)
    }

    /// Get all transfer-related data (owners and post balances) in one call
    ///
    /// This is the most comprehensive method that returns all the data needed
    /// for transfer instruction parsing in a single call.
    ///
    /// # Arguments
    ///
    /// * `ix` - The instruction update containing token balance information
    /// * `source_index` - The index of the source account
    /// * `dest_index` - The index of the destination account
    ///
    /// # Returns
    ///
    /// * `(Option<Pubkey>, Option<Pubkey>, u64, u64)` - Tuple of (source_owner, dest_owner, source_post_balance, dest_post_balance)
    pub fn get_transfer_data(
        ix: &InstructionUpdate,
        source_index: usize,
        dest_index: usize,
    ) -> (Option<Pubkey>, Option<Pubkey>, u64, u64) {
        let (source_owner, dest_owner) = Self::get_transfer_owners(ix, source_index, dest_index);
        let (source_post_balance, dest_post_balance) =
            Self::get_transfer_post_balances(ix, source_index, dest_index);
        (
            source_owner,
            dest_owner,
            source_post_balance,
            dest_post_balance,
        )
    }
}
