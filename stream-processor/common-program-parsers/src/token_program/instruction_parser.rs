use spl_token::instruction::TokenInstruction;
use yellowstone_vixen_core::{
    <PERSON><PERSON><PERSON><PERSON>, Parse<PERSON><PERSON>r, Pa<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Prefilter, ProgramParser,
    instruction::InstructionUpdate,
};

#[allow(clippy::wildcard_imports)]
use super::instruction_helpers::*;
use crate::{
    Result, ResultExt, TokenBalanceParser, error,
    helpers::{check_min_accounts_req, into_vixen_pubkey},
};

#[derive(Debu<PERSON>, <PERSON>lone, Copy)]
pub struct InstructionParser;

impl Parser for InstructionParser {
    type Input = InstructionUpdate;
    type Output = TokenProgramIxInfo;

    fn id(&self) -> std::borrow::Cow<str> {
        "token_program::InstructionParser".into()
    }

    fn prefilter(&self) -> Prefilter {
        Prefilter::builder()
            .transaction_accounts([spl_token::ID])
            .build()
            .unwrap()
    }

    async fn parse(&self, ix_update: &InstructionUpdate) -> ParseResult<Self::Output> {
        // filter out failed transactions
        if ix_update.shared.err.is_some() {
            return Err(ParseError::Filtered);
        }

        if ix_update.program.equals_ref(spl_token::ID) {
            InstructionParser::parse_impl(ix_update).map_err(|e| ParseError::Other(e.into()))
        } else {
            Err(ParseError::Filtered)
        }
    }
}

impl ProgramParser for InstructionParser {
    #[inline]
    fn program_id(&self) -> yellowstone_vixen_core::Pubkey {
        spl_token::ID.to_bytes().into()
    }
}

impl InstructionParser {
    #[allow(clippy::too_many_lines)]
    pub(crate) fn parse_impl(ix: &InstructionUpdate) -> Result<TokenProgramIxInfo> {
        let ix_type = TokenInstruction::unpack(&ix.data)
            .parse_err("Error unpacking token instruction data")?;
        let accounts_len = ix.accounts.len();
        let parsed_ix = match ix_type {
            TokenInstruction::Transfer { amount } => {
                check_min_accounts_req(accounts_len, 3)?;

                let (source_owner, destination_owner, source_post_balance, dest_post_balance) =
                    TokenBalanceParser::get_transfer_data(ix, 0, 1);

                let token_balances: Vec<_> = ix
                    .shared
                    .pre_token_balances
                    .iter()
                    .chain(ix.shared.post_token_balances.iter())
                    .collect();

                let token_mint = token_balances
                    .iter()
                    .find(|b| {
                        let account = ix
                            .shared
                            .accounts
                            .get(b.account_index)
                            .unwrap_or(KeyBytes::new([0; 32]));
                        account == ix.accounts[0] || account == ix.accounts[1]
                    })
                    .map(|b| b.mint.parse::<KeyBytes<32>>().unwrap());
                if token_mint.is_none() {
                    return Err(error::Error::new("Token mint not found".to_string()));
                }
                let token_mint = token_mint.unwrap();

                Ok(TokenProgramIx::Transfer(
                    TransferAccounts {
                        source: ix.accounts[0],
                        destination: ix.accounts[1],
                        owner: ix.accounts[2],
                        multisig_signers: ix.accounts[3..].to_vec(),
                        source_owner,
                        destination_owner,
                    },
                    TransferData {
                        token_mint,
                        amount,
                        source_post_balance,
                        dest_post_balance,
                    },
                ))
            }
            TokenInstruction::InitializeAccount => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::InitializeAccount(
                    InitializeAccountAccounts {
                        account: ix.accounts[0],
                        mint: ix.accounts[1],
                        owner: ix.accounts[2],
                    },
                ))
            }
            TokenInstruction::InitializeMint {
                decimals,
                mint_authority,
                freeze_authority,
            } => {
                check_min_accounts_req(accounts_len, 1)?;
                Ok(TokenProgramIx::InitializeMint(
                    InitializeMintAccounts {
                        mint: ix.accounts[0],
                    },
                    InitializeMintData {
                        decimals,
                        mint_authority: into_vixen_pubkey(mint_authority),
                        freeze_authority: freeze_authority.map(into_vixen_pubkey).into(),
                    },
                ))
            }
            TokenInstruction::InitializeMint2 {
                decimals,
                mint_authority,
                freeze_authority,
            } => {
                check_min_accounts_req(accounts_len, 1)?;
                Ok(TokenProgramIx::InitializeMint(
                    InitializeMintAccounts {
                        mint: ix.accounts[0],
                    },
                    InitializeMintData {
                        decimals,
                        mint_authority: into_vixen_pubkey(mint_authority),
                        freeze_authority: freeze_authority.map(into_vixen_pubkey).into(),
                    },
                ))
            }

            TokenInstruction::InitializeAccount2 { owner } => {
                check_min_accounts_req(accounts_len, 2)?;
                Ok(TokenProgramIx::InitializeAccount2(
                    InitializeAccount2Accounts {
                        account: ix.accounts[0],
                        mint: ix.accounts[1],
                    },
                    InitializeAccountData2 {
                        owner: into_vixen_pubkey(owner),
                    },
                ))
            }

            TokenInstruction::InitializeAccount3 { owner } => {
                check_min_accounts_req(accounts_len, 2)?;
                Ok(TokenProgramIx::InitializeAccount3(
                    InitializeAccount2Accounts {
                        account: ix.accounts[0],
                        mint: ix.accounts[1],
                    },
                    InitializeAccountData2 {
                        owner: into_vixen_pubkey(owner),
                    },
                ))
            }
            TokenInstruction::InitializeMultisig { m } => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::InitializeMultisig(
                    InitializeMultisigAccounts {
                        multisig: ix.accounts[0],
                        signers: ix.accounts[2..].to_vec(),
                    },
                    InitializeMultisigData { m },
                ))
            }

            TokenInstruction::InitializeMultisig2 { m } => {
                check_min_accounts_req(accounts_len, 2)?;
                Ok(TokenProgramIx::InitializeMultisig(
                    InitializeMultisigAccounts {
                        multisig: ix.accounts[0],
                        signers: ix.accounts[1..].to_vec(),
                    },
                    InitializeMultisigData { m },
                ))
            }

            TokenInstruction::Approve { amount } => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::Approve(
                    ApproveAccounts {
                        source: ix.accounts[0],
                        delegate: ix.accounts[1],
                        owner: ix.accounts[2],
                        multisig_signers: ix.accounts[3..].to_vec(),
                    },
                    ApproveData { amount },
                ))
            }

            TokenInstruction::Revoke => {
                check_min_accounts_req(accounts_len, 2)?;
                Ok(TokenProgramIx::Revoke(RevokeAccounts {
                    source: ix.accounts[0],
                    owner: ix.accounts[1],
                    multisig_signers: ix.accounts[2..].to_vec(),
                }))
            }

            TokenInstruction::SetAuthority {
                authority_type,
                new_authority,
            } => {
                check_min_accounts_req(accounts_len, 2)?;
                Ok(TokenProgramIx::SetAuthority(
                    SetAuthorityAccounts {
                        account: ix.accounts[0],
                        current_authority: ix.accounts[1],
                        multisig_signers: ix.accounts[2..].to_vec(),
                    },
                    SetAuthorityData {
                        authority_type,
                        new_authority: new_authority.map(into_vixen_pubkey).into(),
                    },
                ))
            }

            TokenInstruction::MintTo { amount } => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::MintTo(
                    MintToAccounts {
                        mint: ix.accounts[0],
                        account: ix.accounts[1],
                        mint_authority: ix.accounts[2],
                        multisig_signers: ix.accounts[3..].to_vec(),
                    },
                    MintToData { amount },
                ))
            }

            TokenInstruction::Burn { amount } => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::Burn(
                    BurnAccounts {
                        account: ix.accounts[0],
                        mint: ix.accounts[1],
                        owner: ix.accounts[2],
                        multisig_signers: ix.accounts[3..].to_vec(),
                    },
                    BurnData { amount },
                ))
            }

            TokenInstruction::CloseAccount => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::CloseAccount(CloseAccountAccounts {
                    account: ix.accounts[0],
                    destination: ix.accounts[1],
                    owner: ix.accounts[2],
                    multisig_signers: ix.accounts[3..].to_vec(),
                }))
            }

            TokenInstruction::FreezeAccount => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::FreezeAccount(FreezeAccountAccounts {
                    account: ix.accounts[0],
                    mint: ix.accounts[1],
                    mint_freeze_authority: ix.accounts[2],
                    multisig_signers: ix.accounts[3..].to_vec(),
                }))
            }

            TokenInstruction::ThawAccount => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::ThawAccount(ThawAccountAccounts {
                    account: ix.accounts[0],
                    mint: ix.accounts[1],
                    mint_freeze_authority: ix.accounts[2],
                    multisig_signers: ix.accounts[3..].to_vec(),
                }))
            }

            TokenInstruction::TransferChecked { amount, decimals } => {
                check_min_accounts_req(accounts_len, 4)?;

                let (source_owner, destination_owner, source_post_balance, dest_post_balance) =
                    TokenBalanceParser::get_transfer_data(ix, 0, 2);

                Ok(TokenProgramIx::TransferChecked(
                    TransferCheckedAccounts {
                        source: ix.accounts[0],
                        mint: ix.accounts[1],
                        destination: ix.accounts[2],
                        owner: ix.accounts[3],
                        multisig_signers: ix.accounts[4..].to_vec(),
                        source_owner,
                        destination_owner,
                    },
                    TransferCheckedData {
                        amount,
                        decimals,
                        source_post_balance,
                        dest_post_balance,
                    },
                ))
            }

            TokenInstruction::ApproveChecked { amount, decimals } => {
                check_min_accounts_req(accounts_len, 4)?;
                Ok(TokenProgramIx::ApproveChecked(
                    ApproveCheckedAccounts {
                        source: ix.accounts[0],
                        mint: ix.accounts[1],
                        delegate: ix.accounts[2],
                        owner: ix.accounts[3],
                        multisig_signers: ix.accounts[4..].to_vec(),
                    },
                    ApproveCheckedData { amount, decimals },
                ))
            }

            TokenInstruction::MintToChecked { amount, decimals } => {
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::MintToChecked(
                    MintToCheckedAccounts {
                        mint: ix.accounts[0],
                        account: ix.accounts[1],
                        mint_authority: ix.accounts[2],
                        multisig_signers: ix.accounts[3..].to_vec(),
                    },
                    MintToCheckedData { amount, decimals },
                ))
            }

            TokenInstruction::BurnChecked { amount, decimals } => {
                //TODO : this ix needs 3 accounts , but only 1 account is available in the instruction
                check_min_accounts_req(accounts_len, 3)?;
                Ok(TokenProgramIx::BurnChecked(
                    BurnCheckedAccounts {
                        account: ix.accounts[0],
                        mint: ix.accounts[1],
                        owner: ix.accounts[2],
                        multisig_signers: ix.accounts[3..].to_vec(),
                    },
                    BurnCheckedData { amount, decimals },
                ))
            }

            TokenInstruction::SyncNative => {
                check_min_accounts_req(accounts_len, 1)?;
                Ok(TokenProgramIx::SyncNative(SyncNativeAccounts {
                    account: ix.accounts[0],
                }))
            }

            TokenInstruction::GetAccountDataSize => {
                check_min_accounts_req(accounts_len, 1)?;
                Ok(TokenProgramIx::GetAccountDataSize(
                    GetAccountDataSizeAccounts {
                        mint: ix.accounts[0],
                    },
                ))
            }

            TokenInstruction::InitializeImmutableOwner => {
                check_min_accounts_req(accounts_len, 1)?;
                Ok(TokenProgramIx::InitializeImmutableOwner(
                    InitializeImmutableOwnerAccounts {
                        account: ix.accounts[0],
                    },
                ))
            }

            TokenInstruction::AmountToUiAmount { amount } => {
                check_min_accounts_req(accounts_len, 1)?;
                Ok(TokenProgramIx::AmountToUiAmount(
                    AmountToUiAmountAccounts {
                        mint: ix.accounts[0],
                    },
                    AmountToUiAmountData { amount },
                ))
            }

            TokenInstruction::UiAmountToAmount { ui_amount } => {
                check_min_accounts_req(accounts_len, 1)?;
                Ok(TokenProgramIx::UiAmountToAmount(
                    UiAmountToAmountAccounts {
                        mint: ix.accounts[0],
                    },
                    UiAmountToAmountData {
                        ui_amount: ui_amount.into(),
                    },
                ))
            }
        };
        Ok(TokenProgramIxInfo {
            ix: parsed_ix?,
            signature: ix.shared.signature.clone(),
            slot: ix.shared.slot,
            tx_index: ix.shared.txn_index as u32,
            ix_index: ix.ix_index as u32,
        })
    }
}

#[cfg(feature = "proto")]
mod proto_parser {
    use yellowstone_vixen_core::proto::ParseProto;
    use yellowstone_vixen_proto::parser::token::ProgramIxs as TokenProgramIxProto;

    use super::InstructionParser;
    use crate::helpers::IntoProto;

    impl ParseProto for InstructionParser {
        type Message = TokenProgramIxProto;

        fn output_into_message(value: Self::Output) -> Self::Message {
            value.into_proto()
        }
    }
}

#[cfg(test)]
mod tests {
    use std::ops::Mul;

    use yellowstone_vixen_mock::tx_fixture;

    use super::*;

    #[tokio::test]
    async fn test_mint_to_checked_ix_parsing() {
        let parser = InstructionParser;

        let ixs = tx_fixture!(
            "55kpnRufcX9Fo44oRBXtrkxPRww4UWJKxCpgBV39kzAAag8oyJbd9Y3YWdQQUi3TBqtrhjgsMGb9Nw8bUxy7j5rt",
            &parser
        );

        let TokenProgramIx::MintToChecked(_accts, data) = &ixs[0].ix else {
            panic!("Invalid Instruction");
        };

        assert_eq!(data.decimals, 10);
        assert_eq!(data.amount, 10.mul(10u64.pow(data.decimals.into())));
    }
}
