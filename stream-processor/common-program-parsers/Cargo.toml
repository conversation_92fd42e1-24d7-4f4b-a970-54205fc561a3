[package]
name = "common-program-parsers"
version = "0.1.0"
edition = "2024"

[dependencies]
spl-pod = { version = "0.3.0" }
spl-token = { version = "6.0.0" }
spl-token-2022 = { version = "4.0.0" }
spl-type-length-value = { version = "0.5.0" }
spl-token-group-interface = { version = "0.3.0" }
spl-token-metadata-interface = { version = "0.4.0" }
yellowstone-vixen = { workspace = true }
yellowstone-vixen-core = { workspace = true }
yellowstone-vixen-proto = { workspace = true, features = [
  "parser",
], optional = true }
thiserror = "1.0.64"
yellowstone-grpc-proto.workspace = true

[dev-dependencies]
tokio = { version = "1", features = ["full"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
parking_lot = "0.12"
yellowstone-vixen-mock = { git = "https://github.com/kryptogo/yellowstone-vixen" }
yellowstone-grpc-proto = { workspace = true }

[features]
default = []
block-meta = []
proto = [
  "dep:yellowstone-vixen-proto",
  "yellowstone-vixen-core/proto",
  "yellowstone-vixen-proto/parser",
]
raydium = []
