use crate::db_writer::{<PERSON><PERSON><PERSON><PERSON><PERSON>, SolTransferRow, TokenTransferRow};
use std::sync::Arc;
use tokio::sync::Mutex;
use yellowstone_vixen::HandlerResult;

const MAX_BATCH_SIZE: usize = 3000;

/// Manages buffering and flushing of transfer events (SOL and token transfers)
#[derive(<PERSON><PERSON>, Debug)]
pub struct TransferBuffer {
    db_writer: Arc<DbWriter>,
    sol_buffer: Arc<Mutex<Vec<SolTransferRow>>>,
    token_buffer: Arc<Mutex<Vec<TokenTransferRow>>>,
}

impl TransferBuffer {
    pub fn new(db_writer: Arc<DbWriter>) -> Self {
        Self {
            db_writer,
            sol_buffer: Arc::new(Mutex::new(Vec::with_capacity(MAX_BATCH_SIZE))),
            token_buffer: Arc::new(Mutex::new(Vec::with_capacity(MAX_BATCH_SIZE))),
        }
    }

    pub async fn flush_sol(&self) -> HandlerResult<()> {
        let mut buf = self.sol_buffer.lock().await;
        if !buf.is_empty() {
            self.db_writer
                .copy_sol_transfers_binary(&buf)
                .await
                .map_err(|e| anyhow::anyhow!(e))?;
            buf.clear();
        }
        Ok(())
    }

    pub async fn flush_token(&self) -> HandlerResult<()> {
        let mut buf = self.token_buffer.lock().await;
        if !buf.is_empty() {
            self.db_writer
                .copy_token_transfers_binary(&buf)
                .await
                .map_err(|e| anyhow::anyhow!(e))?;
            buf.clear();
        }
        Ok(())
    }

    pub async fn add_sol_transfer(&self, row: SolTransferRow) -> HandlerResult<()> {
        let mut buf = self.sol_buffer.lock().await;
        buf.push(row);
        if buf.len() >= MAX_BATCH_SIZE {
            drop(buf);
            self.flush_sol().await?;
        }
        Ok(())
    }

    pub async fn add_token_transfer(&self, row: TokenTransferRow) -> HandlerResult<()> {
        let mut buf = self.token_buffer.lock().await;
        buf.push(row);
        if buf.len() >= MAX_BATCH_SIZE {
            drop(buf);
            self.flush_token().await?;
        }
        Ok(())
    }

    pub async fn flush_all(&self) -> HandlerResult<()> {
        tracing::info!("Flushing all transfer buffers");
        let (sol_result, token_result) = tokio::join!(self.flush_sol(), self.flush_token(),);
        sol_result?;
        token_result?;
        Ok(())
    }
}
