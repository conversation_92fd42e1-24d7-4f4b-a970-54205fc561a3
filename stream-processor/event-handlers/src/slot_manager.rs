use crate::db_writer::{
    <PERSON>b<PERSON><PERSON>r, SlotTimeRow, SolBalanceRow, TokenBalanceRow, TransactionSignatureRow,
};
use rust_decimal::Decimal;
use std::collections::{BTreeSet, HashMap};
use std::sync::Arc;
use tokio::sync::Mutex;
use yellowstone_vixen::HandlerResult;

/// Manages slot-based data structures for efficient deduplication and flushing
#[derive(<PERSON>lone, Debug)]
pub struct SlotManager {
    db_writer: Arc<DbWriter>,
    // Slot-based data structures for efficient deduplication and flushing
    transaction_signatures: Arc<Mutex<HashMap<u64, HashMap<u32, Vec<u8>>>>>, // slot -> tx_index -> signature
    sol_balances: Arc<Mutex<HashMap<u64, HashMap<Vec<u8>, (Decimal, u32)>>>>, // slot -> account -> (amount, tx_index)
    token_balances: Arc<Mutex<HashMap<u64, HashMap<(Vec<u8>, Vec<u8>), (Decimal, u32)>>>>, // slot -> (owner, token) -> (amount, tx_index)
    slot_times: Arc<Mutex<HashMap<u64, i64>>>, // slot -> timestamp
    slots_seen: Arc<Mutex<BTreeSet<u64>>>,     // Sorted set of all slots we've seen
}

impl SlotManager {
    pub fn new(db_writer: Arc<DbWriter>) -> Self {
        Self {
            db_writer,
            transaction_signatures: Arc::new(Mutex::new(HashMap::new())),
            sol_balances: Arc::new(Mutex::new(HashMap::new())),
            token_balances: Arc::new(Mutex::new(HashMap::new())),
            slot_times: Arc::new(Mutex::new(HashMap::new())),
            slots_seen: Arc::new(Mutex::new(BTreeSet::new())),
        }
    }

    /// Flush data for specific slots (2 slots at a time)
    /// This method removes all data for the specified slot range from memory after flushing
    async fn flush_slots(&self, start_slot: u64, end_slot: u64) -> HandlerResult<()> {
        // Collect data while holding locks
        let (tx_sig_rows, sol_bal_rows, token_bal_rows, slot_time_rows) = {
            let mut tx_sigs = self.transaction_signatures.lock().await;
            let mut sol_bals = self.sol_balances.lock().await;
            let mut token_bals = self.token_balances.lock().await;
            let mut slot_times = self.slot_times.lock().await;

            let mut tx_sig_rows = Vec::new();
            let mut sol_bal_rows = Vec::new();
            let mut token_bal_rows = Vec::new();
            let mut slot_time_rows = Vec::new();

            for slot in start_slot..=end_slot {
                // Collect and remove transaction signatures for the slot
                if let Some(tx_indices) = tx_sigs.remove(&slot) {
                    for (tx_index, signature) in tx_indices {
                        tx_sig_rows.push(TransactionSignatureRow {
                            slot: slot as i64,
                            tx_index: tx_index as i32,
                            signature,
                        });
                    }
                }

                // Collect and remove sol balances for the slot
                if let Some(accounts) = sol_bals.remove(&slot) {
                    for (account, (amount, _)) in accounts {
                        sol_bal_rows.push(SolBalanceRow {
                            slot: slot as i64,
                            account,
                            amount,
                        });
                    }
                }

                // Collect and remove token balances for the slot
                if let Some(owner_tokens) = token_bals.remove(&slot) {
                    for ((owner, token), (amount, _)) in owner_tokens {
                        token_bal_rows.push(TokenBalanceRow {
                            slot: slot as i64,
                            owner,
                            token,
                            amount,
                        });
                    }
                }

                // Collect and remove slot time for the slot
                if let Some(timestamp) = slot_times.remove(&slot) {
                    slot_time_rows.push(SlotTimeRow {
                        slot: slot as i64,
                        timestamp,
                    });
                }
            }

            (tx_sig_rows, sol_bal_rows, token_bal_rows, slot_time_rows)
        };

        // Release locks and perform DB writes in parallel
        let (tx_sig_result, sol_bal_result, token_bal_result, slot_time_result) = tokio::join!(
            async {
                if !tx_sig_rows.is_empty() {
                    self.db_writer
                        .copy_transaction_signatures_binary(&tx_sig_rows)
                        .await
                        .map_err(|e| anyhow::anyhow!(e))
                } else {
                    Ok(())
                }
            },
            async {
                if !sol_bal_rows.is_empty() {
                    self.db_writer
                        .copy_sol_balances_binary(&sol_bal_rows)
                        .await
                        .map_err(|e| anyhow::anyhow!(e))
                } else {
                    Ok(())
                }
            },
            async {
                if !token_bal_rows.is_empty() {
                    self.db_writer
                        .copy_token_balances_binary(&token_bal_rows)
                        .await
                        .map_err(|e| anyhow::anyhow!(e))
                } else {
                    Ok(())
                }
            },
            async {
                if !slot_time_rows.is_empty() {
                    self.db_writer
                        .copy_slot_times_binary(&slot_time_rows)
                        .await
                        .map_err(|e| anyhow::anyhow!(e))
                } else {
                    Ok(())
                }
            }
        );

        // Check all results
        tx_sig_result?;
        sol_bal_result?;
        token_bal_result?;
        slot_time_result?;

        Ok(())
    }

    /// Check if we should flush based on slot progress
    pub async fn check_and_flush_slots(&self, new_slot: u64) -> HandlerResult<()> {
        let mut slots_seen = self.slots_seen.lock().await;

        // Only proceed if this is a new slot
        if slots_seen.insert(new_slot) {
            // Check if new_slot - 3 exists in our set
            let flush_candidate = new_slot - 3;
            if slots_seen.contains(&flush_candidate) {
                // Flush slots new_slot - 3 and new_slot - 2
                let start_slot = flush_candidate;
                let end_slot = new_slot - 2;

                // Remove the flushed slots from our set
                slots_seen.remove(&start_slot);
                slots_seen.remove(&end_slot);

                drop(slots_seen);

                // Flush the slots
                self.flush_slots(start_slot, end_slot).await?;
            }
        }

        Ok(())
    }

    /// Flush all remaining slot-based data
    pub async fn flush_all(&self) -> HandlerResult<()> {
        tracing::info!("Flushing all slot-based data");
        let mut slots_seen = self.slots_seen.lock().await;
        let slots_to_flush: Vec<u64> = slots_seen.iter().cloned().collect();

        // Clear all slots from the set since we're flushing everything
        slots_seen.clear();
        drop(slots_seen);

        // Flush remaining slots
        if !slots_to_flush.is_empty() {
            let start = slots_to_flush[0];
            let end = slots_to_flush[slots_to_flush.len() - 1];
            tracing::info!("Flushing all slot-based data from {} to {}", start, end);
            self.flush_slots(start, end).await?;
        }

        Ok(())
    }

    pub async fn add_transaction_signature(
        &self,
        signature: Vec<u8>,
        slot: u64,
        tx_index: u32,
    ) -> HandlerResult<()> {
        let mut tx_sigs = self.transaction_signatures.lock().await;
        tx_sigs
            .entry(slot)
            .or_insert_with(HashMap::new)
            .insert(tx_index, signature);
        Ok(())
    }

    pub async fn add_sol_balance(
        &self,
        slot: u64,
        tx_index: u32,
        account: Vec<u8>,
        amount: Decimal,
    ) -> HandlerResult<()> {
        let mut sol_bals = self.sol_balances.lock().await;
        sol_bals
            .entry(slot)
            .or_insert_with(HashMap::new)
            .entry(account)
            .and_modify(|(_, existing_tx_index)| {
                if tx_index > *existing_tx_index {
                    *existing_tx_index = tx_index;
                }
            })
            .or_insert((amount, tx_index));
        Ok(())
    }

    pub async fn add_token_balance(
        &self,
        slot: u64,
        owner: Vec<u8>,
        token: Vec<u8>,
        amount: Decimal,
        tx_index: u32,
    ) -> HandlerResult<()> {
        let mut token_bals = self.token_balances.lock().await;
        let slot_balances = token_bals.entry(slot).or_insert_with(HashMap::new);
        let key = (owner, token);
        if let Some((_, existing_tx_index)) = slot_balances.get(&key) {
            if tx_index <= *existing_tx_index {
                return Ok(());
            }
        }
        slot_balances.insert(key, (amount, tx_index));
        Ok(())
    }

    pub async fn add_slot_time(&self, slot: u64, timestamp: i64) -> HandlerResult<()> {
        let mut slot_times = self.slot_times.lock().await;
        slot_times.insert(slot, timestamp);
        Ok(())
    }
}
