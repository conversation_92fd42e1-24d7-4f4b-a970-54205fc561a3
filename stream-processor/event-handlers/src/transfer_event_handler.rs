use common_program_parsers::{
    token_extension_program::{
        TokenExtensionProgramIx, TokenExtensionProgramIxInfo, TransferFeeIx,
    },
    token_program::{TokenProgramIx, TokenProgramIxInfo},
    SlotTime,
};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use tokio::time::interval;
use tracing;
use yellowstone_vixen::{<PERSON><PERSON>, HandlerResult};

use crate::publisher::PubSubClient;
use common_program_parsers::system_program_parser::SolTransfer;
use event_proto::{
    BatchedSolanaEvent, SolBalanceEvent, SolTransferEventV2, SolanaEvent, TokenBalanceEvent,
    TokenTransferEventV2, TransactionEvent,
};

const MAX_BATCH_SIZE: usize = 3000;

#[derive(<PERSON><PERSON>, Debug)]
pub struct TransferEventHandler {
    publisher: Arc<PubSubClient>,
    batch: Arc<Mutex<VecDeque<SolanaEvent>>>,
    transaction_indices: Arc<Mutex<HashMap<(u64, u32), usize>>>,
    sol_balance_indices: Arc<Mutex<HashMap<(u64, Vec<u8>), (usize, u32)>>>,
    token_balance_indices: Arc<Mutex<HashMap<(u64, Vec<u8>, Vec<u8>), (usize, u32)>>>,
    include_balance_events: bool,
}

impl TransferEventHandler {
    pub fn new(
        publisher: Arc<PubSubClient>,
        flush_interval: Duration,
        include_balance_events: bool,
    ) -> Self {
        let handler = Self {
            publisher,
            batch: Arc::new(Mutex::new(VecDeque::new())),
            transaction_indices: Arc::new(Mutex::new(HashMap::new())),
            sol_balance_indices: Arc::new(Mutex::new(HashMap::new())),
            token_balance_indices: Arc::new(Mutex::new(HashMap::new())),
            include_balance_events,
        };

        // Spawn background task to periodically flush batches
        let handler_clone = handler.clone();

        // The plugin's runtime will handle this task
        tokio::spawn(async move {
            let mut interval = interval(flush_interval);
            loop {
                interval.tick().await;
                if let Err(e) = handler_clone.flush_batch(true).await {
                    tracing::error!("Failed to flush batch: {:?}", e);
                }
            }
        });

        handler
    }

    pub async fn shutdown(&self) -> HandlerResult<()> {
        tracing::info!("Shutting down TransferEventHandler, flushing remaining data...");

        // Keep flushing until batch is empty
        loop {
            let batch_len = {
                let batch = self.batch.lock().await;
                batch.len()
            };

            if batch_len == 0 {
                tracing::info!("All data flushed successfully");
                break;
            }

            tracing::info!("Flushing remaining {} events", batch_len);
            if let Err(e) = self.flush_batch(true).await {
                tracing::error!("Failed to flush batch during shutdown: {:?}", e);
                return Err(e);
            }

            // Small delay to prevent tight loop
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        Ok(())
    }

    async fn add_transaction_to_batch(
        &self,
        slot: u64,
        tx_index: u32,
        signature: Vec<u8>,
    ) -> HandlerResult<()> {
        {
            let mut batch = self.batch.lock().await;
            let mut tx_indices = self.transaction_indices.lock().await;

            let key = (slot, tx_index);

            // O(1) lookup for existing transaction event
            if let std::collections::hash_map::Entry::Vacant(e) = tx_indices.entry(key) {
                // No duplicate found, add new event
                let event = SolanaEvent {
                    event: Some(event_proto::solana_event::Event::Transaction(
                        TransactionEvent {
                            slot,
                            tx_index,
                            signature,
                        },
                    )),
                };
                let new_index = batch.len();
                batch.push_back(event);
                e.insert(new_index);
            } else {
                // Transaction already exists, don't overwrite
                return Ok(());
            }
        }

        self.flush_batch(false).await?;
        Ok(())
    }

    async fn add_sol_balance_to_batch(
        &self,
        slot: u64,
        tx_index: u32,
        account: Vec<u8>,
        amount: u64,
    ) -> HandlerResult<()> {
        if !self.include_balance_events {
            return Ok(());
        }

        {
            let mut batch = self.batch.lock().await;
            let mut sol_balance_indices = self.sol_balance_indices.lock().await;

            let key = (slot, account.clone());

            // Use entry API to avoid borrow checker issues
            match sol_balance_indices.entry(key) {
                std::collections::hash_map::Entry::Occupied(mut entry) => {
                    let (existing_index, existing_tx_index) = *entry.get();
                    // Only overwrite if the new tx_index is higher (more recent)
                    if tx_index > existing_tx_index {
                        // Replace the existing event
                        let new_event = SolanaEvent {
                            event: Some(event_proto::solana_event::Event::SolBalance(
                                SolBalanceEvent {
                                    slot,
                                    account: account.clone(),
                                    amount,
                                },
                            )),
                        };
                        batch[existing_index] = new_event;
                        // Update the stored tx_index
                        entry.insert((existing_index, tx_index));
                    }
                    // If tx_index is not higher, don't overwrite
                    return Ok(());
                }
                std::collections::hash_map::Entry::Vacant(entry) => {
                    // No existing event, add new event
                    let event = SolanaEvent {
                        event: Some(event_proto::solana_event::Event::SolBalance(
                            SolBalanceEvent {
                                slot,
                                account: account.clone(),
                                amount,
                            },
                        )),
                    };
                    let new_index = batch.len();
                    batch.push_back(event);
                    entry.insert((new_index, tx_index));
                }
            }
        };

        self.flush_batch(false).await?;
        Ok(())
    }

    async fn add_token_balance_to_batch(
        &self,
        slot: u64,
        tx_index: u32,
        owner: Vec<u8>,
        token: Vec<u8>,
        amount: u64,
    ) -> HandlerResult<()> {
        if !self.include_balance_events {
            return Ok(());
        }

        {
            let mut batch = self.batch.lock().await;
            let mut token_balance_indices = self.token_balance_indices.lock().await;

            let key = (slot, owner.clone(), token.clone());

            // Use entry API to avoid borrow checker issues
            match token_balance_indices.entry(key) {
                std::collections::hash_map::Entry::Occupied(mut entry) => {
                    let (existing_index, existing_tx_index) = *entry.get();
                    // Only overwrite if the new tx_index is higher (more recent)
                    if tx_index > existing_tx_index {
                        // Replace the existing event
                        let new_event = SolanaEvent {
                            event: Some(event_proto::solana_event::Event::TokenBalance(
                                TokenBalanceEvent {
                                    slot,
                                    owner: owner.clone(),
                                    token: token.clone(),
                                    amount,
                                },
                            )),
                        };
                        batch[existing_index] = new_event;
                        // Update the stored tx_index
                        entry.insert((existing_index, tx_index));
                    }
                    // If tx_index is not higher, don't overwrite
                    return Ok(());
                }
                std::collections::hash_map::Entry::Vacant(entry) => {
                    // No existing event, add new event
                    let event = SolanaEvent {
                        event: Some(event_proto::solana_event::Event::TokenBalance(
                            TokenBalanceEvent {
                                slot,
                                owner: owner.clone(),
                                token: token.clone(),
                                amount,
                            },
                        )),
                    };
                    let new_index = batch.len();
                    batch.push_back(event);
                    entry.insert((new_index, tx_index));
                }
            }
        };

        self.flush_batch(false).await?;
        Ok(())
    }

    async fn add_to_batch(&self, event: SolanaEvent) -> HandlerResult<()> {
        {
            let mut batch = self.batch.lock().await;
            batch.push_back(event);
        }
        self.flush_batch(false).await?;
        Ok(())
    }

    async fn flush_batch(&self, force: bool) -> HandlerResult<()> {
        let mut batch = self.batch.lock().await;
        let mut tx_indices = self.transaction_indices.lock().await;
        let mut sol_balance_indices = self.sol_balance_indices.lock().await;
        let mut token_balance_indices = self.token_balance_indices.lock().await;

        if batch.len() == 0 || (batch.len() < MAX_BATCH_SIZE && !force) {
            return Ok(());
        }
        let events: Vec<SolanaEvent> = batch.drain(..).collect();
        drop(batch);

        let batched_event = BatchedSolanaEvent { events };
        tracing::info!("Flushing batch of {} events", batched_event.events.len());

        // Clear all indices since we're flushing the batch
        tx_indices.clear();
        sol_balance_indices.clear();
        token_balance_indices.clear();
        drop(tx_indices);
        drop(sol_balance_indices);
        drop(token_balance_indices);

        if !batched_event.events.is_empty() {
            tracing::debug!("Publishing to PubSub topic: solana-events");
            self.publisher
                .publish_proto("solana-events", &batched_event)
                .await?;
            tracing::debug!("Successfully published to PubSub");
        }
        Ok(())
    }
}

impl Handler<SolTransfer> for TransferEventHandler {
    async fn handle(&self, parsed: &SolTransfer) -> HandlerResult<()> {
        // Add transaction event first (deduplicated)
        self.add_transaction_to_batch(parsed.slot, parsed.tx_index, parsed.signature.clone())
            .await?;

        // Add transfer event
        let solana_event_new = SolanaEvent {
            event: Some(event_proto::solana_event::Event::SolTransferV2(
                SolTransferEventV2 {
                    slot: parsed.slot,
                    tx_index: parsed.tx_index,
                    ix_index: parsed.ix_index,
                    from_wallet: parsed.from.to_vec(),
                    to_wallet: parsed.to.to_vec(),
                    amount: parsed.lamports,
                },
            )),
        };
        self.add_to_batch(solana_event_new).await?;

        // Add balance events if enabled
        if self.include_balance_events {
            // Add from wallet balance
            self.add_sol_balance_to_batch(
                parsed.slot,
                parsed.tx_index,
                parsed.from.to_vec(),
                parsed.from_post_balance,
            )
            .await?;

            // Add to wallet balance
            self.add_sol_balance_to_batch(
                parsed.slot,
                parsed.tx_index,
                parsed.to.to_vec(),
                parsed.to_post_balance,
            )
            .await?;
        }

        Ok(())
    }
}

impl Handler<TokenProgramIxInfo> for TransferEventHandler {
    async fn handle(&self, parsed: &TokenProgramIxInfo) -> HandlerResult<()> {
        // Add transaction event first (deduplicated)
        self.add_transaction_to_batch(parsed.slot, parsed.tx_index, parsed.signature.clone())
            .await?;

        // Extract transfer data and balance information
        let (token_mint, from_wallet, to_wallet, amount, from_post_balance, to_post_balance) =
            match &parsed.ix {
                TokenProgramIx::TransferChecked(accounts, data) => (
                    accounts.mint,
                    accounts.source_owner.as_ref(),
                    accounts.destination_owner.as_ref(),
                    data.amount,
                    data.source_post_balance,
                    data.dest_post_balance,
                ),
                TokenProgramIx::Transfer(accounts, data) => (
                    data.token_mint,
                    accounts.source_owner.as_ref(),
                    accounts.destination_owner.as_ref(),
                    data.amount,
                    data.source_post_balance,
                    data.dest_post_balance,
                ),
                _ => return Ok(()),
            };

        let (from_wallet, to_wallet) = match (from_wallet, to_wallet) {
            (Some(from), Some(to)) => (from, to),
            _ => return Ok(()),
        };

        // Add transfer event
        let event_new = TokenTransferEventV2 {
            slot: parsed.slot,
            tx_index: parsed.tx_index,
            ix_index: parsed.ix_index,
            token_mint: token_mint.to_vec(),
            from_wallet: from_wallet.to_vec(),
            to_wallet: to_wallet.to_vec(),
            amount,
        };
        let solana_event_new = SolanaEvent {
            event: Some(event_proto::solana_event::Event::TokenTransferV2(event_new)),
        };
        self.add_to_batch(solana_event_new).await?;

        // Add balance events if enabled
        if self.include_balance_events {
            // Add from wallet token balance
            self.add_token_balance_to_batch(
                parsed.slot,
                parsed.tx_index,
                from_wallet.to_vec(),
                token_mint.to_vec(),
                from_post_balance,
            )
            .await?;

            // Add to wallet token balance
            self.add_token_balance_to_batch(
                parsed.slot,
                parsed.tx_index,
                to_wallet.to_vec(),
                token_mint.to_vec(),
                to_post_balance,
            )
            .await?;
        }

        Ok(())
    }
}

impl Handler<TokenExtensionProgramIxInfo> for TransferEventHandler {
    async fn handle(&self, parsed: &TokenExtensionProgramIxInfo) -> HandlerResult<()> {
        // Add transaction event first (deduplicated)
        self.add_transaction_to_batch(parsed.slot, parsed.tx_index, parsed.signature.clone())
            .await?;

        // Extract transfer data and balance information
        let (token_mint, from_wallet, to_wallet, amount, from_post_balance, to_post_balance) =
            match &parsed.ix {
                TokenExtensionProgramIx::TokenProgramIx(TokenProgramIx::TransferChecked(
                    accounts,
                    data,
                )) => (
                    accounts.mint,
                    accounts.source_owner.as_ref(),
                    accounts.destination_owner.as_ref(),
                    data.amount,
                    data.source_post_balance,
                    data.dest_post_balance,
                ),
                TokenExtensionProgramIx::TransferFeeIx(TransferFeeIx::TransferCheckedWithFee(
                    accounts,
                    data,
                )) => (
                    accounts.mint,
                    accounts.source_owner.as_ref(),
                    accounts.destination_owner.as_ref(),
                    data.amount,
                    data.source_post_balance,
                    data.dest_post_balance,
                ),
                _ => return Ok(()),
            };

        let (from_wallet, to_wallet) = match (from_wallet, to_wallet) {
            (Some(from), Some(to)) => (from, to),
            _ => return Ok(()),
        };

        // Add transfer event
        let event_new = TokenTransferEventV2 {
            slot: parsed.slot,
            tx_index: parsed.tx_index,
            ix_index: parsed.ix_index,
            token_mint: token_mint.to_vec(),
            from_wallet: from_wallet.to_vec(),
            to_wallet: to_wallet.to_vec(),
            amount,
        };
        let solana_event_new = SolanaEvent {
            event: Some(event_proto::solana_event::Event::TokenTransferV2(event_new)),
        };
        self.add_to_batch(solana_event_new).await?;

        // Add balance events if enabled
        if self.include_balance_events {
            // Add from wallet token balance
            self.add_token_balance_to_batch(
                parsed.slot,
                parsed.tx_index,
                from_wallet.to_vec(),
                token_mint.to_vec(),
                from_post_balance,
            )
            .await?;

            // Add to wallet token balance
            self.add_token_balance_to_batch(
                parsed.slot,
                parsed.tx_index,
                to_wallet.to_vec(),
                token_mint.to_vec(),
                to_post_balance,
            )
            .await?;
        }

        Ok(())
    }
}

impl Handler<SlotTime> for TransferEventHandler {
    async fn handle(&self, parsed: &SlotTime) -> HandlerResult<()> {
        let event = event_proto::SlotTime {
            slot: parsed.slot,
            timestamp: parsed.timestamp,
        };
        let solana_event = SolanaEvent {
            event: Some(event_proto::solana_event::Event::SlotTime(event)),
        };
        self.add_to_batch(solana_event).await
    }
}
