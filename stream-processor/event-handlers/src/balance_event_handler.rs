use common_program_parsers::{
    sol_balance_parser::SolBalance,
    token_extension_program::{TokenExtensionState, TokenExtensionStateInfo},
    token_program::{TokenProgramState, TokenProgramStateInfo},
};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use tokio::time::interval;
use tracing;
use yellowstone_vixen::{<PERSON><PERSON>, HandlerResult};

use crate::publisher::PubSubClient;
use event_proto::{BatchedSolanaEvent, SolBalanceEvent, SolanaEvent, TokenBalanceEvent};

const MAX_BATCH_SIZE: usize = 3000;

#[derive(Clone, Debug)]
pub struct BalanceEventHandler {
    publisher: Arc<PubSubClient>,
    batch: Arc<Mutex<VecDeque<SolanaEvent>>>,
    sol_balance_indices: Arc<Mutex<HashMap<(u64, Vec<u8>), usize>>>,
    token_balance_indices: Arc<Mutex<HashMap<(u64, Vec<u8>, Vec<u8>), usize>>>,
    min_batch_size: usize,
}

impl BalanceEventHandler {
    pub fn new(
        publisher: Arc<PubSubClient>,
        min_batch_size: usize,
        flush_interval: Duration,
    ) -> Self {
        let handler = Self {
            publisher,
            batch: Arc::new(Mutex::new(VecDeque::new())),
            sol_balance_indices: Arc::new(Mutex::new(HashMap::new())),
            token_balance_indices: Arc::new(Mutex::new(HashMap::new())),
            min_batch_size,
        };

        // Spawn background task to periodically flush batches
        let handler_clone = handler.clone();

        // The plugin's runtime will handle this task
        tokio::spawn(async move {
            let mut interval = interval(flush_interval);
            loop {
                interval.tick().await;
                if let Err(e) = handler_clone.flush_batch(false).await {
                    tracing::error!("Failed to flush batch: {:?}", e);
                }
            }
        });

        handler
    }

    pub async fn shutdown(&self) -> HandlerResult<()> {
        tracing::info!("Shutting down BalanceEventHandler, flushing remaining data...");

        // Keep flushing until batch is empty
        loop {
            let batch_len = {
                let batch = self.batch.lock().await;
                batch.len()
            };

            if batch_len == 0 {
                tracing::info!("All data flushed successfully");
                break;
            }

            tracing::info!("Flushing remaining {} events", batch_len);
            if let Err(e) = self.flush_batch(true).await {
                tracing::error!("Failed to flush batch during shutdown: {:?}", e);
                return Err(e);
            }

            // Small delay to prevent tight loop
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        Ok(())
    }

    async fn add_sol_to_batch(
        &self,
        slot: u64,
        account: Vec<u8>,
        amount: u64,
    ) -> HandlerResult<()> {
        let should_flush = {
            let mut batch = self.batch.lock().await;
            let mut sol_indices = self.sol_balance_indices.lock().await;

            let key = (slot, account);

            // O(1) lookup for existing event
            if let Some(&existing_index) = sol_indices.get(&key) {
                // Replace existing event with new one (which has updated amount)
                let event = SolanaEvent {
                    event: Some(event_proto::solana_event::Event::SolBalance(
                        SolBalanceEvent {
                            slot,
                            account: key.1.clone(),
                            amount,
                        },
                    )),
                };
                batch[existing_index] = event;
            } else {
                // No duplicate found, add new event
                let event = SolanaEvent {
                    event: Some(event_proto::solana_event::Event::SolBalance(
                        SolBalanceEvent {
                            slot,
                            account: key.1.clone(),
                            amount,
                        },
                    )),
                };
                let new_index = batch.len();
                batch.push_back(event);
                sol_indices.insert(key, new_index);
            }

            let len = batch.len();
            len >= MAX_BATCH_SIZE
        };

        if should_flush {
            self.flush_batch(true).await?;
        }
        Ok(())
    }

    async fn add_token_to_batch(
        &self,
        slot: u64,
        owner: Vec<u8>,
        token: Vec<u8>,
        amount: u64,
    ) -> HandlerResult<()> {
        let should_flush = {
            let mut batch = self.batch.lock().await;
            let mut token_indices = self.token_balance_indices.lock().await;

            let key = (slot, owner, token);

            // O(1) lookup for existing event
            if let Some(&existing_index) = token_indices.get(&key) {
                // Replace existing event with new one (which has updated amount)
                let event = SolanaEvent {
                    event: Some(event_proto::solana_event::Event::TokenBalance(
                        TokenBalanceEvent {
                            slot,
                            owner: key.1.clone(),
                            token: key.2.clone(),
                            amount,
                        },
                    )),
                };
                batch[existing_index] = event;
            } else {
                // No duplicate found, add new event
                let event = SolanaEvent {
                    event: Some(event_proto::solana_event::Event::TokenBalance(
                        TokenBalanceEvent {
                            slot,
                            owner: key.1.clone(),
                            token: key.2.clone(),
                            amount,
                        },
                    )),
                };
                let new_index = batch.len();
                batch.push_back(event);
                token_indices.insert(key, new_index);
            }

            let len = batch.len();
            len >= MAX_BATCH_SIZE
        };

        if should_flush {
            self.flush_batch(true).await?;
        }
        Ok(())
    }

    async fn flush_batch(&self, force: bool) -> HandlerResult<()> {
        let mut batch = self.batch.lock().await;
        let mut sol_indices = self.sol_balance_indices.lock().await;
        let mut token_indices = self.token_balance_indices.lock().await;

        if batch.len() < self.min_batch_size && !force {
            return Ok(());
        }

        let events: Vec<SolanaEvent> = batch.drain(..).collect();
        let batched_event = BatchedSolanaEvent { events };
        tracing::info!(
            "Flushing balance batch of {} events",
            batched_event.events.len()
        );

        // Clear the indices since we're flushing the batch
        sol_indices.clear();
        token_indices.clear();

        if !batched_event.events.is_empty() {
            tracing::debug!("Publishing to PubSub topic: solana-events");
            self.publisher
                .publish_proto("solana-events", &batched_event)
                .await?;
            tracing::debug!("Successfully published to PubSub");
        }
        Ok(())
    }
}

impl Handler<SolBalance> for BalanceEventHandler {
    async fn handle(&self, parsed: &SolBalance) -> HandlerResult<()> {
        self.add_sol_to_batch(parsed.slot, parsed.account.to_vec(), parsed.lamports)
            .await
    }
}

impl Handler<TokenProgramStateInfo> for BalanceEventHandler {
    async fn handle(&self, parsed: &TokenProgramStateInfo) -> HandlerResult<()> {
        match parsed.state {
            TokenProgramState::TokenAccount(account) => {
                self.add_token_to_batch(
                    parsed.slot,
                    account.owner.to_bytes().into(),
                    account.mint.to_bytes().into(),
                    account.amount,
                )
                .await
            }
            _ => Ok(()), // Only handle token accounts for balance tracking
        }
    }
}

impl Handler<TokenExtensionStateInfo> for BalanceEventHandler {
    async fn handle(&self, parsed: &TokenExtensionStateInfo) -> HandlerResult<()> {
        match &parsed.state {
            TokenExtensionState::ExtendedTokenAccount(ext_account) => {
                self.add_token_to_batch(
                    parsed.slot,
                    ext_account.base_account.owner.to_bytes().into(),
                    ext_account.base_account.mint.to_bytes().into(),
                    ext_account.base_account.amount,
                )
                .await
            }
            _ => Ok(()), // Only handle token accounts for balance tracking
        }
    }
}
