use common_program_parsers::system_program_parser::SolTransfer;
use common_program_parsers::{
    token_extension_program::{
        TokenExtensionProgramIx, TokenExtensionProgramIxInfo, TransferFeeIx,
    },
    token_program::{TokenProgramIx, TokenProgramIxInfo},
};
use yellowstone_vixen::{bs58, <PERSON><PERSON>, HandlerResult};

#[derive(<PERSON><PERSON>, Debug)]
pub struct LoggingHandler;

impl Default for LoggingHandler {
    fn default() -> Self {
        Self::new()
    }
}

impl LoggingHandler {
    pub fn new() -> Self {
        Self
    }
}

impl Handler<SolTransfer> for LoggingHandler {
    async fn handle(&self, parsed: &SolTransfer) -> HandlerResult<()> {
        tracing::info!(
            "Received SolTransfer event: signature={}, slot={}, ix_index={}, from={}, to={}, amount={}",
            bs58::encode(&parsed.signature).into_string(),
            parsed.slot,
            parsed.ix_index,
            parsed.from,
            parsed.to,
            parsed.lamports
        );
        Ok(())
    }
}

impl Handler<TokenProgramIxInfo> for LoggingHandler {
    async fn handle(&self, parsed: &TokenProgramIxInfo) -> HandlerResult<()> {
        match &parsed.ix {
            TokenProgramIx::TransferChecked(accounts, data) => {
                tracing::info!(
                    "Received TokenTransferChecked event: signature={}, slot={}, ix_index={}, mint={}, source={:?}, destination={:?}, amount={}",
                    bs58::encode(&parsed.signature).into_string(),
                    parsed.slot,
                    parsed.ix_index,
                    accounts.mint,
                    accounts.source_owner,
                    accounts.destination_owner,
                    data.amount
                );
            }
            TokenProgramIx::Transfer(accounts, data) => {
                tracing::info!(
                    "Received TokenTransfer event: signature={}, slot={}, ix_index={}, mint={:?}, source={:?}, destination={:?}, amount={}",
                    bs58::encode(&parsed.signature).into_string(),
                    parsed.slot,
                    parsed.ix_index,
                    data.token_mint,
                    accounts.source_owner,
                    accounts.destination_owner,
                    data.amount
                );
            }
            _ => {
                tracing::info!(
                    "Received other TokenProgram instruction: signature={}, slot={}, ix_index={}, ix={:?}",
                    bs58::encode(&parsed.signature).into_string(),
                    parsed.slot,
                    parsed.ix_index,
                    parsed.ix
                );
            }
        }
        Ok(())
    }
}

impl Handler<TokenExtensionProgramIxInfo> for LoggingHandler {
    async fn handle(&self, parsed: &TokenExtensionProgramIxInfo) -> HandlerResult<()> {
        match &parsed.ix {
            TokenExtensionProgramIx::TokenProgramIx(TokenProgramIx::TransferChecked(
                accounts,
                data,
            )) => {
                tracing::info!(
                    "Received TokenExtension TransferChecked event: signature={}, slot={}, ix_index={}, mint={}, source={:?}, destination={:?}, amount={}",
                    bs58::encode(&parsed.signature).into_string(),
                    parsed.slot,
                    parsed.ix_index,
                    accounts.mint,
                    accounts.source_owner,
                    accounts.destination_owner,
                    data.amount
                );
            }
            TokenExtensionProgramIx::TransferFeeIx(TransferFeeIx::TransferCheckedWithFee(
                accounts,
                data,
            )) => {
                tracing::info!(
                    "Received TokenExtension TransferCheckedWithFee event: signature={}, slot={}, ix_index={}, mint={}, source={:?}, destination={:?}, amount={}",
                    bs58::encode(&parsed.signature).into_string(),
                    parsed.slot,
                    parsed.ix_index,
                    accounts.mint,
                    accounts.source_owner,
                    accounts.destination_owner,
                    data.amount
                );
            }
            _ => {
                tracing::info!(
                    "Received other TokenExtension instruction: signature={}, slot={}, ix_index={}, ix={:?}",
                    bs58::encode(&parsed.signature).into_string(),
                    parsed.slot,
                    parsed.ix_index,
                    parsed.ix
                );
            }
        }
        Ok(())
    }
}
