use crate::db_writer::{Sol<PERSON><PERSON><PERSON>ferRow, TokenTransferRow};
use crate::slot_manager::SlotManager;
use crate::transfer_buffer::TransferBuffer;
use common_program_parsers::{
    system_program_parser::SolTransfer, token_extension_program::TokenExtensionProgramIxInfo,
    token_program::TokenProgramIxInfo, SlotTime,
};
use rust_decimal::Decimal;
use std::sync::Arc;
use yellowstone_vixen::{Handler, HandlerResult};

/// Main database event handler that coordinates all data processing
#[derive(Clone, Debug)]
pub struct DbEventHandler {
    slot_manager: Arc<SlotManager>,
    transfer_buffer: Arc<TransferBuffer>,
}

impl DbEventHandler {
    pub fn new(db_writer: Arc<crate::db_writer::DbWriter>) -> Self {
        let slot_manager = Arc::new(SlotManager::new(db_writer.clone()));
        let transfer_buffer = Arc::new(TransferBuffer::new(db_writer.clone()));

        Self {
            slot_manager,
            transfer_buffer,
        }
    }

    pub async fn flush_all(&self) -> HandlerResult<()> {
        // Flush all buffers in parallel for better performance
        let (transfer_result, slot_manager_result) = tokio::join!(
            self.transfer_buffer.flush_all(),
            self.slot_manager.flush_all()
        );
        transfer_result?;
        slot_manager_result?;

        Ok(())
    }
}

impl Handler<SolTransfer> for DbEventHandler {
    async fn handle(&self, parsed: &SolTransfer) -> HandlerResult<()> {
        // Add transaction signature first
        let amount = Decimal::from(parsed.lamports);

        let row = SolTransferRow {
            slot: parsed.slot as i64,
            tx_index: parsed.tx_index as i32,
            ix_index: parsed.ix_index as i16,
            from_wallet: parsed.from.0.to_vec(),
            to_wallet: parsed.to.0.to_vec(),
            amount,
        };

        // Run operations in parallel for better performance
        let (signature_result, transfer_result, from_balance_result, to_balance_result) = tokio::join!(
            self.slot_manager.add_transaction_signature(
                parsed.signature.clone(),
                parsed.slot,
                parsed.tx_index
            ),
            self.transfer_buffer.add_sol_transfer(row),
            self.slot_manager.add_sol_balance(
                parsed.slot,
                parsed.tx_index,
                parsed.from.0.to_vec(),
                Decimal::from(parsed.from_post_balance),
            ),
            self.slot_manager.add_sol_balance(
                parsed.slot,
                parsed.tx_index,
                parsed.to.0.to_vec(),
                Decimal::from(parsed.to_post_balance),
            )
        );

        signature_result?;
        transfer_result?;
        from_balance_result?;
        to_balance_result?;

        Ok(())
    }
}

impl Handler<TokenProgramIxInfo> for DbEventHandler {
    async fn handle(&self, parsed: &TokenProgramIxInfo) -> HandlerResult<()> {
        use common_program_parsers::token_program::TokenProgramIx;
        let (token_mint, from_wallet, to_wallet, amount, from_post_balance, to_post_balance) =
            match &parsed.ix {
                TokenProgramIx::TransferChecked(accounts, data) => (
                    accounts.mint,
                    accounts.source_owner.as_ref(),
                    accounts.destination_owner.as_ref(),
                    Decimal::from(data.amount),
                    Decimal::from(data.source_post_balance),
                    Decimal::from(data.dest_post_balance),
                ),
                TokenProgramIx::Transfer(accounts, data) => (
                    data.token_mint,
                    accounts.source_owner.as_ref(),
                    accounts.destination_owner.as_ref(),
                    Decimal::from(data.amount),
                    Decimal::from(data.source_post_balance),
                    Decimal::from(data.dest_post_balance),
                ),
                _ => return Ok(()),
            };
        let (from_wallet, to_wallet) = match (from_wallet, to_wallet) {
            (Some(from), Some(to)) => (from, to),
            _ => return Ok(()),
        };

        // Convert PubKey to bytes directly
        let from_bytes = from_wallet.to_vec();
        let to_bytes = to_wallet.to_vec();

        let row = TokenTransferRow {
            slot: parsed.slot as i64,
            tx_index: parsed.tx_index as i32,
            ix_index: parsed.ix_index as i16,
            token_mint: token_mint.to_vec(),
            from_wallet: from_bytes.clone(),
            to_wallet: to_bytes.clone(),
            amount,
        };

        // Run operations in parallel for better performance
        let (signature_result, transfer_result, from_balance_result, to_balance_result) = tokio::join!(
            self.slot_manager.add_transaction_signature(
                parsed.signature.clone(),
                parsed.slot,
                parsed.tx_index
            ),
            self.transfer_buffer.add_token_transfer(row),
            self.slot_manager.add_token_balance(
                parsed.slot,
                from_bytes,
                token_mint.to_vec(),
                from_post_balance,
                parsed.tx_index,
            ),
            self.slot_manager.add_token_balance(
                parsed.slot,
                to_bytes,
                token_mint.to_vec(),
                to_post_balance,
                parsed.tx_index,
            )
        );

        signature_result?;
        transfer_result?;
        from_balance_result?;
        to_balance_result?;

        Ok(())
    }
}

impl Handler<TokenExtensionProgramIxInfo> for DbEventHandler {
    async fn handle(&self, parsed: &TokenExtensionProgramIxInfo) -> HandlerResult<()> {
        use common_program_parsers::token_extension_program::TokenExtensionProgramIx;
        use common_program_parsers::token_extension_program::TransferFeeIx;
        let (token_mint, from_wallet, to_wallet, amount, from_post_balance, to_post_balance) =
            match &parsed.ix {
                TokenExtensionProgramIx::TokenProgramIx(inner) => match inner {
                    common_program_parsers::token_program::TokenProgramIx::TransferChecked(
                        accounts,
                        data,
                    ) => (
                        accounts.mint,
                        accounts.source_owner.as_ref(),
                        accounts.destination_owner.as_ref(),
                        Decimal::from(data.amount),
                        Decimal::from(data.source_post_balance),
                        Decimal::from(data.dest_post_balance),
                    ),
                    _ => return Ok(()),
                },
                TokenExtensionProgramIx::TransferFeeIx(TransferFeeIx::TransferCheckedWithFee(
                    accounts,
                    data,
                )) => (
                    accounts.mint,
                    accounts.source_owner.as_ref(),
                    accounts.destination_owner.as_ref(),
                    Decimal::from(data.amount),
                    Decimal::from(data.source_post_balance),
                    Decimal::from(data.dest_post_balance),
                ),
                _ => return Ok(()),
            };
        let (from_wallet, to_wallet) = match (from_wallet, to_wallet) {
            (Some(from), Some(to)) => (from, to),
            _ => return Ok(()),
        };

        // Convert PubKey to bytes directly
        let from_bytes = from_wallet.to_vec();
        let to_bytes = to_wallet.to_vec();

        let row = TokenTransferRow {
            slot: parsed.slot as i64,
            tx_index: parsed.tx_index as i32,
            ix_index: parsed.ix_index as i16,
            token_mint: token_mint.to_vec(),
            from_wallet: from_bytes.clone(),
            to_wallet: to_bytes.clone(),
            amount,
        };

        // Run operations in parallel for better performance
        let (signature_result, transfer_result, from_balance_result, to_balance_result) = tokio::join!(
            self.slot_manager.add_transaction_signature(
                parsed.signature.clone(),
                parsed.slot,
                parsed.tx_index
            ),
            self.transfer_buffer.add_token_transfer(row),
            self.slot_manager.add_token_balance(
                parsed.slot,
                from_bytes,
                token_mint.to_vec(),
                from_post_balance,
                parsed.tx_index,
            ),
            self.slot_manager.add_token_balance(
                parsed.slot,
                to_bytes,
                token_mint.to_vec(),
                to_post_balance,
                parsed.tx_index,
            )
        );

        signature_result?;
        transfer_result?;
        from_balance_result?;
        to_balance_result?;

        Ok(())
    }
}

impl Handler<SlotTime> for DbEventHandler {
    async fn handle(&self, parsed: &SlotTime) -> HandlerResult<()> {
        self.slot_manager
            .add_slot_time(parsed.slot, parsed.timestamp as i64)
            .await?;

        // Update slot and trigger flushing
        self.slot_manager.check_and_flush_slots(parsed.slot).await
    }
}
