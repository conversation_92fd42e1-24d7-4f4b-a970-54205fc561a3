use async_trait::async_trait;
use rust_decimal::Decimal;
use std::pin::pin;
use std::sync::Arc;
use tokio_postgres::binary_copy::BinaryCopyInWriter;
use tokio_postgres::types::{ToSql, Type};
use tokio_postgres::Client;
use tokio_postgres_rustls::MakeRustlsConnect;

use crate::tls_config::SkipServerVerification;

#[derive(Debug, Clone)]
pub struct DbWriter {
    pub client: Arc<Client>,
    pub epoch: u32,
    pub slot_start: i64,
    pub slot_end: i64,
}

#[derive(Debug)]
pub struct SolTransferRow {
    pub slot: i64,
    pub tx_index: i32,
    pub ix_index: i16,
    pub from_wallet: Vec<u8>,
    pub to_wallet: Vec<u8>,
    pub amount: Decimal,
}

#[derive(Debug)]
pub struct TokenTransferRow {
    pub slot: i64,
    pub tx_index: i32,
    pub ix_index: i16,
    pub token_mint: Vec<u8>,
    pub from_wallet: Vec<u8>,
    pub to_wallet: Vec<u8>,
    pub amount: Decimal,
}

#[derive(Debug)]
pub struct SlotTimeRow {
    pub slot: i64,
    pub timestamp: i64,
}

#[derive(Debug, Clone)]
pub struct TransactionSignatureRow {
    pub slot: i64,
    pub tx_index: i32,
    pub signature: Vec<u8>,
}

#[derive(Debug, Clone)]
pub struct SolBalanceRow {
    pub slot: i64,
    pub account: Vec<u8>,
    pub amount: Decimal, // NUMERIC(20,0) as Decimal
}

#[derive(Debug, Clone)]
pub struct TokenBalanceRow {
    pub slot: i64,
    pub owner: Vec<u8>,
    pub token: Vec<u8>,
    pub amount: Decimal, // NUMERIC(20,0) as Decimal
}

/// A trait to abstract over the different row types that are partitioned by slot.
/// This allows us to use a single generic function for the COPY operation.
#[async_trait]
pub trait PartitionedRow {
    /// Returns the slot number, which is used for partitioning.
    fn slot(&self) -> i64;

    /// Returns the base name of the database table.
    fn table_name() -> &'static str;

    /// Returns the comma-separated list of column names for the COPY statement.
    fn column_list() -> &'static str;

    /// Returns the slice of `tokio_postgres::types::Type` for the binary copy.
    fn types() -> &'static [Type];

    /// Writes the row's data to the binary copy writer.
    /// This is an async method because it involves I/O.
    async fn write_to(
        &self,
        writer: &mut std::pin::Pin<&mut BinaryCopyInWriter>,
    ) -> Result<(), tokio_postgres::Error>;
}

#[async_trait]
impl PartitionedRow for SolTransferRow {
    fn slot(&self) -> i64 {
        self.slot
    }
    fn table_name() -> &'static str {
        "sol_transfer_events"
    }
    fn column_list() -> &'static str {
        "slot, tx_index, ix_index, from_wallet, to_wallet, amount"
    }
    fn types() -> &'static [Type] {
        &[
            Type::INT8,
            Type::INT4,
            Type::INT2,
            Type::BYTEA,
            Type::BYTEA,
            Type::NUMERIC,
        ]
    }
    async fn write_to(
        &self,
        writer: &mut std::pin::Pin<&mut BinaryCopyInWriter>,
    ) -> Result<(), tokio_postgres::Error> {
        let values: [&(dyn ToSql + Sync); 6] = [
            &self.slot,
            &self.tx_index,
            &self.ix_index,
            &self.from_wallet,
            &self.to_wallet,
            &self.amount,
        ];
        writer.as_mut().write(&values).await
    }
}

#[async_trait]
impl PartitionedRow for TokenTransferRow {
    fn slot(&self) -> i64 {
        self.slot
    }
    fn table_name() -> &'static str {
        "token_transfer_events"
    }
    fn column_list() -> &'static str {
        "slot, tx_index, ix_index, token_mint, from_wallet, to_wallet, amount"
    }
    fn types() -> &'static [Type] {
        &[
            Type::INT8,
            Type::INT4,
            Type::INT2,
            Type::BYTEA,
            Type::BYTEA,
            Type::BYTEA,
            Type::NUMERIC,
        ]
    }
    async fn write_to(
        &self,
        writer: &mut std::pin::Pin<&mut BinaryCopyInWriter>,
    ) -> Result<(), tokio_postgres::Error> {
        let values: [&(dyn ToSql + Sync); 7] = [
            &self.slot,
            &self.tx_index,
            &self.ix_index,
            &self.token_mint,
            &self.from_wallet,
            &self.to_wallet,
            &self.amount,
        ];
        writer.as_mut().write(&values).await
    }
}

#[async_trait]
impl PartitionedRow for SolBalanceRow {
    fn slot(&self) -> i64 {
        self.slot
    }
    fn table_name() -> &'static str {
        "sol_balances"
    }
    fn column_list() -> &'static str {
        "slot, account, amount"
    }
    fn types() -> &'static [Type] {
        &[Type::INT8, Type::BYTEA, Type::NUMERIC]
    }
    async fn write_to(
        &self,
        writer: &mut std::pin::Pin<&mut BinaryCopyInWriter>,
    ) -> Result<(), tokio_postgres::Error> {
        let values: [&(dyn ToSql + Sync); 3] = [&self.slot, &self.account, &self.amount];
        writer.as_mut().write(&values).await
    }
}

#[async_trait]
impl PartitionedRow for TokenBalanceRow {
    fn slot(&self) -> i64 {
        self.slot
    }
    fn table_name() -> &'static str {
        "token_balances"
    }
    fn column_list() -> &'static str {
        "slot, owner, token, amount"
    }
    fn types() -> &'static [Type] {
        &[Type::INT8, Type::BYTEA, Type::BYTEA, Type::NUMERIC]
    }
    async fn write_to(
        &self,
        writer: &mut std::pin::Pin<&mut BinaryCopyInWriter>,
    ) -> Result<(), tokio_postgres::Error> {
        let values: [&(dyn ToSql + Sync); 4] = [&self.slot, &self.owner, &self.token, &self.amount];
        writer.as_mut().write(&values).await
    }
}

#[async_trait]
impl PartitionedRow for TransactionSignatureRow {
    fn slot(&self) -> i64 {
        self.slot
    }
    fn table_name() -> &'static str {
        "transaction_signatures"
    }
    fn column_list() -> &'static str {
        "slot, tx_index, signature"
    }
    fn types() -> &'static [Type] {
        &[Type::INT8, Type::INT4, Type::BYTEA]
    }
    async fn write_to(
        &self,
        writer: &mut std::pin::Pin<&mut BinaryCopyInWriter>,
    ) -> Result<(), tokio_postgres::Error> {
        let values: [&(dyn ToSql + Sync); 3] = [&self.slot, &self.tx_index, &self.signature];
        writer.as_mut().write(&values).await
    }
}

impl DbWriter {
    pub async fn new(
        host: &str,
        port: u16,
        user: &str,
        password: &str,
        database: &str,
        sslmode: &str,
        epoch: u32,
    ) -> anyhow::Result<Self> {
        // Configure TLS with custom verifier that skips certificate verification
        let config = rustls::ClientConfig::builder()
            .dangerous()
            .with_custom_certificate_verifier(SkipServerVerification::new())
            .with_no_client_auth();

        let tls = MakeRustlsConnect::new(config);

        let (client, connection) = tokio_postgres::connect(
            &format!(
                "host={} port={} user={} password={} dbname={} sslmode={}",
                host, port, user, password, database, sslmode
            ),
            tls,
        )
        .await?;
        tokio::spawn(connection);

        let slot_start = 432_000 * (epoch as i64);
        let slot_end = 432_000 * ((epoch + 1) as i64);

        // Calculate partitions: epoch n corresponds to partition 2n and 2n+1
        let partition1 = 2 * epoch;
        let partition2 = 2 * epoch + 1;

        // Clean up old data in partitioned tables using for loop
        let partitioned_tables = [
            "sol_transfer_events",
            "token_transfer_events",
            "sol_balances",
            "token_balances",
            "transaction_signatures",
        ];
        let partitions = [partition1, partition2];
        for table in partitioned_tables {
            for partition in partitions {
                client
                    .execute(&format!("TRUNCATE {}_p{}", table, partition), &[])
                    .await?;
            }
        }

        // Delete by slot range for non-partitioned tables
        client
            .execute(
                "DELETE FROM slot_times WHERE slot >= $1 AND slot < $2",
                &[&slot_start, &slot_end],
            )
            .await?;

        Ok(Self {
            client: Arc::new(client),
            epoch,
            slot_start,
            slot_end,
        })
    }

    /// Helper function to determine which partition to use based on slot.
    fn get_partition_for_slot(&self, slot: i64) -> u32 {
        (slot / 216_000) as u32
    }

    /// A generic, reusable function to perform a binary COPY for any type
    /// that implements `PartitionedRow` into a specific partition table.
    async fn copy_to_partition_binary<R: PartitionedRow + Sync>(
        &self,
        rows: &[&R],
        partition: u32,
    ) -> anyhow::Result<()> {
        let stmt = format!(
            "COPY {}_p{} ({}) FROM STDIN WITH (FORMAT binary)",
            R::table_name(),
            partition,
            R::column_list()
        );
        let sink = self.client.copy_in(stmt.as_str()).await?;
        let writer = BinaryCopyInWriter::new(sink, R::types());
        let mut writer = pin!(writer);

        for &row in rows {
            // The trait implementation now handles writing the row
            row.write_to(&mut writer).await?;
        }

        writer.finish().await?;
        Ok(())
    }

    /// A generic, public-facing function that takes a slice of rows,
    /// groups them by partition, and then uses the generic helper to write them.
    pub async fn copy_partitioned_rows_binary<R>(&self, rows: &[R]) -> anyhow::Result<()>
    where
        R: PartitionedRow + Sync,
    {
        if rows.is_empty() {
            return Ok(());
        }

        let mut partition1_rows = Vec::new();
        let mut partition2_rows = Vec::new();

        let p1_num = 2 * self.epoch;

        // Group rows by partition using the common logic
        for row in rows {
            if self.get_partition_for_slot(row.slot()) == p1_num {
                partition1_rows.push(row);
            } else {
                partition2_rows.push(row);
            }
        }

        // Write to both partitions if they have data
        if !partition1_rows.is_empty() {
            self.copy_to_partition_binary(&partition1_rows, p1_num)
                .await?;
        }
        if !partition2_rows.is_empty() {
            let p2_num = 2 * self.epoch + 1;
            self.copy_to_partition_binary(&partition2_rows, p2_num)
                .await?;
        }

        Ok(())
    }

    // The old, specific functions are now simple one-line wrappers.
    pub async fn copy_sol_transfers_binary(&self, rows: &[SolTransferRow]) -> anyhow::Result<()> {
        self.copy_partitioned_rows_binary(rows).await
    }

    pub async fn copy_token_transfers_binary(
        &self,
        rows: &[TokenTransferRow],
    ) -> anyhow::Result<()> {
        self.copy_partitioned_rows_binary(rows).await
    }

    pub async fn copy_sol_balances_binary(&self, rows: &[SolBalanceRow]) -> anyhow::Result<()> {
        self.copy_partitioned_rows_binary(rows).await
    }

    pub async fn copy_token_balances_binary(&self, rows: &[TokenBalanceRow]) -> anyhow::Result<()> {
        self.copy_partitioned_rows_binary(rows).await
    }

    // Non-partitioned copy functions remain unchanged
    pub async fn copy_slot_times_binary(&self, rows: &[SlotTimeRow]) -> anyhow::Result<()> {
        if rows.is_empty() {
            return Ok(());
        }

        let stmt = "COPY slot_times (slot, timestamp) FROM STDIN WITH (FORMAT binary)";
        let sink = self.client.copy_in(stmt).await?;
        let types = &[
            Type::INT8, // slot
            Type::INT8, // timestamp
        ];
        let mut writer = BinaryCopyInWriter::new(sink, types);
        let mut writer = pin!(writer);
        for row in rows {
            let values: [&(dyn ToSql + Sync); 2] = [&row.slot, &row.timestamp];
            writer.as_mut().write(&values).await?;
        }

        writer.finish().await?;
        Ok(())
    }

    pub async fn copy_transaction_signatures_binary(
        &self,
        rows: &[TransactionSignatureRow],
    ) -> anyhow::Result<()> {
        self.copy_partitioned_rows_binary(rows).await
    }
}
