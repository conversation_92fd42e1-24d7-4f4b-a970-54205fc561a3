[package]
name = "event-handlers"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio.workspace = true
tracing.workspace = true
google-cloud-pubsub.workspace = true
google-cloud-googleapis.workspace = true
prost.workspace = true
thiserror.workspace = true
common-program-parsers = { workspace = true }
event-proto = { workspace = true }
yellowstone-vixen = { workspace = true }
anyhow = { workspace = true }
tokio-postgres = "0.7.13"
tokio-postgres-rustls.workspace = true
rustls.workspace = true
rustls-pki-types.workspace = true
async-trait = "0.1"
rust_decimal = { version = "1.32", features = ["serde-with-float", "db-tokio-postgres"] }
