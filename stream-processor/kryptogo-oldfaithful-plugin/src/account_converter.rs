use agave_geyser_plugin_interface::geyser_plugin_interface::{
    GeyserPluginError, ReplicaAccountInfoVersions,
};
use yellowstone_grpc_proto::geyser::{SubscribeUpdateAccount, SubscribeUpdateAccountInfo};

pub struct AccountConverter;

impl AccountConverter {
    /// Convert ReplicaAccountInfoV3 to SubscribeUpdateAccount
    pub fn convert_account_info_v3_to_subscribe_update(
        account_info: &agave_geyser_plugin_interface::geyser_plugin_interface::ReplicaAccountInfoV3,
        slot: u64,
        is_startup: bool,
    ) -> SubscribeUpdateAccount {
        let account_info_inner = SubscribeUpdateAccountInfo {
            pubkey: account_info.pubkey.to_vec(),
            lamports: account_info.lamports,
            owner: account_info.owner.to_vec(),
            executable: account_info.executable,
            rent_epoch: account_info.rent_epoch,
            data: account_info.data.to_vec(),
            write_version: account_info.write_version,
            txn_signature: None,
        };

        SubscribeUpdateAccount {
            account: Some(account_info_inner),
            slot,
            is_startup,
        }
    }

    /// Convert ReplicaAccountInfoVersions to SubscribeUpdateAccount with version checking
    pub fn convert_account_versions_to_subscribe_update(
        account: ReplicaAccountInfoVersions,
        slot: u64,
        is_startup: bool,
    ) -> Result<SubscribeUpdateAccount, GeyserPluginError> {
        // Only handle V0_0_3 for now
        let account_info = match account {
            ReplicaAccountInfoVersions::V0_0_3(info) => info,
            _ => {
                tracing::error!(
                    "___ Unsupported ReplicaAccountInfoVersions: {:?}",
                    std::mem::discriminant(&account)
                );
                return Err(GeyserPluginError::Custom(Box::new(std::io::Error::new(
                    std::io::ErrorKind::Unsupported,
                    "Unsupported ReplicaAccountInfoVersions",
                ))));
            }
        };

        Ok(Self::convert_account_info_v3_to_subscribe_update(
            account_info,
            slot,
            is_startup,
        ))
    }
}
