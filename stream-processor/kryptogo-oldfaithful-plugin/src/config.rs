use agave_geyser_plugin_interface::geyser_plugin_interface::GeyserPluginError;
use serde::Deserialize;
use std::fs;

#[derive(Debug, Deserialize, Clone)]
pub struct Config {
    pub epoch: u32,
    pub log: LogConfig,
    pub tokio: TokioConfig,
    pub db: DbConfig,
}

#[derive(Debug, Deserialize, Clone)]
pub struct LogConfig {
    pub level: String,
}

#[derive(Debug, Deserialize, Clone)]
pub struct TokioConfig {
    pub worker_threads: Option<usize>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct DbConfig {
    pub host: String,
    pub port: u16,
    pub user: String,
    pub password: String,
    pub database: String,
    pub sslmode: String,
}

impl Config {
    pub fn load_from_file(config_file: &str) -> Result<Self, GeyserPluginError> {
        let data = fs::read_to_string(config_file)
            .map_err(|e| GeyserPluginError::ConfigFileReadError { msg: e.to_string() })?;
        serde_json::from_str(&data).map_err(|e| GeyserPluginError::Custom(Box::new(e)))
    }
}
