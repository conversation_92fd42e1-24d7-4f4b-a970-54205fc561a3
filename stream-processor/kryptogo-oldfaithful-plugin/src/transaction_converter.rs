use agave_geyser_plugin_interface::geyser_plugin_interface::{
    GeyserPluginError, ReplicaTransactionInfoVersions,
};
use anyhow::Result;
use yellowstone_grpc_proto::{
    plugin::message::MessageTransaction, prelude::SubscribeUpdateTransactionInfo,
};

pub struct MessageTransactionConverter;

impl MessageTransactionConverter {
    /// Convert MessageTransaction to yellowstone_vixen_core::TransactionUpdate
    pub fn convert_to_transaction_update(
        msg_tx: &MessageTransaction,
    ) -> Result<yellowstone_vixen_core::TransactionUpdate> {
        let mut tx_info = SubscribeUpdateTransactionInfo::default();
        tx_info.index = msg_tx.transaction.index as u64;
        tx_info.signature = msg_tx.transaction.signature.as_ref().to_vec();
        tx_info.is_vote = msg_tx.transaction.is_vote;

        // Set transaction from the confirmed_block::Transaction
        tx_info.transaction = Some(msg_tx.transaction.transaction.clone());

        // Set meta from the confirmed_block::TransactionStatusMeta
        tx_info.meta = Some(msg_tx.transaction.meta.clone());

        let mut tx_update = yellowstone_vixen_core::TransactionUpdate::default();
        tx_update.slot = msg_tx.slot;
        tx_update.transaction = Some(tx_info);

        Ok(tx_update)
    }

    /// Convert ReplicaTransactionInfoVersions to MessageTransaction with version checking
    pub fn convert_transaction_versions_to_message_transaction(
        transaction: ReplicaTransactionInfoVersions<'_>,
        slot: u64,
    ) -> Result<MessageTransaction, GeyserPluginError> {
        let transaction_info = match transaction {
            ReplicaTransactionInfoVersions::V0_0_1(_info) => {
                tracing::error!("___ ReplicaTransactionInfoVersions::V0_0_1 is not supported");
                return Err(GeyserPluginError::Custom(Box::new(std::io::Error::new(
                    std::io::ErrorKind::Unsupported,
                    "ReplicaTransactionInfoVersions::V0_0_1 is not supported",
                ))));
            }
            ReplicaTransactionInfoVersions::V0_0_2(info) => info,
        };

        let message_tx = MessageTransaction::from_geyser(transaction_info, slot);
        Ok(message_tx)
    }

    /// Convert multiple MessageTransactions to TransactionUpdates in batch
    pub fn convert_batch_to_transaction_updates(
        msg_txs: &[MessageTransaction],
    ) -> Vec<yellowstone_vixen_core::TransactionUpdate> {
        msg_txs
            .iter()
            .filter_map(|msg_tx| match Self::convert_to_transaction_update(msg_tx) {
                Ok(tx_update) => Some(tx_update),
                Err(e) => {
                    tracing::error!(
                        "Failed to convert MessageTransaction to TransactionUpdate: {:?}",
                        e
                    );
                    None
                }
            })
            .collect()
    }
}
