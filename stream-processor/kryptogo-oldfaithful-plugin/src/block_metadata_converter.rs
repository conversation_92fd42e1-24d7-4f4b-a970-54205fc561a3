use agave_geyser_plugin_interface::geyser_plugin_interface::{
    GeyserPluginError, ReplicaBlockInfoVersions,
};
use yellowstone_grpc_proto::{
    geyser::SubscribeUpdateBlockMeta,
    prelude::{BlockHeight, UnixTimestamp},
};

pub struct BlockMetadataConverter;

impl BlockMetadataConverter {
    /// Convert ReplicaBlockInfoVersions to SubscribeUpdateBlockMeta with version checking
    pub fn convert_block_info_versions_to_subscribe_update(
        block_info: ReplicaBlockInfoVersions<'_>,
    ) -> Result<SubscribeUpdateBlockMeta, GeyserPluginError> {
        let block = match block_info {
            ReplicaBlockInfoVersions::V0_0_4(info) => info,
            _ => {
                tracing::error!(
                    "___ Unsupported ReplicaBlockInfoVersions: {:?}",
                    std::mem::discriminant(&block_info)
                );
                return Err(GeyserPluginError::Custom(Box::new(std::io::Error::new(
                    std::io::ErrorKind::Unsupported,
                    "Unsupported ReplicaBlockInfoVersions",
                ))));
            }
        };

        let block_meta_update = SubscribeUpdateBlockMeta {
            slot: block.slot,
            block_time: Some(UnixTimestamp {
                timestamp: block.block_time.unwrap(),
            }),
            blockhash: block.blockhash.to_string(),
            block_height: Some(BlockHeight {
                block_height: block.block_height.unwrap(),
            }),
            rewards: None, // ignore for now
            parent_slot: block.parent_slot,
            parent_blockhash: block.parent_blockhash.to_string(),
            executed_transaction_count: block.executed_transaction_count,
            entries_count: 0, // ignore for now
        };

        Ok(block_meta_update)
    }
}
