use {
    crate::Config,
    agave_geyser_plugin_interface::geyser_plugin_interface::{
        GeyserPlugin, GeyserPluginError, ReplicaAccountInfoVersions, ReplicaBlockInfoVersions,
        ReplicaEntryInfoVersions, ReplicaTransactionInfoVersions, Result as PluginR<PERSON>ult,
        SlotStatus,
    },
    common_program_parsers::{
        system_program_parser::SystemProgramParser,
        token_extension_program::InstructionParser as TokenExtensionIxParser,
        token_program::InstructionParser as TokenIxParser, BlockMetaParser,
    },
    event_handlers::{db_event_handler::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, db_writer::DbWrite<PERSON>},
    rayon::prelude::*,
    rustls::crypto::CryptoProvider,
    std::{env, fmt::Debug, sync::Arc},
    tokio::runtime::{Builder, Runtime},
    yellowstone_vixen::{
        handler::Dyn<PERSON><PERSON>eline, instruction::InstructionPipeline, metrics::NullMetrics, Pipeline,
    },
    yellowstone_vixen_core::{instruction::InstructionUpdate, BlockMetaUpdate},
};

use crate::block_metadata_converter::BlockMetadataConverter;
use crate::transaction_converter::MessageTransactionConverter;

// Global plugin instance
static mut GLOBAL_PLUGIN: Option<KryptogoOldFaithfulPlugin> = None;

type BoxPipeline<'h, T> = Box<dyn DynPipeline<T> + Send + Sync + 'h>;

pub fn get_thread_name() -> String {
    format!("plugin-{:?}", std::thread::current().id())
}

#[derive(Debug)]
pub struct KryptogoOldFaithfulPlugin {
    pipeline: InstructionPipeline<NullMetrics>,
    block_meta_pipeline: Vec<BoxPipeline<'static, BlockMetaUpdate>>,
    runtime: Runtime,
    db_handler: DbEventHandler, // Keep reference for cleanup
}

impl KryptogoOldFaithfulPlugin {
    fn new(config: &Config) -> Result<Self, GeyserPluginError> {
        // Install the default crypto provider for rustls
        CryptoProvider::install_default(rustls::crypto::ring::default_provider())
            .expect("Failed to install crypto provider");

        let runtime = Builder::new_multi_thread()
            .enable_all()
            .thread_name("plugin-worker")
            .build()
            .map_err(|e| GeyserPluginError::Custom(Box::new(e)))?;

        // Use DB event handler
        let db_writer = runtime
            .block_on(DbWriter::new(
                &config.db.host,
                config.db.port,
                &config.db.user,
                &config.db.password,
                &config.db.database,
                &config.db.sslmode,
                config.epoch,
            ))
            .map_err(|e| {
                GeyserPluginError::Custom(Box::new(std::io::Error::other(e.to_string())))
            })?;
        let db_writer = Arc::new(db_writer);

        // Only binary format is supported now
        let db_handler = DbEventHandler::new(db_writer);

        let ix_pipelines: Vec<BoxPipeline<'static, InstructionUpdate>> = vec![
            Box::new(Pipeline::new(SystemProgramParser, [db_handler.clone()])),
            Box::new(Pipeline::new(TokenIxParser, [db_handler.clone()])),
            Box::new(Pipeline::new(TokenExtensionIxParser, [db_handler.clone()])),
        ];

        let block_meta_pipeline: Vec<BoxPipeline<'static, BlockMetaUpdate>> = vec![Box::new(
            Pipeline::new(BlockMetaParser, [db_handler.clone()]),
        )];

        let pipeline = InstructionPipeline::new(ix_pipelines, &NullMetrics).ok_or_else(|| {
            GeyserPluginError::Custom(Box::new(std::io::Error::other("Failed to create pipeline")))
        })?;

        Ok(Self {
            pipeline,
            block_meta_pipeline,
            runtime,
            db_handler,
        })
    }

    // Helper method to run async operations within the plugin's runtime
    pub fn run_async<F, T>(&self, future: F) -> Result<T, GeyserPluginError>
    where
        F: std::future::Future<Output = Result<T, GeyserPluginError>> + Send + 'static,
        T: Send + 'static,
    {
        self.runtime.block_on(future)
    }

    // Graceful shutdown - flush all pending data
    pub async fn shutdown(&self) -> Result<(), GeyserPluginError> {
        log::info!("Performing graceful shutdown, flushing pending data...");
        self.db_handler.flush_all().await.map_err(|e| {
            GeyserPluginError::Custom(Box::new(std::io::Error::other(format!(
                "Failed to flush data during shutdown: {:?}",
                e
            ))))
        })?;
        log::info!("Graceful shutdown completed");
        Ok(())
    }
}

#[derive(Debug, Default)]
pub struct Plugin {}

impl GeyserPlugin for Plugin {
    fn name(&self) -> &'static str {
        "kryptogo-oldfaithful-plugin"
    }

    fn on_load(&mut self, config_file: &str, _is_reload: bool) -> PluginResult<()> {
        tracing_subscriber::fmt::init();

        let config = Config::load_from_file(config_file)?;

        let log_filter = config.log.level.as_str();
        env::set_var("RUST_LOG", log_filter);

        let mut builder = Builder::new_multi_thread();
        builder.enable_all().thread_name_fn(get_thread_name);

        if let Some(worker_threads) = config.tokio.worker_threads {
            builder.worker_threads(worker_threads);
        }

        let inner = KryptogoOldFaithfulPlugin::new(&config)?;

        // Initialize the global plugin
        unsafe {
            GLOBAL_PLUGIN = Some(inner);
        }

        log::info!("Plugin loaded successfully with pipeline support");
        Ok(())
    }

    fn on_unload(&mut self) {
        log::info!("Unloading plugin...");
        // Perform graceful shutdown
        unsafe {
            #[allow(static_mut_refs)]
            if let Some(plugin) = GLOBAL_PLUGIN.as_ref() {
                if let Err(e) = plugin.run_async(async move {
                    #[allow(static_mut_refs)]
                    GLOBAL_PLUGIN
                        .as_ref()
                        .expect("Global plugin not initialized")
                        .shutdown()
                        .await
                }) {
                    log::error!("Error during plugin shutdown: {:?}", e);
                } else {
                    log::info!("Plugin shutdown completed successfully");
                }
            }
        }
    }

    fn update_account(
        &self,
        _account: ReplicaAccountInfoVersions,
        _slot: u64,
        _is_startup: bool,
    ) -> PluginResult<()> {
        Ok(())
    }

    fn notify_end_of_startup(&self) -> PluginResult<()> {
        Ok(())
    }

    fn update_slot_status(
        &self,
        _slot: u64,
        _parent: Option<u64>,
        _status: &SlotStatus,
    ) -> PluginResult<()> {
        Ok(())
    }

    fn notify_transaction(
        &self,
        transaction: ReplicaTransactionInfoVersions<'_>,
        slot: u64,
    ) -> PluginResult<()> {
        let message_tx =
            MessageTransactionConverter::convert_transaction_versions_to_message_transaction(
                transaction,
                slot,
            )?;

        // Convert to TransactionUpdate and process immediately
        // TransferEventHandler will handle all batching and pubsub logic
        match MessageTransactionConverter::convert_to_transaction_update(&message_tx) {
            Ok(tx_update) => {
                // Use the plugin's runtime to handle the async operation
                unsafe {
                    #[allow(static_mut_refs)]
                    if let Err(e) = GLOBAL_PLUGIN.as_ref().unwrap().run_async(async move {
                        #[allow(static_mut_refs)]
                        GLOBAL_PLUGIN
                            .as_ref()
                            .expect("Global plugin not initialized")
                            .pipeline
                            .handle(&tx_update)
                            .await
                            .map_err(|e| {
                                // tracing::error!("Pipeline error details: {:?}", e);
                                GeyserPluginError::Custom(Box::new(std::io::Error::other(format!(
                                    "pipeline error: {:?}",
                                    e
                                ))))
                            })
                    }) {
                        // tracing::error!("Pipeline error: {:?}", e);
                        return Err(GeyserPluginError::Custom(Box::new(std::io::Error::other(
                            format!("Pipeline error: {:?}", e),
                        ))));
                    }
                    Ok(())
                }
            }
            Err(e) => {
                tracing::error!("Failed to convert transaction: {:?}", e);
                Err(GeyserPluginError::Custom(Box::new(std::io::Error::other(
                    format!("Failed to convert transaction: {:?}", e),
                ))))
            }
        }
    }

    fn notify_entry(&self, _entry: ReplicaEntryInfoVersions) -> PluginResult<()> {
        Ok(())
    }

    fn notify_block_metadata(&self, block_info: ReplicaBlockInfoVersions<'_>) -> PluginResult<()> {
        let block_meta_update =
            BlockMetadataConverter::convert_block_info_versions_to_subscribe_update(block_info)?;

        unsafe {
            #[allow(static_mut_refs)]
            if let Err(e) = GLOBAL_PLUGIN.as_ref().unwrap().run_async(async move {
                #[allow(static_mut_refs)]
                let futures: Vec<_> = GLOBAL_PLUGIN
                    .as_ref()
                    .expect("Global plugin not initialized")
                    .block_meta_pipeline
                    .par_iter()
                    .map(|pipeline| pipeline.handle(&block_meta_update))
                    .collect();

                futures::future::join_all(futures).await;
                Ok(())
            }) {
                tracing::error!("Pipeline error: {:?}", e);
                return Err(GeyserPluginError::Custom(Box::new(std::io::Error::other(
                    format!("Pipeline error: {:?}", e),
                ))));
            }
        }

        Ok(()) // ignore errors
    }

    fn account_data_notifications_enabled(&self) -> bool {
        false
    }

    fn account_data_snapshot_notifications_enabled(&self) -> bool {
        false
    }

    fn transaction_notifications_enabled(&self) -> bool {
        true
    }

    fn entry_notifications_enabled(&self) -> bool {
        false
    }
}

#[no_mangle]
#[allow(improper_ctypes_definitions)]
/// # Safety
///
/// This function returns the Plugin pointer as trait GeyserPlugin.
pub unsafe extern "C" fn _create_plugin() -> *mut dyn GeyserPlugin {
    let plugin = Plugin::default();
    let plugin: Box<dyn GeyserPlugin> = Box::new(plugin);
    Box::into_raw(plugin)
}
