[package]
name = "kryptogo-oldfaithful-plugin"
version = "7.0.0"
edition = "2021"
description = "Kryptogo Oldfaithful Geyser Plugin"

[lib]
crate-type = ["cdylib", "rlib"]

[[bin]]
name = "config-check"
path = "bin/config-check.rs"

[dependencies]
anyhow = "1.0.79"
log = "0.4"
serde = { workspace = true }
serde_json = { workspace = true }
agave-geyser-plugin-interface = "=2.2.7"
yellowstone-grpc-proto = { version = "=6.1.0", features = ["plugin"] }
tokio = { workspace = true }
clap = { workspace = true }
rustls = { workspace = true }

# Event handlers for publisher functionality
event-handlers = { path = "../event-handlers" }

# Yellowstone vixen for pipeline support
yellowstone-vixen = { workspace = true }
yellowstone-vixen-core = { workspace = true }

# Common program parsers
common-program-parsers = { path = "../common-program-parsers" }

# Additional dependencies for async support
futures = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
rayon = "1.10.0"

[build-dependencies]
anyhow = { workspace = true }
git-version = { workspace = true }
vergen = { workspace = true }
