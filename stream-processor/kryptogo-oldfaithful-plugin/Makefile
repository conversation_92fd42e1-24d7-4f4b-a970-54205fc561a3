# Makefile for building the Geyser plugin

.PHONY: build clean release debug test

# Build the plugin in release mode
build: release

# Build the plugin in release mode
release:
	cargo build --release

# Build the plugin in debug mode
debug:
	cargo build

# Clean build artifacts
clean:
	cargo clean

# Run tests
test:
	cargo test

# Build and copy the plugin to a specific location
install: release
	mkdir -p ./target/plugin
	cp target/release/libyellowstone_grpc_geyser.so ./target/plugin/

# Show the plugin library path
show-lib:
	@echo "Plugin library: $(shell pwd)/target/release/libyellowstone_grpc_geyser.so"

# Check configuration
config-check:
	cargo run --bin config-check -- --config config.json

help:
	@echo "Available targets:"
	@echo "  build     - Build the plugin in release mode"
	@echo "  release   - Build the plugin in release mode"
	@echo "  debug     - Build the plugin in debug mode" 
	@echo "  clean     - Clean build artifacts"
	@echo "  test      - Run tests"
	@echo "  install   - Build and copy plugin to target/plugin/"
	@echo "  show-lib  - Show the plugin library path"
	@echo "  config-check - Check configuration file"
	@echo "  help      - Show this help message"
