fn main() -> anyhow::Result<()> {
    vergen::EmitBuilder::builder()
        .all_build()
        .all_rustc()
        .emit()?;

    // vergen git version does not looks cool
    // println!(
    //     "cargo:rustc-env=GIT_VERSION={}",
    //     git_version::git_version!()
    // );

    // Set default versions since cargo-lock parsing is causing issues
    println!("cargo:rustc-env=SOLANA_SDK_VERSION=2.2.0");
    println!("cargo:rustc-env=YELLOWSTONE_GRPC_PROTO_VERSION=6.1.0");

    Ok(())
}
