// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EventMetadata {
    #[prost(bytes = "vec", tag = "1")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "2")]
    pub slot: u64,
    #[prost(uint32, tag = "3")]
    pub ix_index: u32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SolTransferEvent {
    #[prost(message, optional, tag = "1")]
    pub metadata: ::core::option::Option<EventMetadata>,
    #[prost(string, tag = "2")]
    pub from_wallet: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub to_wallet: ::prost::alloc::string::String,
    /// in lamports
    #[prost(uint64, tag = "4")]
    pub amount_lamports: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TokenTransferEvent {
    #[prost(message, optional, tag = "1")]
    pub metadata: ::core::option::Option<EventMetadata>,
    #[prost(string, tag = "2")]
    pub token_mint: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub from_wallet: ::prost::alloc::string::String,
    #[prost(string, tag = "4")]
    pub to_wallet: ::prost::alloc::string::String,
    #[prost(string, tag = "5")]
    pub amount: ::prost::alloc::string::String,
}
/// New event types for the updated schema
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SolTransferEventV2 {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(uint32, tag = "2")]
    pub tx_index: u32,
    #[prost(uint32, tag = "3")]
    pub ix_index: u32,
    #[prost(bytes = "vec", tag = "4")]
    pub from_wallet: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "5")]
    pub to_wallet: ::prost::alloc::vec::Vec<u8>,
    /// in lamports
    #[prost(uint64, tag = "6")]
    pub amount: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TokenTransferEventV2 {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(uint32, tag = "2")]
    pub tx_index: u32,
    #[prost(uint32, tag = "3")]
    pub ix_index: u32,
    #[prost(bytes = "vec", tag = "4")]
    pub token_mint: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "5")]
    pub from_wallet: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "6")]
    pub to_wallet: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "7")]
    pub amount: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TransactionEvent {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(uint32, tag = "2")]
    pub tx_index: u32,
    #[prost(bytes = "vec", tag = "3")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SolBalanceEvent {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(bytes = "vec", tag = "2")]
    pub account: ::prost::alloc::vec::Vec<u8>,
    /// in lamports
    #[prost(uint64, tag = "3")]
    pub amount: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TokenBalanceEvent {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(bytes = "vec", tag = "2")]
    pub owner: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "3")]
    pub token: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "4")]
    pub amount: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SlotTime {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(uint64, tag = "2")]
    pub timestamp: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SolanaEvent {
    #[prost(oneof = "solana_event::Event", tags = "1, 2, 3, 4, 5, 6, 7, 8")]
    pub event: ::core::option::Option<solana_event::Event>,
}
/// Nested message and enum types in `SolanaEvent`.
pub mod solana_event {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Event {
        #[prost(message, tag = "1")]
        SolTransfer(super::SolTransferEvent),
        #[prost(message, tag = "2")]
        TokenTransfer(super::TokenTransferEvent),
        #[prost(message, tag = "3")]
        SlotTime(super::SlotTime),
        #[prost(message, tag = "4")]
        SolTransferV2(super::SolTransferEventV2),
        #[prost(message, tag = "5")]
        TokenTransferV2(super::TokenTransferEventV2),
        #[prost(message, tag = "6")]
        Transaction(super::TransactionEvent),
        #[prost(message, tag = "7")]
        SolBalance(super::SolBalanceEvent),
        #[prost(message, tag = "8")]
        TokenBalance(super::TokenBalanceEvent),
    }
}
/// Topic: solana-events
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct BatchedSolanaEvent {
    #[prost(message, repeated, tag = "1")]
    pub events: ::prost::alloc::vec::Vec<SolanaEvent>,
}
