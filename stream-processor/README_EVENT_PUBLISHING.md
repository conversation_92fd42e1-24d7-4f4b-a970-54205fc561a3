# Stream Processor Event Publishing Implementation

This document describes the implementation of event publishing in the stream-processor, which now supports both legacy transfer events and new V2 events including balance tracking and transaction information.

## Overview

The stream-processor has been enhanced to publish comprehensive Solana event data including:

1. **Legacy Transfer Events** (backward compatibility)
   - SOL transfers
   - Token transfers
   - Slot times

2. **New V2 Transfer Events**
   - SOL transfers V2 (with bytes for wallet addresses)
   - Token transfers V2 (with bytes for wallet addresses and token mints)

3. **Balance Events**
   - SOL balance updates
   - Token balance updates

4. **Transaction Events**
   - Transaction metadata (slot, tx_index, signature)

## Architecture

### Parsers

The implementation includes several new parsers:

#### 1. `SolBalanceParser` (`common-program-parsers/src/sol_balance_parser.rs`)

- **Input**: `AccountUpdate` for system program accounts
- **Output**: `SolBalance` (slot, account, lamports)
- **Purpose**: Tracks SOL balance changes for any account

#### 2. `TransactionParser` (`common-program-parsers/src/transaction_parser.rs`)

- **Input**: `TransactionUpdate`
- **Output**: `TransactionInfo` (slot, tx_index, signature)
- **Purpose**: Captures transaction metadata for correlation

#### 3. Existing Parsers (Enhanced)

- `SystemProgramParser`: SOL transfers
- `TokenIxParser`: Token program instruction parsing
- `TokenExtensionIxParser`: Token extension program instruction parsing
- `TokenAccountParser`: Token account state parsing
- `TokenExtensionAccountParser`: Token extension account state parsing
- `BlockMetaParser`: Slot time information

### Handlers

#### 1. `TransferEventHandler` (Enhanced)

- Handles legacy and V2 transfer events
- Processes SOL transfers, token transfers, and slot times
- Maintains backward compatibility
- Publishes both legacy and V2 event formats

#### 2. `BalanceEventHandler` (New)

- Handles balance-related events
- Processes SOL balance updates
- Processes token balance updates (both regular and extension tokens)
- Processes transaction metadata
- Publishes events to the same Pub/Sub topic

## Event Flow

### Transfer Events Flow

1. **Parsing**: Instructions are parsed by respective parsers
2. **Handling**: `TransferEventHandler` processes the parsed data
3. **Conversion**: Data is converted to both legacy and V2 proto formats
4. **Batching**: Events are batched for efficient publishing
5. **Publishing**: Batched events are sent to Pub/Sub topic "solana-events"

### Balance Events Flow

1. **Parsing**: Account updates are parsed by balance parsers
2. **Handling**: `BalanceEventHandler` processes the parsed data
3. **Conversion**: Data is converted to proto format
4. **Batching**: Events are batched for efficient publishing
5. **Publishing**: Batched events are sent to Pub/Sub topic "solana-events"

### Transaction Events Flow

1. **Parsing**: Transaction updates are parsed by `TransactionParser`
2. **Handling**: `BalanceEventHandler` processes transaction metadata
3. **Conversion**: Data is converted to proto format
4. **Batching**: Events are batched for efficient publishing
5. **Publishing**: Batched events are sent to Pub/Sub topic "solana-events"

## Proto Messages

The implementation uses the following proto message types:

### Legacy Events

- `SolTransferEvent`: SOL transfers with string wallet addresses
- `TokenTransferEvent`: Token transfers with string addresses
- `SlotTime`: Slot timestamp information

### V2 Events

- `SolTransferEventV2`: SOL transfers with bytes wallet addresses
- `TokenTransferEventV2`: Token transfers with bytes addresses
- `TransactionEvent`: Transaction metadata
- `SolBalanceEvent`: SOL balance updates
- `TokenBalanceEvent`: Token balance updates

### Wrapper

- `SolanaEvent`: Oneof wrapper for all event types
- `BatchedSolanaEvent`: Batch container for multiple events

## Configuration

### Vixen Runtime Configuration

The main.rs file configures the Vixen runtime with multiple pipelines:

```rust
let vixen_runtime = yellowstone_vixen::Runtime::builder()
    .commitment_level(yellowstone_vixen::CommitmentLevel::Processed)
    // Instruction pipelines for transfer events
    .instruction(Pipeline::new(SystemProgramParser, [transfer_handler.clone()]))
    .instruction(Pipeline::new(TokenIxParser, [transfer_handler.clone()]))
    .instruction(Pipeline::new(TokenExtensionIxParser, [transfer_handler.clone()]))
    // Account pipelines for balance events
    .account(Pipeline::new(SolBalanceParser, [balance_handler.clone()]))
    .account(Pipeline::new(TokenAccountParser, [balance_handler.clone()]))
    .account(Pipeline::new(TokenExtensionAccountParser, [balance_handler.clone()]))
    // Transaction pipeline for transaction events
    .transaction(Pipeline::new(TransactionParser, [balance_handler.clone()]))
    // Block meta pipeline for slot time events
    .block_meta(Pipeline::new(BlockMetaParser, [transfer_handler.clone()]))
    .build(vixen_config);
```

### Handler Configuration

Both handlers are configured with:

- **Min batch size**: 100 events
- **Flush interval**: 100ms
- **Max batch size**: 3000 events (internal constant)

## Data Processing

### Batching Strategy

- Events are collected in memory buffers
- Batches are flushed when:
  - Maximum batch size is reached (3000 events)
  - Flush interval timer expires (100ms)
  - Shutdown is initiated
- Each handler maintains its own batch buffer

### Error Handling

- Failed parsing results in filtered events (not published)
- Handler errors are logged but don't stop processing
- Graceful shutdown ensures all buffered events are published

### Performance Considerations

- Concurrent processing of different event types
- Efficient batching reduces Pub/Sub overhead
- Memory-efficient event storage
- Non-blocking event processing

## Backward Compatibility

The implementation maintains full backward compatibility:

- Legacy transfer events continue to be published
- Existing consumers can continue to work unchanged
- New V2 events are published alongside legacy events
- No breaking changes to existing functionality

## Monitoring and Observability

### Logging

- Structured logging with tracing
- Event counts and batch sizes are logged
- Error conditions are logged with context
- Performance metrics are logged periodically

### Health Checks

- HTTP health check endpoint on configured port
- Database connectivity verification
- Pub/Sub connectivity verification

## Future Enhancements

The modular architecture enables easy future enhancements:

- **New Event Types**: Easy to add new parsers and handlers
- **Alternative Publishers**: Pub/Sub client can be replaced
- **Enhanced Filtering**: More sophisticated event filtering
- **Performance Optimization**: Independent optimization of each pipeline
- **Monitoring**: Enhanced metrics and alerting

## Deployment

### Environment Variables

- `GRPC_URL`: Yellowstone GRPC endpoint
- `GRPC_AUTH_TOKEN`: Authentication token (optional)
- `GRPC_TIMEOUT`: GRPC timeout in seconds
- `HEALTH_CHECK_PORT`: HTTP health check port (default: 8080)

### Dependencies

- `yellowstone-vixen`: Core streaming framework
- `event-proto`: Protocol buffer definitions
- `tokio`: Async runtime
- `tracing`: Logging framework
- `axum`: HTTP server for health checks

## Testing

The implementation includes unit tests for:

- Parser functionality
- Handler logic
- Proto message serialization
- Error handling scenarios

Tests can be run with:

```bash
cargo test
```
