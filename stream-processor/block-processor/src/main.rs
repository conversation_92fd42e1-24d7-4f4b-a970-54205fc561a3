use std::sync::Arc;
use std::{
    env,
    time::{Duration, Instant},
};

use anyhow::Result;
use clap::Parser;
use common_program_parsers::BlockMetaParser;
use common_program_parsers::{
    system_program_parser::SystemProgramParser,
    token_extension_program::InstructionParser as TokenExtensionIxParser,
    token_program::InstructionParser as TokenIxParser,
};
use event_handlers::{PubSubClient, TransferEventHandler};
use logging;
use rayon::prelude::*;
use rustls::crypto::CryptoProvider;
use solana_client::{rpc_client::RpcClient, rpc_config::RpcBlockConfig};
use solana_commitment_config::CommitmentConfig;
use solana_transaction_status::{TransactionDetails, UiTransactionEncoding};
use tokio::sync::Semaphore;
use yellowstone_grpc_proto::geyser::SubscribeUpdateBlockMeta;
use yellowstone_grpc_proto::prelude::{BlockHeight, UnixTimestamp};
use yellowstone_vixen::{
    handler::DynPipeline, instruction::InstructionPipeline, metrics::NullMetrics, Pipeline,
};
use yellowstone_vixen_core::instruction::InstructionUpdate;

mod transaction_converter;
use transaction_converter::TransactionConverter;
use yellowstone_vixen_core::BlockMetaUpdate;

type BoxPipeline<'h, T> = Box<dyn DynPipeline<T> + Send + Sync + 'h>;

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// Starting block number (inclusive)
    #[arg(long)]
    from_block: u64,

    /// Ending block number (inclusive)
    #[arg(long)]
    to_block: u64,

    /// Number of parallel threads
    #[arg(long, default_value = "10")]
    threads: usize,

    /// Include balance events in the output
    #[arg(long, default_value = "true")]
    include_balance_events: bool,
}

struct BlockProcessor {
    rpc_client: Arc<RpcClient>,
    pipeline: InstructionPipeline<NullMetrics>,
    block_meta_pipeline: Vec<BoxPipeline<'static, BlockMetaUpdate>>,
}

impl BlockProcessor {
    async fn new(
        rpc_url: &str,
        pipelines: Vec<BoxPipeline<'static, InstructionUpdate>>,
        block_meta_pipeline: Vec<BoxPipeline<'static, BlockMetaUpdate>>,
    ) -> Result<Self> {
        let rpc_client = Arc::new(RpcClient::new(rpc_url.to_string()));
        let pipeline = InstructionPipeline::new(pipelines, &NullMetrics);
        if pipeline.is_none() {
            return Err(anyhow::anyhow!("Failed to create pipeline"));
        }

        Ok(Self {
            rpc_client,
            pipeline: pipeline.unwrap(),
            block_meta_pipeline,
        })
    }

    async fn process_block(&self, slot: u64) -> Result<()> {
        if slot % 1000 == 0 {
            tracing::info!("Processing block {}", slot);
        }

        // Get block from RPC
        let block = self.rpc_client.get_block_with_config(
            slot,
            RpcBlockConfig {
                encoding: Some(UiTransactionEncoding::Json),
                transaction_details: Some(TransactionDetails::Full),
                rewards: Some(false),
                commitment: Some(CommitmentConfig::confirmed()),
                max_supported_transaction_version: Some(0),
            },
        )?;

        if block.block_time.is_some() && block.block_height.is_some() {
            let block_meta_update = SubscribeUpdateBlockMeta {
                slot,
                block_time: Some(UnixTimestamp {
                    timestamp: block.block_time.unwrap(),
                }),
                blockhash: block.blockhash.clone(),
                block_height: Some(BlockHeight {
                    block_height: block.block_height.unwrap(),
                }),
                rewards: None, // ignore for now
                parent_slot: block.parent_slot,
                parent_blockhash: block.previous_blockhash.clone(),
                executed_transaction_count: block.transactions.as_ref().unwrap().len() as u64,
                entries_count: 0, // ignore for now
            };
            let futures: Vec<_> = self
                .block_meta_pipeline
                .par_iter()
                .map(|pipeline| pipeline.handle(&block_meta_update))
                .collect();
            futures::future::join_all(futures).await;
        }

        let txs = match block.transactions {
            Some(txs) => txs,
            None => return Err(anyhow::anyhow!("No transactions found")),
        };

        let tx_updates: Vec<_> = txs
            .par_iter()
            .enumerate()
            .filter_map(|(index, tx)| {
                match TransactionConverter::convert_to_transaction_update(tx, slot, index as u64) {
                    Ok(tx_update) => Some(tx_update),
                    Err(e) => {
                        tracing::error!("Failed to convert to TransactionUpdate: {:?}", e);
                        None
                    }
                }
            })
            .collect();

        for chunk in tx_updates.chunks(10) {
            let futures: Vec<_> = chunk
                .iter()
                .map(|tx_update| self.pipeline.handle(tx_update))
                .collect();

            let results = futures::future::join_all(futures).await;
            for result in results {
                if let Err(e) = result {
                    if format!("{:?}", e).contains("AlreadyHandled") {
                        continue;
                    }
                    return Err(anyhow::anyhow!("Pipeline error: {:?}", e));
                }
            }
        }
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    logging::init_logging();

    let args = Args::parse();
    let rpc_url =
        env::var("RPC_URL").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());

    // Install the default crypto provider for rustls
    CryptoProvider::install_default(rustls::crypto::ring::default_provider())
        .expect("Failed to install crypto provider");

    // Create the same pipelines as in stream-processor-bin
    let event_publisher = Arc::new(PubSubClient::new().await?);
    let transfer_handler = TransferEventHandler::new(
        event_publisher.clone(),
        Duration::from_millis(3000),
        args.include_balance_events,
    );

    let ix_pipelines: Vec<BoxPipeline<'static, InstructionUpdate>> = vec![
        Box::new(Pipeline::new(
            SystemProgramParser,
            [transfer_handler.clone()],
        )),
        Box::new(Pipeline::new(TokenIxParser, [transfer_handler.clone()])),
        Box::new(Pipeline::new(
            TokenExtensionIxParser,
            [transfer_handler.clone()],
        )),
    ];

    let block_meta_pipeline: Vec<BoxPipeline<'static, BlockMetaUpdate>> = vec![Box::new(
        Pipeline::new(BlockMetaParser, [transfer_handler.clone()]),
    )];

    let processor =
        Arc::new(BlockProcessor::new(&rpc_url, ix_pipelines, block_meta_pipeline).await?);
    let semaphore = Arc::new(Semaphore::new(args.threads));

    // Create a stream of block numbers
    let blocks: Vec<u64> = (args.from_block..=args.to_block).collect();

    let total_start = Instant::now();

    // Process blocks in parallel using tokio
    let mut handles = Vec::new();
    for chunk in blocks.chunks(args.threads) {
        let mut chunk_handles = Vec::new();
        for &block in chunk {
            let processor = processor.clone();
            let semaphore = semaphore.clone();
            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                match processor.process_block(block).await {
                    Ok(_) => tracing::debug!("Successfully processed block {}", block),
                    Err(e) => {
                        if !format!("{:?}", e).contains("was skipped, or missing") {
                            tracing::error!("Error processing block {}: {:?}", block, e)
                        }
                    }
                }
            });
            chunk_handles.push(handle);
        }
        handles.extend(chunk_handles);
    }

    // Wait for all blocks to be processed
    for handle in handles {
        handle.await?;
    }

    let total_duration = total_start.elapsed();
    tracing::info!("Total execution time: {:?}", total_duration);

    if let Err(e) = transfer_handler.shutdown().await {
        tracing::error!("Error during shutdown: {:?}", e);
    }

    Ok(())
}
