#!/bin/bash

set -e

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 預設設定
DEFAULT_PROJECT="kryptogo-wallet-data"
DEFAULT_REGION="asia-east1"
DEFAULT_CLUSTER="kg-cluster"
DEFAULT_NAMESPACE="solana-data"
DEFAULT_BUCKET="kg-old-faithful"
DEFAULT_MAX_JOBS=10
DEFAULT_DRY_RUN=false

# 參數設定
PROJECT_ID=${PROJECT_ID:-$DEFAULT_PROJECT}
REGION=${REGION:-$DEFAULT_REGION}
CLUSTER_NAME=${CLUSTER_NAME:-$DEFAULT_CLUSTER}
NAMESPACE=${NAMESPACE:-$DEFAULT_NAMESPACE}
BUCKET_NAME=${BUCKET_NAME:-$DEFAULT_BUCKET}
MAX_CONCURRENT_JOBS=${MAX_CONCURRENT_JOBS:-$DEFAULT_MAX_JOBS}
DRY_RUN=${DRY_RUN:-$DEFAULT_DRY_RUN}

# 檔案過濾設定
FILE_PATTERN=${FILE_PATTERN:-"*.car"}
START_INDEX=${START_INDEX:-0}
MAX_FILES=${MAX_FILES:-0}  # 0 表示處理所有檔案

usage() {
    cat << EOF
使用方法: $0 [選項]

選項:
  -p, --project PROJECT_ID        GCP 專案 ID (預設: $DEFAULT_PROJECT)
  -r, --region REGION              GCP 區域 (預設: $DEFAULT_REGION)
  -c, --cluster CLUSTER_NAME       GKE 叢集名稱 (預設: $DEFAULT_CLUSTER)
  -n, --namespace NAMESPACE        Kubernetes 命名空間 (預設: $DEFAULT_NAMESPACE)
  -b, --bucket BUCKET_NAME         GCS 儲存桶名稱 (預設: $DEFAULT_BUCKET)
  -j, --max-jobs MAX_JOBS          最大同時執行 Jobs 數量 (預設: $DEFAULT_MAX_JOBS)
  -f, --file-pattern PATTERN       檔案過濾模式 (預設: $FILE_PATTERN)
  -s, --start-index INDEX          開始處理的檔案索引 (預設: $START_INDEX)
  -m, --max-files COUNT            最大處理檔案數量，0=全部 (預設: $MAX_FILES)
  -d, --dry-run                   只顯示要執行的操作，不實際執行
  -h, --help                      顯示此說明

環境變數:
  PROJECT_ID, REGION, CLUSTER_NAME, NAMESPACE, BUCKET_NAME
  MAX_CONCURRENT_JOBS, FILE_PATTERN, START_INDEX, MAX_FILES, DRY_RUN

範例:
  # 處理所有 CAR 檔案
  $0

  # 只處理前 10 個檔案，最多同時 5 個 Jobs
  $0 --max-files 10 --max-jobs 5

  # 從第 100 個檔案開始處理
  $0 --start-index 100

  # 乾跑模式，不實際提交 Jobs
  $0 --dry-run

EOF
}

# 解析命令列參數
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--project)
            PROJECT_ID="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -c|--cluster)
            CLUSTER_NAME="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -b|--bucket)
            BUCKET_NAME="$2"
            shift 2
            ;;
        -j|--max-jobs)
            MAX_CONCURRENT_JOBS="$2"
            shift 2
            ;;
        -f|--file-pattern)
            FILE_PATTERN="$2"
            shift 2
            ;;
        -s|--start-index)
            START_INDEX="$2"
            shift 2
            ;;
        -m|--max-files)
            MAX_FILES="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            log_error "未知選項: $1"
            usage
            exit 1
            ;;
    esac
done

# 驗證必要工具
check_dependencies() {
    log_info "檢查必要工具..."
    
    local missing_tools=()
    
    if ! command -v gcloud &> /dev/null; then
        missing_tools+=("gcloud")
    fi
    
    if ! command -v kubectl &> /dev/null; then
        missing_tools+=("kubectl")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_error "請安裝 Google Cloud SDK 和 kubectl"
        exit 1
    fi
    
    log_success "工具檢查完成"
}

# 設定 GCP 認證和叢集存取
setup_gcp_access() {
    log_info "設定 GCP 存取..."
    
    # 設定專案
    gcloud config set project "$PROJECT_ID"
    
    # 獲取叢集憑證
    if ! gcloud container clusters get-credentials "$CLUSTER_NAME" --region="$REGION" --project="$PROJECT_ID"; then
        log_error "無法連接到 GKE 叢集: $CLUSTER_NAME"
        exit 1
    fi
    
    # 檢查命名空間
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "命名空間 $NAMESPACE 不存在"
        exit 1
    fi
    
    log_success "GCP 存取設定完成"
}

# 掃描 GCS 儲存桶中的 CAR 檔案
scan_car_files() {
    log_info "掃描 GCS 儲存桶中的 CAR 檔案..."
    
    # 獲取檔案清單
    local temp_file=$(mktemp)
    if ! gsutil ls "gs://$BUCKET_NAME/$FILE_PATTERN" > "$temp_file" 2>/dev/null; then
        log_error "無法掃描 GCS 儲存桶 gs://$BUCKET_NAME"
        rm -f "$temp_file"
        exit 1
    fi
    
    # 讀取檔案清單
    readarray -t ALL_FILES < "$temp_file"
    rm -f "$temp_file"
    
    local total_files=${#ALL_FILES[@]}
    log_info "找到 $total_files 個 CAR 檔案"
    
    # 應用開始索引和最大檔案數量限制
    local end_index=$total_files
    if [ $MAX_FILES -gt 0 ]; then
        end_index=$((START_INDEX + MAX_FILES))
        if [ $end_index -gt $total_files ]; then
            end_index=$total_files
        fi
    fi
    
    # 提取要處理的檔案
    FILES_TO_PROCESS=("${ALL_FILES[@]:$START_INDEX:$((end_index - START_INDEX))}")
    
    log_info "將處理 ${#FILES_TO_PROCESS[@]} 個檔案 (從索引 $START_INDEX 開始)"
    
    if [ ${#FILES_TO_PROCESS[@]} -eq 0 ]; then
        log_warning "沒有檔案需要處理"
        exit 0
    fi
}

# 獲取正在執行的 Jobs 數量
get_running_jobs_count() {
    kubectl get jobs -n "$NAMESPACE" -l "app=backfiller,version=v2.0" --field-selector status.active=1 -o name 2>/dev/null | wc -l
}

# 等待 Jobs 完成到指定數量以下
wait_for_job_slots() {
    local max_jobs=$1
    
    while true; do
        local running_jobs=$(get_running_jobs_count)
        if [ $running_jobs -lt $max_jobs ]; then
            break
        fi
        
        log_info "目前有 $running_jobs 個 Jobs 執行中，等待空槽..."
        sleep 30
    done
}

# 生成 Job 名稱
generate_job_name() {
    local file_path="$1"
    local filename=$(basename "$file_path" .car)
    # 確保符合 Kubernetes 名稱規範
    echo "$(echo "$filename" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9-]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')"
}

# 創建並提交 Job
create_job() {
    local gcs_path="$1"
    local job_name="$2"
    
    log_info "創建 Job: $job_name"
    log_info "處理檔案: $gcs_path"
    
    # 讀取模板並替換變數
    local template_file="kubernetes/backfill-job-yellowstone.template.yaml"
    if [ ! -f "$template_file" ]; then
        log_error "找不到 Job 模板檔案: $template_file"
        return 1
    fi
    
    local job_yaml=$(mktemp)
    sed -e "s/JOB_NAME_PLACEHOLDER/$job_name/g" \
        -e "s|GCS_PATH_PLACEHOLDER|$gcs_path|g" \
        "$template_file" > "$job_yaml"
    
    if [ "$DRY_RUN" = true ]; then
        log_warning "[DRY RUN] 將創建以下 Job:"
        echo "---"
        cat "$job_yaml"
        echo "---"
    else
        # 提交 Job 和 Service
        if kubectl apply -f "$job_yaml" -n "$NAMESPACE"; then
            log_success "Job $job_name 提交成功"
        else
            log_error "Job $job_name 提交失敗"
            rm -f "$job_yaml"
            return 1
        fi
    fi
    
    rm -f "$job_yaml"
    return 0
}

# 主要處理邏輯
process_files() {
    log_info "開始處理 CAR 檔案..."
    log_info "最大同時 Jobs: $MAX_CONCURRENT_JOBS"
    
    local processed=0
    local total=${#FILES_TO_PROCESS[@]}
    
    for gcs_path in "${FILES_TO_PROCESS[@]}"; do
        # 等待空槽
        wait_for_job_slots "$MAX_CONCURRENT_JOBS"
        
        # 生成 Job 名稱
        local job_name=$(generate_job_name "$gcs_path")
        
        # 檢查是否已存在同名 Job
        if kubectl get job "$job_name" -n "$NAMESPACE" &> /dev/null; then
            log_warning "Job $job_name 已存在，跳過"
            continue
        fi
        
        # 創建 Job
        if create_job "$gcs_path" "$job_name"; then
            processed=$((processed + 1))
        fi
        
        log_info "進度: $processed/$total"
        
        # 避免過快提交
        if [ "$DRY_RUN" != true ]; then
            sleep 2
        fi
    done
    
    log_success "所有檔案處理完成！"
    log_info "已提交 $processed 個 Jobs"
}

# 顯示摘要
show_summary() {
    cat << EOF

=====================================
     Yellowstone Backfiller
=====================================

配置摘要:
  GCP 專案:        $PROJECT_ID
  GKE 叢集:        $CLUSTER_NAME (區域: $REGION)
  命名空間:        $NAMESPACE
  GCS 儲存桶:      gs://$BUCKET_NAME
  檔案模式:        $FILE_PATTERN
  開始索引:        $START_INDEX
  最大檔案數:      $([ $MAX_FILES -eq 0 ] && echo "無限制" || echo $MAX_FILES)
  最大同時 Jobs:   $MAX_CONCURRENT_JOBS
  乾跑模式:        $DRY_RUN

將要處理的檔案數: ${#FILES_TO_PROCESS[@]}

=====================================

EOF
}

# 主函數
main() {
    echo -e "${BLUE}Solana Backfiller - Yellowstone 版本${NC}"
    echo "================================================"
    
    check_dependencies
    setup_gcp_access
    scan_car_files
    show_summary
    
    if [ "$DRY_RUN" != true ]; then
        read -p "確認要繼續嗎? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
    
    process_files
    
    if [ "$DRY_RUN" != true ]; then
        log_info "使用以下命令監控 Jobs:"
        echo "  kubectl get jobs -n $NAMESPACE -l app=backfiller,version=v2.0"
        echo "  kubectl get services -n $NAMESPACE -l app=backfiller,version=v2.0"
        log_info "gRPC 端點將通過 Service 名稱提供: backfill-service-<job-name>:10001"
    fi
}

# 執行主函數
main "$@" 