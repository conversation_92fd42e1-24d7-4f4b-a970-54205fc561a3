# Solana Historical Data Backfiller

A system for processing Solana historical data from CAR files and publishing events to Pub/Sub.

## System Overview

```text
┌─────────────────────────────────────────────────────────────────┐
│                Solana Historical Data Backfiller 系統           │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  GCS Storage                 GKE Cluster                        │
│  ┌─────────────────┐         ┌──────────────────────────────┐   │
│  │ gs://kg-old-    │         │        Backfill Jobs         │   │
│  │ faithful/       │◄────────┤                              │   │
│  │ *.car files     │ gcsfuse │  ┌─────────────────────────┐ │   │
│  └─────────────────┘         │  │ geyser-plugin-runner    │ │   │
│                               │  │ + kryptogo-oldfaithful- │ │   │
│                               │  │   plugin.so             │ │   │
│                               │  │                         │ │   │
│                               │  │ Pub/Sub Publisher       │ │   │
│                               │  └─────────────────────────┘ │   │
│                               └──────────────┬───────────────┘   │
│                                              │                   │
│                                              ▼                   │
│                                   ┌─────────────────┐           │
│                                   │  Pub/Sub Topic  │           │
│                                   │  (solana-data)  │           │
│                                   └─────────────────┘           │
│                                              │                   │
│                                              ▼                   │
│                                   ┌─────────────────┐           │
│                                   │  data-writer    │           │
│                                   │  (消費者)       │           │
│                                   └─────────────────┘           │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

- **geyser-plugin-runner** (Git Submodule)
  - From `github.com/kryptogo/yellowstone-faithful`
  - Handles CAR file streaming and parsing
  
- **kryptogo-oldfaithful-plugin** (Custom Plugin)
  - Parses transactions and account updates
  - Publishes events to Pub/Sub
  
- **Docker Container**
  - Includes gcsfuse for GCS mounting
  - Integrates geyser-plugin-runner and custom plugin

## Data Flow

1. Job scans `gs://kg-old-faithful` and creates GKE Jobs for each CAR file
2. Each Job Pod mounts GCS bucket using gcsfuse
3. `geyser-plugin-runner` streams CAR file content
4. `kryptogo-oldfaithful-plugin` parses events and publishes to Pub/Sub
5. `data-writer` service consumes Pub/Sub events and writes to database

## Supported Data Types

- ✅ **Accounts** - Account state updates
- ✅ **Transactions** - Transaction data
- ✅ **Blocks** - Block data
- ✅ **Slots** - Slot information
- ✅ **Block Metadata** - Block metadata
- ✅ **Entries** - Entry data

## Quick Start

### Prerequisites

- GCP project
- GKE cluster
- GCS bucket (`gs://kg-old-faithful`)
- Pub/Sub Topic (`solana-data`)

### Setup

1. **Install tools**

   ```bash
   # Install gcsfuse
   echo "deb https://packages.cloud.google.com/apt gcsfuse-focal main" | sudo tee /etc/apt/sources.list.d/gcsfuse.list
   curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key add -
   sudo apt-get update
   sudo apt-get install gcsfuse

   # Install Docker
   curl -fsSL https://get.docker.com | sh
   ```

2. **Configure GCP**

   ```bash
   gcloud auth login
   gcloud config set project <your-project-id>
   gcloud auth configure-docker
   gcloud container clusters get-credentials <cluster-name> --zone <zone>
   ```

3. **Build and Deploy**

   ```bash
   cd backfiller
   make setup
   make all
   ./scripts/launch_jobs.sh
   ```

## Monitoring

### View Status

```bash
# Check job status
./scripts/monitor_jobs.sh

# View logs
kubectl logs -f job/backfill-job-<job-name>

# Monitor Pub/Sub
gcloud pubsub topics describe solana-data
```

### Key Metrics

- Job success rate
- Pub/Sub publish latency
- Memory usage
- CPU usage

## Troubleshooting

### Common Issues

1. **GCS Mount Failure**

   ```bash
   kubectl logs -f job/backfill-job-<job-name> -c gcsfuse
   ```

2. **Pub/Sub Publish Failure**

   ```bash
   kubectl logs -f job/backfill-job-<job-name> -c backfiller
   ```

3. **Memory Issues**

   ```bash
   kubectl edit job backfill-job-<job-name>
   ```

## Performance Tuning

### Resource Configuration

```yaml
resources:
  requests:
    memory: "16Gi"
    cpu: "4"
  limits:
    memory: "32Gi"
    cpu: "8"
```

### Batch Size

```yaml
env:
  - name: BATCH_SIZE
    value: "1000"
```

### Concurrency

```yaml
env:
  - name: MAX_CONCURRENT_JOBS
    value: "10"
```

## Security

1. **IAM Permissions**
   - GCS read access (`gs://kg-old-faithful`)
   - Pub/Sub publish permissions
   - Monitoring and logging permissions

2. **Network Security**
   - Use Workload Identity for authentication
   - Restrict Pod network access

3. **Data Security**
   - Verify CAR file integrity
   - Monitor Pub/Sub publish status

## Backup and Recovery

1. **CAR Files**

   ```bash
   gsutil cp gs://kg-old-faithful/*.car gs://backup-bucket/
   ```

2. **Pub/Sub**

   ```bash
   gcloud pubsub subscriptions create backup-sub --topic solana-data
   ```

## Expected Performance

- **Files**: ~700 CAR files
- **File Size**: Up to 800GB
- **Concurrency**: 10-20 concurrent Jobs
- **Memory**: 16GB per Job (adjustable)

## Maintenance

1. **Update Image**

   ```bash
   make all
   ```

2. **Redeploy Jobs**

   ```bash
   kubectl delete job backfill-job-<job-name>
   ./scripts/launch_jobs.sh
   ```

3. **Cleanup**

   ```bash
   kubectl delete jobs --field-selector status.successful=1
   ```
