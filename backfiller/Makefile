# Yellowstone Backfiller Makefile
# 用於建置和部署使用 Yellowstone 的 Solana 歷史資料回填系統

# 變數設定
PROJECT_ID ?= kryptogo-wallet-data
REGION ?= asia-east1
CLUSTER_NAME ?= kg-cluster
NAMESPACE ?= solana-data
REGISTRY ?= gcr.io/$(PROJECT_ID)
IMAGE_NAME ?= backfiller-yellowstone
IMAGE_TAG ?= latest
FULL_IMAGE_NAME = $(REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)

# 目錄設定
BUILD_DIR = build
SCRIPTS_DIR = scripts
CONFIG_DIR = config
K8S_DIR = ../kubernetes

# 日誌輸出使用 emoji 來標示狀態
# 🔧 [INFO] - 信息
# ✅ [SUCCESS] - 成功
# ⚠️  [WARNING] - 警告
# ❌ [ERROR] - 錯誤

.PHONY: all clean setup build-image push-image deploy test-build help

# 預設目標
all: setup build-image push-image

# 顯示說明
help:
	@echo "Yellowstone Backfiller - 建置和部署工具"
	@echo ""
	@echo "使用方法: make [目標] [變數=值]"
	@echo ""
	@echo "目標:"
	@echo "  all              - 執行完整建置流程 (setup + build-image + push-image)"
	@echo "  setup            - 設定 Git submodule 和環境"
	@echo "  build-image      - 建置 Docker 映像檔"
	@echo "  test-build       - 測試建置 (不推送)"
	@echo "  push-image       - 推送映像檔到 Container Registry"
	@echo "  deploy           - 部署到 GKE (需要先建置和推送映像檔)"
	@echo "  clean            - 清理建置產出"
	@echo "  validate-config  - 驗證配置檔案"
	@echo "  help             - 顯示此說明"
	@echo ""
	@echo "變數:"
	@echo "  PROJECT_ID       - GCP 專案 ID (預設: $(PROJECT_ID))"
	@echo "  REGION           - GCP 區域 (預設: $(REGION))"
	@echo "  CLUSTER_NAME     - GKE 叢集名稱 (預設: $(CLUSTER_NAME))"
	@echo "  NAMESPACE        - K8s 命名空間 (預設: $(NAMESPACE))"
	@echo "  IMAGE_TAG        - Docker 映像檔標籤 (預設: $(IMAGE_TAG))"
	@echo ""
	@echo "範例:"
	@echo "  make all                              # 完整建置"
	@echo "  make build-image IMAGE_TAG=v1.0      # 建置特定版本"
	@echo "  make deploy NAMESPACE=test-env       # 部署到測試環境"

# 設定環境
setup:
	@echo "🔧 [INFO] 設定 Yellowstone Backfiller 環境..."
	
	@# 檢查必要目錄
	@if [ ! -d "$(SCRIPTS_DIR)" ]; then \
		echo "❌ [ERROR] 找不到 scripts 目錄"; \
		exit 1; \
	fi
	
	@if [ ! -d "$(CONFIG_DIR)" ]; then \
		echo "❌ [ERROR] 找不到 config 目錄"; \
		exit 1; \
	fi
	
	@# 檢查 yellowstone-faithful submodule
	@if [ ! -d "yellowstone-faithful" ]; then \
		echo "⚠️  [WARNING] yellowstone-faithful submodule 不存在，跳過..."; \
	else \
		echo "🔧 [INFO] 更新 yellowstone-faithful submodule..."; \
		git submodule update --recursive yellowstone-faithful; \
	fi
	
	@# 檢查必要配置檔案 (如果不存在則警告但不退出)
	@if [ ! -f "$(CONFIG_DIR)/yellowstone_config.json" ]; then \
		echo "⚠️  [WARNING] 找不到 yellowstone 配置檔案，請確保稍後提供"; \
	fi
	
	@# 設定 GCP 專案
	@if command -v gcloud >/dev/null 2>&1; then \
		gcloud config set project $(PROJECT_ID) || { \
			echo "⚠️  [WARNING] 無法設定 GCP 專案，請確保 gcloud 已安裝並已認證"; \
		}; \
	else \
		echo "⚠️  [WARNING] gcloud 未安裝，跳過 GCP 專案設定"; \
	fi
	
	@# 檢查 Docker 認證
	@if command -v gcloud >/dev/null 2>&1; then \
		gcloud auth configure-docker --quiet || { \
			echo "⚠️  [WARNING] Docker 認證設定失敗，請檢查 gcloud 認證狀態"; \
		}; \
	else \
		echo "⚠️  [WARNING] gcloud 未安裝，跳過 Docker 認證設定"; \
	fi
	
	@echo "✅ [SUCCESS] 環境設定完成"

# 驗證配置檔案
validate-config:
	@echo "🔧 [INFO] 驗證配置檔案..."
	
	@# 檢查 JSON 格式
	@if ! jq empty "$(CONFIG_DIR)/yellowstone_config.json" 2>/dev/null; then \
		echo "❌ [ERROR] yellowstone_config.json 格式錯誤"; \
		exit 1; \
	fi
	
	@# 檢查必要欄位
	@if ! jq -e '.libpath' "$(CONFIG_DIR)/yellowstone_config.json" >/dev/null; then \
		echo "❌ [ERROR] 配置檔案缺少 libpath 欄位"; \
		exit 1; \
	fi
	
	@echo "✅ [SUCCESS] 配置檔案驗證通過"

# 測試建置
test-build: validate-config
	@echo "🔧 [INFO] 執行測試建置..."
	
	@docker build \
		-f Dockerfile \
		-t $(IMAGE_NAME):test \
		--target builder \
		.. || { \
		echo "❌ [ERROR] 測試建置失敗"; \
		exit 1; \
	}
	
	@echo "✅ [SUCCESS] 測試建置成功"

# 建置 Docker 映像檔
build-image: setup validate-config
	@echo "🔧 [INFO] 建置 Docker 映像檔: $(FULL_IMAGE_NAME)"
	
	@docker build \
		-f Dockerfile \
		-t $(FULL_IMAGE_NAME) \
		.. || { \
		echo "❌ [ERROR] Docker 建置失敗"; \
		exit 1; \
	}
	
	@# 標記為 latest (如果不是 latest 標籤)
	@if [ "$(IMAGE_TAG)" != "latest" ]; then \
		docker tag $(FULL_IMAGE_NAME) $(REGISTRY)/$(IMAGE_NAME):latest; \
	fi
	
	@echo "✅ [SUCCESS] 映像檔建置完成: $(FULL_IMAGE_NAME)"

# 推送映像檔
push-image:
	@echo "🔧 [INFO] 推送映像檔: $(FULL_IMAGE_NAME)"
	
	@docker push $(FULL_IMAGE_NAME) || { \
		echo "❌ [ERROR] 映像檔推送失敗"; \
		exit 1; \
	}
	
	@# 如果建置了 latest 標籤也推送
	@if [ "$(IMAGE_TAG)" != "latest" ] && docker images $(REGISTRY)/$(IMAGE_NAME):latest --format "table {{.Repository}}" | grep -q $(IMAGE_NAME); then \
		echo "🔧 [INFO] 推送 latest 標籤..."; \
		docker push $(REGISTRY)/$(IMAGE_NAME):latest; \
	fi
	
	@echo "✅ [SUCCESS] 映像檔推送完成"

# 部署到 GKE
deploy:
	@echo "🔧 [INFO] 部署 Yellowstone Backfiller..."
	
	@# 檢查叢集連接
	@gcloud container clusters get-credentials $(CLUSTER_NAME) \
		--region=$(REGION) \
		--project=$(PROJECT_ID) || { \
		echo "❌ [ERROR] 無法連接到 GKE 叢集"; \
		exit 1; \
	}
	
	@# 檢查命名空間
	@kubectl get namespace $(NAMESPACE) >/dev/null 2>&1 || { \
		echo "⚠️  [WARNING] 命名空間 $(NAMESPACE) 不存在，正在創建..."; \
		kubectl create namespace $(NAMESPACE); \
	}
	
	@# 啟動部署腳本
	@chmod +x $(SCRIPTS_DIR)/launch_yellowstone_jobs.sh
	@$(SCRIPTS_DIR)/launch_yellowstone_jobs.sh \
		--project $(PROJECT_ID) \
		--region $(REGION) \
		--cluster $(CLUSTER_NAME) \
		--namespace $(NAMESPACE) \
		--dry-run
	
	@echo "✅ [SUCCESS] 部署配置完成，使用 --dry-run 模式驗證"

# 清理建置產出
clean:
	@echo "🔧 [INFO] 清理建置產出..."
	
	@# 清理 Docker 映像檔
	@if docker images $(REGISTRY)/$(IMAGE_NAME) --format "table {{.Repository}}" | grep -q $(IMAGE_NAME); then \
		echo "🔧 [INFO] 清理 Docker 映像檔..."; \
		docker rmi $(REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG) 2>/dev/null || true; \
		if [ "$(IMAGE_TAG)" != "latest" ]; then \
			docker rmi $(REGISTRY)/$(IMAGE_NAME):latest 2>/dev/null || true; \
		fi; \
	fi
	
	@# 清理測試映像檔
	@docker rmi $(IMAGE_NAME):test 2>/dev/null || true
	
	@# Docker 系統清理
	@docker system prune -f
	
	@echo "✅ [SUCCESS] 清理完成"

# 顯示當前配置
show-config:
	@echo ""
	@echo "=============================="
	@echo "  Yellowstone Backfiller"
	@echo "=============================="
	@echo ""
	@echo "配置:"
	@echo "  GCP 專案:     $(PROJECT_ID)"
	@echo "  區域:         $(REGION)"
	@echo "  GKE 叢集:     $(CLUSTER_NAME)"
	@echo "  命名空間:     $(NAMESPACE)"
	@echo "  映像檔:       $(FULL_IMAGE_NAME)"
	@echo ""
	@echo "檔案位置:"
	@echo "  Dockerfile:   Dockerfile.yellowstone"
	@echo "  配置檔案:     $(CONFIG_DIR)/yellowstone_config.json"
	@echo "  啟動腳本:     $(SCRIPTS_DIR)/launch_yellowstone_jobs.sh"
	@echo "  K8s 模板:     $(K8S_DIR)/backfill-job-yellowstone.template.yaml"
	@echo ""

# 快速啟動開發環境
dev-setup: setup
	@echo "🔧 [INFO] 設定開發環境..."
	
	@# 安裝開發依賴
	@if ! command -v jq >/dev/null 2>&1; then \
		echo "⚠️  [WARNING] jq 未安裝，請手動安裝"; \
	fi
	
	@# 檢查 Rust 環境 (如果需要本地開發)
	@if command -v cargo >/dev/null 2>&1; then \
		echo "🔧 [INFO] 檢查 Rust 環境..."; \
		cd yellowstone-faithful/geyser-plugin-runner && cargo check; \
	fi
	
	@echo "✅ [SUCCESS] 開發環境設定完成" 