use anyhow::{anyhow, Result};
use chrono::Utc;
use dotenv::dotenv;
use jito_sdk_rust::JitoJsonRpcSDK;
use log::{debug, error, info};
use solana_bot::acquire_trading_lock;
use solana_bot::{
    get_latest_trades, get_potential_wallets, get_token_account_balance, process_trade,
    send_tx_via_jito, telegram::TELEGRAM_BOT, wallet_store::WalletStore, PoolBuyInfo,
};
use solana_bot::{
    get_raydium_pool_implementation, wallet::WalletContext, RaydiumPoolType, TradeState,
};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_associated_token_account::get_associated_token_address;
use std::collections::{HashMap, HashSet};
use std::io::Write;
use std::sync::atomic::AtomicBool;
use std::sync::{<PERSON>, Mutex};
use std::{
    env,
    str::FromStr,
    time::{Duration, SystemTime},
};
use tokio::sync::Semaphore;
use tokio::time::sleep;

// Shared state for active trades
struct ActiveTrades {
    trades: HashSet<(String, String)>,
}

impl ActiveTrades {
    fn new() -> Self {
        Self {
            trades: HashSet::new(),
        }
    }

    fn is_token_trading(&self, token_mint: &str, target_wallet: &str) -> bool {
        self.trades
            .iter()
            .any(|(mint, wallet)| mint == token_mint && wallet == target_wallet)
    }

    fn add_trade(&mut self, token_mint: String, target_wallet: String) -> bool {
        if self.is_token_trading(&token_mint, &target_wallet) {
            false
        } else {
            self.trades.insert((token_mint, target_wallet));
            true
        }
    }

    fn remove_trade(&mut self, token_mint: &str, target_wallet: &str) {
        self.trades
            .remove(&(token_mint.to_string(), target_wallet.to_string()));
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();
    info!("Starting Solana trading bot...");

    // Initialize Telegram bot
    let telegram_bot = solana_bot::telegram::TelegramBot::new(
        &env::var("TELEGRAM_BOT_TOKEN").expect("TELEGRAM_BOT_TOKEN must be set"),
        env::var("TELEGRAM_CHANNEL_ID")
            .expect("TELEGRAM_CHANNEL_ID must be set")
            .parse::<i64>()
            .expect("TELEGRAM_CHANNEL_ID must be a valid integer"),
    );
    TELEGRAM_BOT
        .set(Arc::new(telegram_bot))
        .expect("Failed to set Telegram bot");

    // Initialize wallet and rpc
    let wallet = Arc::new(WalletContext::new()?);
    info!("Wallet public key: {}", wallet.payer());
    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url.to_string(),
        CommitmentConfig::confirmed(),
    ));
    let jito_sdk = Arc::new(JitoJsonRpcSDK::new(
        "https://mainnet.block-engine.jito.wtf/api/v1",
        None,
    ));
    let random_tip_account = jito_sdk.get_random_tip_account().await?;
    let jito_tip_account = Pubkey::from_str(&random_tip_account)?;

    // Shared state
    let active_trades = Arc::new(Mutex::new(ActiveTrades::new()));
    let seen_txs = Arc::new(Mutex::new(HashMap::<String, bool>::new()));
    let my_wallet_trading = Arc::new(AtomicBool::new(false));

    // Thread limiter
    let semaphore = Arc::new(Semaphore::new(10)); // Limit to 10 concurrent trades

    // Main loop for finding trade opportunities
    let mut tracked_wallet_balances: HashMap<String, u64> = HashMap::new();
    let mut interval = tokio::time::interval(Duration::from_millis(500));
    let mut last_loop_time = Utc::now();
    loop {
        let now = Utc::now();
        debug!(
            "Time since last log: {} ms",
            (now - last_loop_time).num_milliseconds()
        );
        last_loop_time = now;

        interval.tick().await;

        // Load tracked wallets
        let wallet_store = WalletStore::load("tracked_wallets.json")?;
        let tracked_pub_keys: Vec<Pubkey> = wallet_store
            .get_wallets()
            .iter()
            .filter_map(|addr| Pubkey::from_str(addr).ok())
            .collect();

        // Batch get SOL balances for all tracked wallets
        let last_wallet_balances = tracked_wallet_balances.clone();
        let (new_balances, potential_wallets) =
            match get_potential_wallets(&client, &tracked_pub_keys, &last_wallet_balances) {
                Ok((balances, wallets)) => (balances, wallets),
                Err(e) => {
                    error!("Error getting wallet balances: {:?}", e);
                    sleep(Duration::from_secs(1)).await;
                    continue;
                }
            };
        tracked_wallet_balances = new_balances;

        if potential_wallets.is_empty() {
            continue;
        }

        for track_target in potential_wallets.iter() {
            let target_wallet = Pubkey::from_str(&track_target).unwrap();

            // Get latest trades
            let trades =
                match get_latest_trades(&client, &target_wallet, &mut seen_txs.lock().unwrap()) {
                    Ok(trades) => trades,
                    Err(_) => continue,
                };

            for trade in trades {
                // Skip if we're already trading this token
                let token_mint = match &trade {
                    PoolBuyInfo::Raydium(info) => info.token_mint.to_string(),
                    PoolBuyInfo::PumpFun(info) => info.token_mint.to_string(),
                    _ => continue,
                };
                {
                    let active_trades = active_trades.lock().unwrap();
                    if active_trades
                        .is_token_trading(&token_mint, target_wallet.to_string().as_str())
                    {
                        continue;
                    }
                }

                // Try to acquire a thread slot with 10 second timeout
                if let Ok(permit) =
                    tokio::time::timeout(Duration::from_secs(1), semaphore.clone().acquire_owned())
                        .await
                {
                    if let Ok(permit) = permit {
                        // Clone necessary Arc's for the new thread
                        let client = client.clone();
                        let wallet = wallet.clone();
                        let jito_sdk = jito_sdk.clone();
                        let active_trades = active_trades.clone();
                        let token_mint_clone = token_mint.clone();
                        let my_wallet_trading_clone = my_wallet_trading.clone();

                        // Add token to active trades
                        {
                            let mut active_trades = active_trades.lock().unwrap();
                            if !active_trades
                                .add_trade(token_mint.clone(), target_wallet.to_string())
                            {
                                continue;
                            }
                        }

                        // Spawn a new thread for this trade
                        tokio::spawn(async move {
                            // info!("Starting new trade thread for token: {}", token_mint);

                            // Execute buy phase
                            if let Some(trade_state) = process_trade(
                                trade,
                                &target_wallet,
                                &client,
                                &jito_sdk,
                                jito_tip_account,
                                &wallet,
                                my_wallet_trading_clone.clone(),
                            )
                            .await
                            {
                                // Execute sell phase
                                execute_sell_phase(
                                    trade_state,
                                    &client,
                                    &wallet,
                                    &jito_sdk,
                                    jito_tip_account,
                                    my_wallet_trading_clone,
                                )
                                .await;
                            }

                            // Cleanup
                            {
                                let mut active_trades = active_trades.lock().unwrap();
                                active_trades.remove_trade(
                                    &token_mint_clone,
                                    target_wallet.to_string().as_str(),
                                );
                            }
                            // Explicitly release the thread slot
                            drop(permit);
                            // info!("Trade thread completed for token: {}", token_mint_clone);
                        });
                    }
                }
            }
        }
    }
}

async fn execute_sell_phase(
    mut trade_state: TradeState,
    client: &RpcClient,
    wallet: &WalletContext,
    jito_sdk: &JitoJsonRpcSDK,
    jito_tip_account: Pubkey,
    my_wallet_trading: Arc<AtomicBool>,
) {
    let target_wallet = trade_state.target_wallet;
    let token_mint = trade_state.token_mint.clone();
    let pool_pubkey = trade_state.pool_pubkey;
    let bought_price = trade_state.bought_price;
    let bought_amount = trade_state.amount;
    let bought_time = trade_state.bought_time;

    info!(
        "Current trade state: target wallet: {}, token mint: {}, bought price: {:?} SOL, bought time: {:?}",
        target_wallet, token_mint, bought_price, bought_time
    );

    let pool_impl =
        get_raydium_pool_implementation(client, &pool_pubkey, &RaydiumPoolType::OpenBook).unwrap();
    let token_mint_pubkey = Pubkey::from_str(&token_mint).unwrap();
    let token_account = get_associated_token_address(&wallet.payer(), &token_mint_pubkey);
    let target_token_account = get_associated_token_address(&target_wallet, &token_mint_pubkey);
    // let mut target_sold_all = false;
    let mut total_sold_sol = 0.0;
    for cnt in 0.. {
        let current_price = pool_impl.get_price(client, &pool_pubkey).await;
        if let Err(e) = current_price {
            error!("Failed to get price: {:?}", e);
            sleep(Duration::from_secs(1)).await;
            continue;
        }
        let current_price = current_price.unwrap();
        let price_multiplier = current_price / bought_price;
        let elapsed_since_buy = SystemTime::now()
            .duration_since(bought_time)
            .unwrap_or_default();
        let elapsed = elapsed_since_buy.as_secs();
        if cnt % 100 == 0 {
            info!(
                "Price multiplier of {}: {:.2}x, Time since buy: {}s",
                token_mint, price_multiplier, elapsed,
            );
        }

        let current_balance = get_token_account_balance(client, &token_account);
        if let Err(e) = current_balance {
            error!("Failed to get token account balance: {:?}", e);
            sleep(Duration::from_secs(1)).await;
            continue;
        }
        let current_balance = current_balance.unwrap();
        if current_balance == 0 {
            info!("Already sold all");
            break;
        }

        let current_target_balance = get_token_account_balance(client, &target_token_account);
        if let Err(e) = current_target_balance {
            error!("Failed to get token account balance: {:?}", e);
            sleep(Duration::from_secs(1)).await;
            continue;
        }
        let current_target_balance = current_target_balance.unwrap();

        // Update target's highest balance if current is higher
        if current_target_balance > trade_state.target_highest_balance {
            trade_state.target_highest_balance = current_target_balance;
        }

        // Calculate target's balance ratio
        let target_balance_ratio = if trade_state.target_highest_balance > 0 {
            current_target_balance as f64 / trade_state.target_highest_balance as f64
        } else {
            1.0
        };
        if target_balance_ratio < trade_state.last_target_balance_ratio {
            info!(
                "Target balance ratio dropped from {:.2} to {:.2}",
                trade_state.last_target_balance_ratio, target_balance_ratio
            );
            trade_state.last_target_balance_ratio = target_balance_ratio;
        }

        // Check if we need to sell based on target's balance ratio
        if trade_state.target_highest_balance > 0 {
            // Find the next ratio threshold that was crossed
            let ratio_threshold = (target_balance_ratio * 10.0 + 0.5).ceil() / 10.0 - 0.1;
            if ratio_threshold < trade_state.last_balance_ratio_sell {
                let mut sell_percentage =
                    (trade_state.last_balance_ratio_sell - ratio_threshold) as f64;
                if trade_state.is_from_pump {
                    // target has lower cost and more token, we should sell faster
                    sell_percentage = f64::min(1.0, sell_percentage * 2.0);
                }
                info!(
                    "Target balance ratio dropped, selling {:.0}% of initial balance",
                    sell_percentage * 100.0
                );

                let mut sell_amount = ((bought_amount as f64 * sell_percentage).floor()) as u64;
                if sell_amount as f64 >= current_balance as f64 * 0.999 {
                    sell_amount = current_balance;
                }

                {
                    let _guard = acquire_trading_lock(&my_wallet_trading).await;
                    let swap_tx = pool_impl
                        .build_swap_transaction(
                            client,
                            wallet.signer(),
                            &pool_pubkey,
                            sell_amount,
                            0,
                            &token_mint_pubkey,
                            false,
                            &jito_tip_account,
                            400_000,
                            4_000_000,
                            0,
                        )
                        .await;
                    let swap_tx = match swap_tx {
                        Ok(tx) => tx,
                        Err(e) => {
                            error!("Error building swap transaction: {:?}", e);
                            continue;
                        }
                    };
                    let current_sol_balance = client.get_balance(&wallet.payer()).unwrap_or(0);
                    if let Ok(Some(uuid)) = send_tx_via_jito(&swap_tx, &jito_sdk).await {
                        info!("Sell order sent! Bundle UUID: {}", uuid);

                        // Wait for the sell to complete
                        let mut attempts = 0;
                        let max_attempts = 30;
                        loop {
                            if let Ok(balance) = client.get_token_account_balance(&token_account) {
                                if let Ok(balance) = balance.amount.parse::<u64>() {
                                    if balance < current_balance {
                                        trade_state.last_balance_ratio_sell = ratio_threshold;
                                        break;
                                    }
                                }
                            }
                            attempts += 1;
                            if attempts >= max_attempts {
                                error!(
                                    "Sell order did not complete after {} attempts",
                                    max_attempts
                                );
                                break;
                            }
                            sleep(Duration::from_secs(1)).await;
                        }
                        let new_sol_balance = client.get_balance(&wallet.payer()).unwrap_or(0);
                        if new_sol_balance > current_sol_balance {
                            let sold_sol = (new_sol_balance - current_sol_balance) as f64 / 1e9;
                            info!(
                                "Sold {} and got {:.2} SOL",
                                trade_state.token_mint, sold_sol,
                            );
                            total_sold_sol += sold_sol;
                        }
                        if ratio_threshold == 0.0 {
                            break;
                        }
                    }
                }
                continue;
            }
        }
        sleep(Duration::from_secs(1)).await;
    }
    info!(
        "Sold all {} and got {:.2} SOL. Cost {:.2} SOL. Profit: {:.1}%",
        trade_state.token_mint,
        total_sold_sol,
        trade_state.bought_sol,
        (total_sold_sol / trade_state.bought_sol - 1.0) * 100.0
    );

    if let Ok(mut wallet_store) = WalletStore::load("tracked_wallets.json") {
        let target = target_wallet.to_string();
        if let Err(e) = wallet_store.record_profit(&target, total_sold_sol - trade_state.bought_sol)
        {
            error!("Failed to record profit: {:?}", e);
        }
        if wallet_store.get_wallet_profit(&target) <= -1.0 * trade_state.bought_sol {
            info!("Wallet {} has reached loss threshold, removing it", target);
            if let Err(e) = wallet_store.remove_wallet(&target) {
                error!("Failed to remove wallet: {:?}", e);
            }
        }
    }
}
