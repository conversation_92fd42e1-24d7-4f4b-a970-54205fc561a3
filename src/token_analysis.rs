use crate::{browser_automation, wallet_transfer_analyzer::KNOWN_EXCLUSIONS};
use anyhow::{anyhow, Result};
use base64::{engine::general_purpose, Engine as _};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use solana_client::{
    rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient},
    rpc_config::RpcTransactionConfig,
};
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use solana_transaction_status::{
    option_serializer::OptionSerializer, EncodedConfirmedTransactionWithStatusMeta,
    EncodedTransaction, UiInstruction, UiMessage, UiTransactionEncoding,
};
use std::collections::HashMap;
use std::{process::Command, str::FromStr, time::Duration};
use tokio::sync::Mutex;
use tokio::time::sleep;

// Static mutex for analyze_token
static ANALYZE_TOKEN_MUTEX: Lazy<Mutex<()>> = Lazy::new(|| Mutex::new(()));

#[derive(Debug, Deserialize)]
pub struct TrendingResponse {
    pub data: TrendingData,
}

#[derive(Debug, Deserialize)]
pub struct TrendingData {
    pub rank: Vec<TrendingToken>,
}

#[derive(Debug, Deserialize)]
pub struct TrendingToken {
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub price: f64,
    pub volume_24h: f64,
    pub price_change_24h: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokenMintActivity {
    pub block_id: u64,
    pub trans_id: String,
    pub block_time: i64,
    pub activity_type: String,
    pub from_address: String,
    pub from_token_account: String,
    pub to_address: String,
    pub to_token_account: String,
    pub token_address: String,
    pub token_decimals: u8,
    pub amount: u64,
    pub flow: String,
    pub value: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenMintResponse {
    // pub code: i32,
    // pub msg: String,
    pub data: Vec<TokenMintActivity>,
}

#[derive(Debug)]
pub struct BuyRecord {
    pub source_address: String,
    pub sol_amount: f64,
    pub timestamp: i64,
}

/// Represents a system program transfer
pub struct SystemTransfer {
    pub from_address: String,
    pub to_address: String,
    pub sol_amount: f64,
    pub timestamp: i64,
}

/// Parse a transaction to extract all system program transfers
pub fn parse_system_transfers(
    tx: &EncodedConfirmedTransactionWithStatusMeta,
) -> Result<Vec<SystemTransfer>> {
    let transfers = Vec::new();

    let meta = match &tx.transaction.meta {
        Some(meta) => meta,
        None => return Ok(transfers),
    };

    let tx_json = match &tx.transaction.transaction {
        EncodedTransaction::Json(tx) => tx,
        _ => return Ok(transfers),
    };

    let msg = match &tx_json.message {
        UiMessage::Raw(msg) => msg,
        _ => return Ok(transfers),
    };

    let mut account_keys = msg.account_keys.clone();
    if let OptionSerializer::Some(loaded_addresses) = meta.loaded_addresses.as_ref() {
        account_keys.extend(loaded_addresses.writable.clone());
        account_keys.extend(loaded_addresses.readonly.clone());
    }

    let inner_ixs: &Vec<solana_transaction_status::UiInnerInstructions> =
        match &meta.inner_instructions {
            OptionSerializer::Some(ixs) => ixs,
            _ => return Ok(transfers),
        };

    let mut transfers = Vec::new();
    // Get block time from the transaction metadata
    // This is the time when the transaction was processed by the Solana blockchain
    let block_time = tx.block_time.unwrap_or(0);

    for inner_instruction_set in inner_ixs {
        for inner_ix in &inner_instruction_set.instructions {
            if let UiInstruction::Compiled(inner_cix) = inner_ix {
                let program_id_index = inner_cix.program_id_index as usize;

                // Check if it's the System Program (11111111111111111111111111111111)
                if account_keys
                    .get(program_id_index)
                    .map_or(false, |key| key == "11111111111111111111111111111111")
                {
                    if inner_cix.accounts.len() >= 2 {
                        // Get the source and destination account indices
                        let source_account_idx = inner_cix.accounts[0] as usize;
                        let dest_account_idx = inner_cix.accounts[1] as usize;

                        let source = account_keys
                            .get(source_account_idx)
                            .cloned()
                            .unwrap_or_default();
                        let destination = account_keys
                            .get(dest_account_idx)
                            .cloned()
                            .unwrap_or_default();
                        let balance_diff = meta.pre_balances[source_account_idx]
                            .checked_sub(meta.post_balances[source_account_idx])
                            .unwrap_or(0);

                        // Extract lamports from data (next 8 bytes after instruction index)
                        if let Ok(decoded_data) = general_purpose::STANDARD.decode(&inner_cix.data)
                        {
                            if decoded_data.len() == 12 {
                                let sol_amount = balance_diff as f64 / 1_000_000_000.0; // Convert lamports to SOL

                                transfers.push(SystemTransfer {
                                    from_address: source,
                                    to_address: destination,
                                    sol_amount,
                                    timestamp: block_time,
                                });
                            }
                        }
                    }
                }
            }
        }
    }

    Ok(transfers)
}

/// Get trending tokens from GMGN API
pub async fn get_trending_tokens(is_24h: bool) -> Result<Vec<TrendingToken>> {
    let url = if is_24h {
        "https://gmgn.ai/defi/quotation/v1/rank/sol/swaps/24h?device_id=0d89bab0-6361-41dd-a41b-88f7e715e19f&client_id=gmgn_web_2025.0128.214338&from_app=gmgn&app_ver=2025.0128.214338&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en&orderby=volume&direction=desc&filters[]=renounced&filters[]=frozen&min_marketcap=30000&min_volume=500000&max_created=24h"
    } else {
        "https://gmgn.ai/defi/quotation/v1/rank/sol/swaps/6h?device_id=0d89bab0-6361-41dd-a41b-88f7e715e19f&client_id=gmgn_web_2025.0128.214338&from_app=gmgn&app_ver=2025.0128.214338&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en&orderby=volume&direction=desc&filters[]=renounced&filters[]=frozen&min_marketcap=30000&min_volume=500000&max_created=24h"
    };

    let mut retries = 0;
    let max_retries = 10;

    while retries < max_retries {
        let output = Command::new("curl")
        .arg(url)
        .arg("-H")
        .arg("accept: application/json, text/plain, */*")
        .arg("-H")
        .arg("accept-language: en-US,en;q=0.9")
        .arg("-H")
        .arg("cache-control: no-cache")
        .arg("-H")
        .arg("dnt: 1")
        .arg("-H")
        .arg("pragma: no-cache")
        .arg("-H")
        .arg("priority: u=1, i")
        .arg("-H")
        .arg("referer: https://gmgn.ai/sol/token/BmXfbamFqrBzrqihr9hbSmEsfQUXMVaqshAjgvZupump")
        .arg("-H")
        .arg("user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        .arg("-H")
        .arg("cookie: _ga=GA1.1.1867644676.1736254604; sid=gmgn%7C2b760f17083b5728ea668c6ca91b37f7; _ga_UGLVBMV4Z0=GS1.2.1737366215762198.7ace014211b366a86efe8ebe7b206400.czsx9giQxCjMQQ4RfG9F5A%3D%3D.zM%2FsNX5mYOmGlGQpJh0kxA%3D%3D.%2BuJXXzT84nG6fQHh77gJeQ%3D%3D.6Au63LezaBeZ0wGXhS9thQ%3D%3D; _ga_0XM0LYXGC8=deleted; __cf_bm=Dxpg3LXip.fPH3QQ5CCDFTienYE5bJcK69O5rxHgIFk-1741428545-*******-SU7H5AuDWsWGcHL.QtcAgkKhukNUX_ZGJPvIWsh46kBuUZ1RtHVrz2JnoTHI62Lyb5Ffk5qKsbuY5WMVTbHus3wr.GwATAHISQ.QSagR78E; _ga_0XM0LYXGC8=GS1.1.1741413554.317.1.1741428697.0.0.0; cf_clearance=uzuQgwpZc8S7BmmSmHxYkKzgI3ien3eyU6PIwWzn5Q8-1741428697-*******-zYFQyLwV73.z_QPD8_eJ_dFPX2Rs_yYCrN8bBoG24TMDU6x7S1xYWOVUWZEGylgg_KPV0mVWMU1VpNBPFPeViFq1.GLghqwIX_jj_RINhzh6vw8XsbAr0qONQDBzxlwwQKkokZ0F4xN2eP3TqrkaVdayQa3Y.ksrwPYizi5_EbkWOHQu1.QSaVDdgT4SdbYROidX4c1_JPgzbX9yxxfQUOnJYIEMvheT_rWbS_68T..tCzcUxdfNYylg43bTxezc.5e87.5bItY0jVpti4j8iOICvhrGG5Hb3q.5tgOWdYyhJvS.kjqjJq2wRv25J81vPRV6lZNWC1FHtWbEeqzQS52w4WRNE_bhgydh2iwrJlPwgVUPUBye2k47a6u3_D_5TtI3Qid9PHQ71ZbYpTDuoauqgbv5aYoexdqPhDo8DYY6a0WXlCphBunPuqID9LHGjuY9p9_oJYnITMcOUi7XvXCTA43AbG1VeM6tGtu2rlQ")
        .output()?;

        let text = String::from_utf8(output.stdout)?;
        match serde_json::from_str::<TrendingResponse>(&text) {
            Ok(resp) => return Ok(resp.data.rank),
            Err(e) => {
                retries += 1;
                if retries >= max_retries {
                    return Err(anyhow!(e));
                }
                sleep(Duration::from_secs(2)).await;
            }
        }
    }
    Err(anyhow!("Failed to get trending tokens"))
}

/// Get token mint activity from Solscan API
pub async fn get_token_mint_activity(token_address: &str) -> Result<Option<TokenMintActivity>> {
    let url = format!(
        "https://api-v2.solscan.io/v2/token/transfer?address={}&page=1&page_size=10&exclude_amount_zero=false&activity_type\\[\\]=ACTIVITY_SPL_MINT",
        token_address
    );

    // Get the cf_clearance token using the file-based caching system
    let mut cf_clearance = crate::browser_automation::get_cf_clearance().await?;

    // Get the user agent associated with the token
    let user_agent = crate::browser_automation::get_user_agent()?;

    let mut retries = 0;
    let max_retries = 5;

    while retries < max_retries {
        let output = Command::new("curl")
            .arg(&url)
            .arg("-H")
            .arg("accept: application/json, text/plain, */*")
            .arg("-H")
            .arg("accept-language: en-US,en;q=0.9")
            .arg("-H")
            .arg("cache-control: no-cache")
            .arg("-b")
            .arg(format!("_ga=GA1.1.706609179.1734405055; cf_clearance={}; _ga_PS3V7B7KV0=GS1.1.1741680506.212.1.1741684457.0.0.0", cf_clearance.cf_clearance))
            .arg("-H")
            .arg("dnt: 1")
            .arg("-H")
            .arg("origin: https://solscan.io")
            .arg("-H")
            .arg("pragma: no-cache")
            .arg("-H")
            .arg("priority: u=1, i")
            .arg("-H")
            .arg("referer: https://solscan.io/")
            .arg("-H")
            .arg("sec-ch-ua: \"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\"")
            .arg("-H")
            .arg("sec-ch-ua-mobile: ?0")
            .arg("-H")
            .arg("sec-ch-ua-platform: \"macOS\"")
            .arg("-H")
            .arg("sec-fetch-dest: empty")
            .arg("-H")
            .arg("sec-fetch-mode: cors")
            .arg("-H")
            .arg("sec-fetch-site: same-site")
            .arg("-H")
            .arg("sol-aut: DLfwOihLG2opSgqxE=XYcUPsB9dls0fKkJr0YQFu")
            .arg("-H")
            .arg(format!("user-agent: {}", user_agent))
            .output()?;

        let text = String::from_utf8(output.stdout)?;

        // Check if the response contains Cloudflare challenge or error
        if text.contains("cf-error")
            || text.contains("challenge")
            || text.contains("captcha")
            || text.contains("Access denied")
        {
            println!("Cloudflare challenge detected or token rejected. Refreshing token...");

            // Force refresh the token
            if retries < max_retries - 1 {
                // Only try to refresh if we have retries left
                cf_clearance = crate::browser_automation::force_refresh_cf_clearance().await?;
                retries += 1;
                println!("Token refreshed, retrying request...");
                continue;
            }
        }

        match serde_json::from_str::<TokenMintResponse>(&text) {
            Ok(resp) => {
                if resp.data.is_empty() {
                    return Ok(None);
                }
                return Ok(Some(resp.data[0].clone()));
            }
            Err(e) => {
                println!("Error parsing response: {}", e);
                println!("Response text: {}", text);

                // If we get a parsing error, it might be due to an invalid token
                // Try to refresh the token if we have retries left
                if retries < max_retries - 1 {
                    println!("Attempting to refresh Cloudflare token...");
                    cf_clearance = crate::browser_automation::force_refresh_cf_clearance().await?;
                }

                retries += 1;
                if retries >= max_retries {
                    return Err(anyhow!(e));
                }
                sleep(Duration::from_secs(1)).await;
            }
        }
    }
    Err(anyhow!("Failed to get token mint activity"))
}

/// Get all transactions for an account and analyze them for buy records
pub async fn analyze_pump_transactions(
    client: &RpcClient,
    bonding_curve_address: &str,
) -> Result<Vec<BuyRecord>> {
    let bonding_curve_pubkey = Pubkey::from_str(bonding_curve_address)?;
    let mut buy_records = Vec::new();
    let mut before_sig: Option<Signature> = None;

    // Fetch all transactions
    let mut processed_txs = 0;
    loop {
        if processed_txs >= 20 {
            break;
        }
        let mut retries = 0;
        let signatures = loop {
            let config = GetConfirmedSignaturesForAddress2Config {
                before: before_sig,
                until: None,
                limit: Some(20),
                commitment: Some(CommitmentConfig::confirmed()),
            };

            match client.get_signatures_for_address_with_config(&bonding_curve_pubkey, config) {
                Ok(sigs) => {
                    break sigs;
                }
                Err(e) => {
                    if retries >= 2 {
                        // Try 3 times total
                        return Err(e.into());
                    }
                    retries += 1;
                    continue;
                }
            }
        };

        // Set before signature for next batch
        if signatures.len() > 0 {
            before_sig = Some(Signature::from_str(&signatures.last().unwrap().signature)?);
        }

        // Process each transaction
        for sig_info in &signatures {
            if processed_txs >= 20 {
                break;
            }
            if let Some(block_time) = sig_info.block_time {
                if sig_info.err.is_some() {
                    continue;
                }
                processed_txs += 1;
                let sig = Signature::from_str(&sig_info.signature)?;
                let mut retries = 0;
                let tx = loop {
                    match client.get_transaction_with_config(
                        &sig,
                        RpcTransactionConfig {
                            commitment: Some(CommitmentConfig::confirmed()),
                            encoding: Some(UiTransactionEncoding::Json),
                            max_supported_transaction_version: Some(0),
                        },
                    ) {
                        Ok(tx) => break tx,
                        Err(e) => {
                            if retries >= 2 {
                                return Err(e.into());
                            }
                            retries += 1;
                            sleep(Duration::from_millis(300)).await;
                            continue;
                        }
                    }
                };

                // Use the parse_system_transfers function to extract transfers
                let transfers = parse_system_transfers(&tx)?
                    .into_iter()
                    .filter(|transfer| transfer.to_address == bonding_curve_address)
                    .collect::<Vec<_>>();
                if transfers.len() > 1 {
                    // Filter multi transfers to the bonding curve address
                    for transfer in transfers {
                        buy_records.push(BuyRecord {
                            source_address: transfer.from_address,
                            sol_amount: transfer.sol_amount,
                            timestamp: block_time,
                        });
                    }
                }
            }
        }

        // If we got less than the limit, we've reached the end
        if signatures.len() < 20 {
            break;
        }
    }

    // Sort by timestamp (newest first)
    buy_records.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

    Ok(buy_records)
}

/// Check if a token has significant buy activity in a short time window
pub fn has_significant_buy_activity(
    buy_records: &[BuyRecord],
    window_minutes: i64,
    threshold_sol: f64,
) -> bool {
    if buy_records.is_empty() {
        return false;
    }

    let window_seconds = window_minutes * 60;

    for (i, record) in buy_records.iter().enumerate() {
        let mut window_sol = record.sol_amount;
        let window_start = record.timestamp;
        let window_end = window_start + window_seconds;

        // Sum up all buys within the time window
        for j in (i + 1)..buy_records.len() {
            let next_record = &buy_records[j];
            if next_record.timestamp <= window_end {
                window_sol += next_record.sol_amount;
            } else {
                break;
            }
        }

        if window_sol >= threshold_sol {
            return true;
        }
    }

    false
}

/// Filter tokens to only include those with "pump" in the address
pub fn filter_pump_tokens(tokens: Vec<TrendingToken>) -> Vec<TrendingToken> {
    tokens
        .into_iter()
        .filter(|token| token.address.to_lowercase().ends_with("pump"))
        .collect()
}

pub async fn get_trending_tokens_gmgn() -> Result<Vec<TrendingToken>> {
    let url = "https://gmgn.ai/defi/quotation/v1/rank/sol/swaps/6h?device_id=0d89bab0-6361-41dd-a41b-88f7e715e19f&client_id=gmgn_web_2025.0128.214338&from_app=gmgn&app_ver=2025.0128.214338&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en&orderby=volume&direction=desc&min_marketcap=30000&min_volume=200000&max_created=48h&filters[]=renounced&filters[]=frozen";

    // Get Cloudflare token for gmgn.ai
    let cf_token = match browser_automation::get_cf_clearance_gmgn().await {
        Ok(token) => token,
        Err(e) => {
            println!("Failed to get Cloudflare token for gmgn.ai: {}", e);
            return Err(anyhow!("Failed to get Cloudflare token"));
        }
    };

    let output = Command::new("curl")
        .arg(&url)
        .arg("-H")
        .arg("accept: application/json, text/plain, */*")
        .arg("-H")
        .arg("accept-language: en-US,en;q=0.9")
        .arg("-H")
        .arg("cache-control: no-cache")
        .arg("-H")
        .arg("dnt: 1")
        .arg("-H")
        .arg("pragma: no-cache")
        .arg("-H")
        .arg("priority: u=1, i")
        .arg("-H")
        .arg("referer: https://gmgn.ai/sol/token/BmXfbamFqrBzrqihr9hbSmEsfQUXMVaqshAjgvZupump")
        .arg("-H")
        .arg("sec-ch-ua: \"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"")
        .arg("-H")
        .arg("sec-ch-ua-mobile: ?0")
        .arg("-H")
        .arg("sec-ch-ua-platform: \"macOS\"")
        .arg("-H")
        .arg("sec-fetch-dest: empty")
        .arg("-H")
        .arg("sec-fetch-mode: cors")
        .arg("-H")
        .arg("sec-fetch-site: same-origin")
        .arg("-H")
        .arg(format!("user-agent: {}", cf_token.user_agent))
        .arg("-b")
        .arg(format!("_ga=GA1.1.2039738004.1741880928; cf_clearance={}; _ga_0XM0LYXGC8=GS1.1.1743928203.89.1.1743929262.0.0.0", cf_token.cf_clearance))
        .output()?;

    let text = String::from_utf8(output.stdout)?;
    let resp: TrendingResponse = serde_json::from_str(&text)?;
    Ok(resp.data.rank)
}

pub async fn get_wallet_holdings_gmgn(wallet_address: &str) -> Result<Vec<GmgnTokenHolding>> {
    let url = format!("https://gmgn.ai/defi/quotation/v1/tokens/wallet_holdings/sol/{}?device_id=934bb6e3-adb2-494e-aad0-99da8d0b6661&client_id=gmgn_web_2025.0404.173751&from_app=gmgn&app_ver=2025.0404.173751&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en-US&fp_did=02fba8305f31a8f7a683c8c56dd1015d&os=web&type=spl", wallet_address);

    // Get Cloudflare token for gmgn.ai
    let cf_token = match browser_automation::get_cf_clearance_gmgn().await {
        Ok(token) => token,
        Err(e) => {
            println!("Failed to get Cloudflare token for gmgn.ai: {}", e);
            return Err(anyhow!("Failed to get Cloudflare token"));
        }
    };

    let output = Command::new("curl")
        .arg(&url)
        .arg("-H")
        .arg("accept: application/json, text/plain, */*")
        .arg("-H")
        .arg("accept-language: en-US,en;q=0.9")
        .arg("-H")
        .arg("cache-control: no-cache")
        .arg("-H")
        .arg("dnt: 1")
        .arg("-H")
        .arg("pragma: no-cache")
        .arg("-H")
        .arg("priority: u=1, i")
        .arg("-H")
        .arg(format!("referer: https://gmgn.ai/sol/address/{}", wallet_address))
        .arg("-H")
        .arg("sec-ch-ua: \"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"")
        .arg("-H")
        .arg("sec-ch-ua-mobile: ?0")
        .arg("-H")
        .arg("sec-ch-ua-platform: \"macOS\"")
        .arg("-H")
        .arg("sec-fetch-dest: empty")
        .arg("-H")
        .arg("sec-fetch-mode: cors")
        .arg("-H")
        .arg("sec-fetch-site: same-origin")
        .arg("-H")
        .arg(format!("user-agent: {}", cf_token.user_agent))
        .arg("-b")
        .arg(format!("_ga=GA1.1.2039738004.1741880928; cf_clearance={}; _ga_0XM0LYXGC8=GS1.1.1743928203.89.1.1743929262.0.0.0", cf_token.cf_clearance))
        .output()?;

    let text = String::from_utf8(output.stdout)?;
    let resp: GmgnWalletHoldingsResponse = serde_json::from_str(&text)?;
    Ok(resp.data.holdings)
}

#[derive(Debug, Deserialize)]
pub struct GmgnWalletHoldingsResponse {
    pub data: GmgnWalletHoldingsData,
}

#[derive(Debug, Deserialize)]
pub struct GmgnWalletHoldingsData {
    pub holdings: Vec<GmgnTokenHolding>,
}

#[derive(Debug, Deserialize)]
pub struct GmgnTokenHolding {
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub amount: f64,
    pub price: f64,
    pub value: f64,
}

#[derive(Debug, Serialize)]
pub struct TokenAnalysisResponse {
    pub liquidity_pools: CategoryStats,
    pub cex: CategoryStats,
    pub individual_traders: CategoryStats,
    pub kol_traders: CategoryStats,
    pub suspect_insiders: CategoryStats,
    pub others: CategoryStats,
    pub suspect_insiders_sol_balance: f64,
}

#[derive(Debug, Serialize)]
pub struct CategoryStats {
    pub total_percentage: f64,
    pub addresses: Vec<String>,
}

// #[derive(Debug, Deserialize)]
// struct ApiResponse {
//     data: ApiData,
// }

// #[derive(Debug, Deserialize)]
// struct ApiData {
//     list: Vec<HolderInfo>,
// }

#[derive(Debug, Deserialize, Clone)]
struct HolderInfo {
    address: String,
    name: Option<String>,
    addr_type: i32,
    tags: Option<Vec<String>>,
    maker_token_tags: Option<Vec<String>>,
    amount_percentage: f64,
    is_suspicious: bool,
    _profit: Option<f64>,
}

pub async fn analyze_token(token: &str) -> Result<TokenAnalysisResponse> {
    // Acquire the mutex lock
    let _guard = ANALYZE_TOKEN_MUTEX.lock().await;

    // Run the Python script to get token data
    let output = Command::new("python3")
        .arg("scripts/debug_token_analysis.py")
        .arg(token)
        .arg("clean")
        .output()?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(anyhow!("Failed to run Python script: {}", stderr));
    }

    let stdout = String::from_utf8_lossy(&output.stdout);
    let data: serde_json::Value = serde_json::from_str(&stdout)?;

    // Check for errors
    if let Some(error) = data.get("error") {
        return Err(anyhow!("Error from Python script: {}", error));
    }

    // Convert the data into Vec<HolderInfo>
    let mut all_holders = Vec::new();

    // Process traders data, at most 80
    if let Some(traders) = data.get("traders").and_then(|t| t.as_array()) {
        for trader in traders.iter().take(80) {
            if let Ok(holder) = serde_json::from_value::<HolderInfo>(trader.clone()) {
                all_holders.push(holder);
            }
        }
    }

    // Process holders data, at most 80
    if let Some(holders) = data.get("holders").and_then(|h| h.as_array()) {
        for holder in holders.iter().take(80) {
            if let Ok(holder) = serde_json::from_value::<HolderInfo>(holder.clone()) {
                all_holders.push(holder);
            }
        }
    }

    // Process kol data, at most 10
    if let Some(holders) = data.get("kol_holders").and_then(|h| h.as_array()) {
        for holder in holders.iter().take(10) {
            if let Ok(holder) = serde_json::from_value::<HolderInfo>(holder.clone()) {
                all_holders.push(holder);
            }
        }
    }

    // Process devs data
    if let Some(devs) = data.get("devs").and_then(|d| d.as_array()) {
        for dev in devs {
            if let Ok(holder) = serde_json::from_value::<HolderInfo>(dev.clone()) {
                all_holders.push(holder);
            }
        }
    }

    // Use a HashMap to track unique holders and their highest percentage
    let mut unique_holders: HashMap<String, HolderInfo> = HashMap::new();

    // Process all holders
    for holder in all_holders {
        unique_holders
            .entry(holder.address.clone())
            .and_modify(|existing| {
                // Keep the holder with the highest percentage
                if holder.amount_percentage > existing.amount_percentage {
                    *existing = holder.clone();
                }
            })
            .or_insert(holder);
    }

    // Convert back to Vec for further processing
    let all_holders: Vec<HolderInfo> = unique_holders.into_values().collect();

    // Initial categorization
    let mut analysis = TokenAnalysisResponse {
        liquidity_pools: CategoryStats {
            total_percentage: 0.0,
            addresses: Vec::new(),
        },
        cex: CategoryStats {
            total_percentage: 0.0,
            addresses: Vec::new(),
        },
        individual_traders: CategoryStats {
            total_percentage: 0.0,
            addresses: Vec::new(),
        },
        kol_traders: CategoryStats {
            total_percentage: 0.0,
            addresses: Vec::new(),
        },
        suspect_insiders: CategoryStats {
            total_percentage: 0.0,
            addresses: Vec::new(),
        },
        others: CategoryStats {
            total_percentage: 0.0,
            addresses: Vec::new(),
        },
        suspect_insiders_sol_balance: 0.0,
    };

    // First pass: categorize all addresses
    for holder in &all_holders {
        if KNOWN_EXCLUSIONS.contains(&holder.address.as_str()) {
            continue;
        }
        let category = categorize_address(holder);
        let stats = match category {
            "liquidity_pool" => &mut analysis.liquidity_pools,
            "cex" => &mut analysis.cex,
            "individual_trader" => &mut analysis.individual_traders,
            "kol" => &mut analysis.kol_traders,
            "suspicious_insider" => &mut analysis.suspect_insiders,
            _ => &mut analysis.others,
        };

        stats.addresses.push(holder.address.clone());
        stats.total_percentage += holder.amount_percentage * 100.0;
    }

    // Get addresses for suspect insider analysis
    let suspect_candidates: Vec<String> = analysis
        .others
        .addresses
        .iter()
        .chain(analysis.suspect_insiders.addresses.iter())
        .chain(analysis.individual_traders.addresses.iter())
        .filter(|h| !KNOWN_EXCLUSIONS.contains(&h.as_str()))
        .map(|h| h.clone())
        .collect();

    // Initialize RPC client for SOL balance checks
    let rpc_url =
        std::env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
    let client = RpcClient::new(rpc_url.clone());
    let mut suspect_insiders_sol_balance = 0.0;

    // Run cluster analysis for suspect insiders
    if !suspect_candidates.is_empty() {
        let clusters = crate::wallet_transfer_analyzer::analyze_wallet_clusters(
            &suspect_candidates,
            token,
            1,    // max_pages
            1,    // min_transfers
            true, // include_one_way
            1,    // min_one_way_transfers
            true, // include_token_creators
        )
        .await?;

        // Add all addresses from clusters to suspect insiders
        for (i, cluster) in clusters.iter().enumerate() {
            log::info!(
                "Suspect insider cluster {}: {} addresses",
                i + 1,
                cluster.len()
            );

            // Calculate individual wallet balances using existing holder data
            for wallet in cluster {
                if let Some(holder) = all_holders.iter().find(|h| h.address == *wallet) {
                    let percentage = holder.amount_percentage * 100.0;
                    log::info!("  {}: {:.4}% of supply", wallet, percentage);

                    // If this wallet was in individual_traders, remove it
                    if let Some(pos) = analysis
                        .individual_traders
                        .addresses
                        .iter()
                        .position(|x| x == wallet)
                    {
                        analysis.individual_traders.addresses.remove(pos);
                        analysis.individual_traders.total_percentage -= percentage;
                    }

                    if !analysis.suspect_insiders.addresses.contains(wallet) {
                        analysis.suspect_insiders.addresses.push(wallet.clone());
                        analysis.suspect_insiders.total_percentage += percentage;
                    }
                }
            }

            // Calculate and display total cluster balance
            let cluster_percentage: f64 = cluster
                .iter()
                .filter_map(|wallet| {
                    all_holders
                        .iter()
                        .find(|h| h.address == *wallet)
                        .map(|h| h.amount_percentage * 100.0)
                })
                .sum();

            log::info!(
                "  Total cluster balance: {:.4}% of supply",
                cluster_percentage
            );
            log::info!("");
        }

        // Get SOL balances for all suspect insider addresses
        let suspect_wallets: Vec<Pubkey> = analysis
            .suspect_insiders
            .addresses
            .iter()
            .filter_map(|addr| Pubkey::from_str(addr).ok())
            .collect();

        if !suspect_wallets.is_empty() {
            let sol_balances =
                crate::token_utils::get_multiple_wallet_balances(&client, &suspect_wallets).await?;

            // Print wallets with most SOL
            let mut wallet_balances: Vec<(&Pubkey, &u64)> = sol_balances.iter().collect();
            wallet_balances.sort_by(|a, b| b.1.cmp(a.1)); // Sort by balance in descending order

            log::info!("Top suspect insider wallets by SOL balance:");
            for (i, (wallet, balance)) in wallet_balances.iter().take(5).enumerate() {
                let sol_amount = **balance as f64 / 1e9; // Convert lamports to SOL
                log::info!("  {}. {}: {:.4} SOL", i + 1, wallet, sol_amount);
            }

            suspect_insiders_sol_balance = sol_balances.values().sum::<u64>() as f64 / 1e9;
            // Convert lamports to SOL
        }
    }

    // Calculate others percentage
    analysis.others.total_percentage = 100.0
        - analysis.liquidity_pools.total_percentage
        - analysis.cex.total_percentage
        - analysis.kol_traders.total_percentage
        - analysis.individual_traders.total_percentage
        - analysis.suspect_insiders.total_percentage;

    Ok(TokenAnalysisResponse {
        liquidity_pools: analysis.liquidity_pools,
        cex: analysis.cex,
        individual_traders: analysis.individual_traders,
        kol_traders: analysis.kol_traders,
        suspect_insiders: analysis.suspect_insiders,
        others: analysis.others,
        suspect_insiders_sol_balance,
    })
}

fn categorize_address(info: &HolderInfo) -> &'static str {
    // Check for liquidity pools
    if info.addr_type == 2
        || info.name.as_ref().map_or(false, |n| {
            n.to_lowercase().contains("raydium") || n.to_lowercase().contains("meteora")
        })
    {
        return "liquidity_pool";
    }

    // Check for CEX
    if info.address == "9ZPsRWGkukYeWg2Z7eZ8NaTBZ1DSuBUVzLcGQWZgE4Y4" {
        return "cex";
    }
    if let Some(name) = &info.name {
        let name_lower = name.to_lowercase();
        if name_lower.contains("mexc")
            || name_lower.contains("bitget")
            || name_lower.contains("binance")
            || name_lower.contains("gate")
            || name_lower.contains("kraken")
            || name_lower.contains("okx")
            || name_lower.contains("coinbase")
            || name_lower.contains("bybit")
        {
            return "cex";
        }
    }

    if info.is_suspicious {
        return "suspicious_insider";
    }
    if let Some(tags) = &info.maker_token_tags {
        if tags.iter().any(|t| t == "rat_trader") {
            return "suspicious_insider";
        }
    }

    // Check for individual traders
    if let Some(tags) = &info.tags {
        if tags.iter().any(|t| t == "kol") {
            return "kol";
        }
        if tags.iter().any(|t| {
            t == "gmgn"
                || t == "trojan"
                || t == "photon"
                || t == "pepeboost"
                || t == "bluechip_owner"
                || t == "bullx"
        }) {
            return "individual_trader";
        }
    }

    "other"
}

// async fn fetch_data(url: &str) -> Result<Vec<HolderInfo>> {
//     let cf_token = browser_automation::get_cf_clearance_gmgn().await?;

//     let token = url
//         .split('/')
//         .last()
//         .unwrap_or("")
//         .split('?')
//         .next()
//         .unwrap_or("");

//     let output = Command::new("curl")
//         .arg(url)
//         .arg("-H")
//         .arg("accept: application/json, text/plain, */*")
//         .arg("-H")
//         .arg("accept-language: zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-CN;q=0.6")
//         .arg("-H")
//         .arg("baggage: sentry-environment=production,sentry-release=20250508-823-3ce2338,sentry-public_key=93c25bab7246077dc3eb85b59d6e7d40,sentry-trace_id=73512053d681458eb97b2337ed57030a,sentry-sample_rate=0.01,sentry-sampled=false")
//         .arg("-H")
//         .arg("cache-control: no-cache")
//         .arg("-H")
//         .arg("dnt: 1")
//         .arg("-H")
//         .arg("pragma: no-cache")
//         .arg("-H")
//         .arg("priority: u=1, i")
//         .arg("-H")
//         .arg(format!("referer: https://gmgn.ai/sol/token/MVNsiiWE_{}", token))
//         .arg("-H")
//         .arg("sec-ch-ua: \"Not.A/Brand\";v=\"99\", \"Chromium\";v=\"136\"")
//         .arg("-H")
//         .arg("sec-ch-ua-arch: \"arm\"")
//         .arg("-H")
//         .arg("sec-ch-ua-bitness: \"64\"")
//         .arg("-H")
//         .arg("sec-ch-ua-full-version: \"136.0.7103.49\"")
//         .arg("-H")
//         .arg("sec-ch-ua-full-version-list: \"Not.A/Brand\";v=\"99.0.0.0\", \"Chromium\";v=\"136.0.7103.49\"")
//         .arg("-H")
//         .arg("sec-ch-ua-mobile: ?0")
//         .arg("-H")
//         .arg("sec-ch-ua-model: \"\"")
//         .arg("-H")
//         .arg("sec-ch-ua-platform: \"macOS\"")
//         .arg("-H")
//         .arg("sec-ch-ua-platform-version: \"15.4.1\"")
//         .arg("-H")
//         .arg("sec-fetch-dest: empty")
//         .arg("-H")
//         .arg("sec-fetch-mode: cors")
//         .arg("-H")
//         .arg("sec-fetch-site: same-origin")
//         .arg("-H")
//         .arg("sentry-trace: 53cde6ee1560433b82270cc99d7de912-aa1d8e7a1c174d93-0")
//         .arg("-H")
//         .arg(format!("user-agent: {}", cf_token.user_agent))
//         .arg("-b")
//         .arg(format!("GMGN_THEME=dark; _ga=GA1.1.1860846390.1745854891; GMGN_LOCALE=zh-TW; GMGN_CHAIN=sol; __cf_bm=qO8m.kIGts__.3F3FGGsVhu1e85Pma5LlSREm2RnOWM-1746699246-*******-OMCZ7TmEqtG9rDUPKzD2u25IxL0gK.aYuVhUsh0YSKmMO.iJtR_hyI2zQ97hZrn_myERAgYjLISh9LLZNjJCLykHgvnoB7H_2uf2eVWhMOU; cf_clearance={}; _ga_0XM0LYXGC8=GS2.1.s1746692864$o70$g1$t1746699896$j0$l0$h0; sid=gmgn%7Ca839a58b92f10a1fbf0a29640aef33f8; _ga_UGLVBMV4Z0=GS1.2.1746699911548880.7ace014211b366a86efe8ebe7b206400.PqJC%2BojFDGEYNqYUEyYLvQ%3D%3D.X%2BOhBFPmaUr4WpwoeT8JEQ%3D%3D.1eZu8XVjPcFIeBbAT0Vhcw%3D%3D.xko6JF4qCrOBjZuIi03UFw%3D%3D", cf_token.cf_clearance))
//         .output()?;

//     let text = String::from_utf8(output.stdout)?;
//     match serde_json::from_str::<ApiResponse>(&text) {
//         Ok(api_response) => Ok(api_response.data.list),
//         Err(e) => {
//             let preview = text.chars().take(50).collect::<String>();
//             eprintln!(
//                 "Error parsing response: {}. Response preview: {}",
//                 e, preview
//             );
//             Err(anyhow::anyhow!("Failed to parse API response: {}", e))
//         }
//     }
// }

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_system_transfers() {
        let client = RpcClient::new("https://api.mainnet-beta.solana.com".to_string());
        let sig = Signature::from_str("5xrhBVt5ty2RspyPL3ffvGLmbubgAnba1HyGSvZuMKm2oDjUDN7FrfPXpfRafcjotBAQNRVJtLsrijzxvzW2Mr5g").unwrap();
        let tx = client
            .get_transaction_with_config(
                &sig,
                RpcTransactionConfig {
                    commitment: Some(CommitmentConfig::confirmed()),
                    encoding: Some(UiTransactionEncoding::Json),
                    max_supported_transaction_version: Some(0),
                },
            )
            .unwrap();

        // Parse system transfers
        let transfers = parse_system_transfers(&tx).unwrap();
        for transfer in &transfers {
            println!(
                "From: {}, To: {}, Amount: {} SOL, Timestamp: {}",
                transfer.from_address, transfer.to_address, transfer.sol_amount, transfer.timestamp
            );
        }

        // Verify results
        assert_eq!(transfers.len(), 6);
        // assert_eq!(transfers[0].from_address, "sender_address");
        // assert_eq!(transfers[0].to_address, "receiver_address");
        // assert_eq!(transfers[0].timestamp, 1625097600);
    }
}
