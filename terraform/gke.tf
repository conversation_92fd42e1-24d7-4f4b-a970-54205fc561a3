resource "google_container_cluster" "solana_data" {
  name     = "solana-data-cluster"
  location = var.region
  project  = var.project_id

  # We create a minimal temporary default pool and immediately delete it
  # to allow for fully custom, separately managed node pools.
  remove_default_node_pool = true
  initial_node_count       = 1

  network    = google_compute_network.vpc.id
  subnetwork = google_compute_subnetwork.subnet.id

  # Security Best Practice: Use a private cluster configuration.
  private_cluster_config {
    enable_private_nodes    = true
    enable_private_endpoint = true # Set to true for a private control plane
    master_ipv4_cidr_block  = "***********/28"
  }

  master_authorized_networks_config {}

  # Use a stable release channel for a balance of features and stability.
  release_channel {
    channel = "REGULAR"
  }

  # Security Best Practice: Enable Network Policy to control pod-to-pod traffic.
  network_policy {
    enabled  = true
    provider = "CALICO"
  }

  ip_allocation_policy {
    cluster_secondary_range_name  = "pods"
    services_secondary_range_name = "services"
  }

  # Configure monitoring and logging for visibility.
  monitoring_config {
    # CORRECTED: "WORKLOADS" is deprecated for monitoring_config and was removed in GKE 1.24.
    # Managed Prometheus handles workload metrics.
    enable_components = ["SYSTEM_COMPONENTS"]
    managed_prometheus {
      enabled = true
    }
  }

  logging_config {
    # "WORKLOADS" remains a valid option for logging_config.
    enable_components = ["SYSTEM_COMPONENTS", "WORKLOADS"]
  }

  # Security Best Practice: Use Workload Identity for secure access to Google APIs.
  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }
}

# -----------------------------------------------------------------------------
# GKE NODE POOL (SECURE CONFIGURATION)
# -----------------------------------------------------------------------------

resource "google_container_node_pool" "primary_nodes" {
  name       = "default-pool"
  location   = var.region
  cluster    = google_container_cluster.solana_data.name
  project    = var.project_id

  autoscaling {
    min_node_count  = 0
    max_node_count  = 8
    location_policy = "BALANCED"
  }

  management {
    auto_repair  = true
    auto_upgrade = true
  }

  upgrade_settings {
    max_surge = 1
    strategy  = "SURGE"
  }

  node_config {
    machine_type = "e2-standard-4"
    disk_size_gb = 100
    disk_type    = "pd-balanced"
    image_type   = "COS_CONTAINERD"

    # Security Best Practice: Use least-privilege OAuth scopes.
    # NOTE: It is highly recommended to create and use a dedicated service account
    # instead of the Compute Engine default service account.
    oauth_scopes = [
      "https://www.googleapis.com/auth/devstorage.read_only",
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
      "https://www.googleapis.com/auth/service.management.readonly",
      "https://www.googleapis.com/auth/servicecontrol",
      "https://www.googleapis.com/auth/trace.append"
    ]

    # Security Best Practice: Enable Shielded Nodes for verifiable integrity.
    shielded_instance_config {
      enable_integrity_monitoring = true
    }

    # Required for Workload Identity.
    workload_metadata_config {
      mode = "GKE_METADATA"
    }

    resource_labels = {
      "goog-gke-node-pool-provisioning-model" = "on-demand"
    }

    kubelet_config {
      cpu_cfs_quota = false
      pod_pids_limit = 0
      cpu_manager_policy = "none"
    }
  }

  max_pods_per_node = 110
}

resource "google_project_service" "gke_apis" {
  project                    = var.project_id
  for_each                   = toset([
    "container.googleapis.com",
    "gkeconnect.googleapis.com",
    "gkehub.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "iam.googleapis.com"
  ])
  service                    = each.key
  disable_on_destroy         = false
}

resource "google_gke_hub_membership" "gke_membership" {
  project       = var.project_id
  membership_id = google_container_cluster.solana_data.name

  endpoint {
    gke_cluster {
      resource_link = "//container.googleapis.com/${google_container_cluster.solana_data.id}"
    }
  }

  depends_on = [google_container_cluster.solana_data]
}
