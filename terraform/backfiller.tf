# Yellowstone gRPC Backfiller IAM 配置
# 為 yellowstone-grpc backfiller 添加讀取 kg-old-faithful 儲存桶的權限
# 不再需要 Pub/Sub 權限，因為使用 gRPC 串流

# 綁定 GCS 讀取權限到現有的 GKE service account
resource "google_storage_bucket_iam_member" "yellowstone_backfill_gcs_reader" {
  bucket = "kg-old-faithful"
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:${google_service_account.gke_sa.email}"
}

# 添加 Storage Object Viewer 權限（用於讀取物件）
resource "google_project_iam_member" "yellowstone_backfill_storage_object_viewer" {
  project = var.project_id
  role    = "roles/storage.objectViewer"  
  member  = "serviceAccount:${google_service_account.gke_sa.email}"
}

# 添加監控寫入權限 (用於 Prometheus 指標)
resource "google_project_iam_member" "yellowstone_backfill_monitoring_writer" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${google_service_account.gke_sa.email}"
}

# 添加日誌寫入權限
resource "google_project_iam_member" "yellowstone_backfill_log_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.gke_sa.email}"
}

# 確保有足夠的 Kubernetes 權限來創建 Services
resource "google_project_iam_member" "yellowstone_backfill_k8s_developer" {
  project = var.project_id
  role    = "roles/container.developer"
  member  = "serviceAccount:${google_service_account.gke_sa.email}"
} 