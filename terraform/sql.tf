resource "google_sql_database_instance" "solana_data" {
  name             = "solana-data"
  database_version = "POSTGRES_17"
  region           = var.region
  project          = var.project_id

  depends_on = [google_service_networking_connection.private_vpc_connection]

  settings {
    edition = "ENTERPRISE_PLUS"
    tier    = "db-perf-optimized-N-8"
    ip_configuration {
      ipv4_enabled                                  = true
      private_network                               = google_compute_network.vpc.id
      enable_private_path_for_google_cloud_services = true
      require_ssl                                   = false
    }

    database_flags {
      name  = "log_lock_waits"
      value = "on"
    }

    database_flags {
      name  = "log_min_duration_statement"
      value = "2000"
    }
    
    database_flags {
      name  = "pg_stat_statements.max"
      value = "10000"
    }

    database_flags {
      name  = "pg_stat_statements.track"
      value = "all"
    }

    data_cache_config {
      data_cache_enabled = true
    }

    backup_configuration {
      enabled                        = true
      point_in_time_recovery_enabled = false
      start_time                     = "02:00"
    }

    maintenance_window {
      day          = 7
      hour         = 3
      update_track = "stable"
    }

    insights_config {
      query_insights_enabled  = true
      query_string_length     = 1024
      record_application_tags = true
      record_client_address   = true
    }
  }

  deletion_protection = true
}

resource "google_sql_database" "database" {
  name     = "solana_data"
  instance = google_sql_database_instance.solana_data.name
  project  = var.project_id
}

resource "google_sql_user" "user" {
  name     = "solana_data"
  instance = google_sql_database_instance.solana_data.name
  password = random_password.db_password.result
  project  = var.project_id
}

# Output the connection name for use in Kubernetes
output "instance_connection_name" {
  value = google_sql_database_instance.solana_data.connection_name
}
