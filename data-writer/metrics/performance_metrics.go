package metrics

import (
	"sync"
	"time"
)

const (
	// maxLatencySamples is the number of latency measurements to store for calculating averages.
	maxLatencySamples = 100
)

// PerformanceMetrics tracks performance metrics for the data writer
type PerformanceMetrics struct {
	// messageHistory stores recent messages with timestamps for rolling window calculations
	messageHistory []messageRecord
	// pubsubLatency stores the last N latency measurements for message acknowledges.
	pubsubLatency []time.Duration
	mu            sync.Mutex
}

// messageRecord stores a single message's metrics with timestamp
type messageRecord struct {
	timestamp  time.Time
	size       int
	eventType  string
	latency    time.Duration
	slotCount  int
	// New V2 event counts
	solV2Count        int
	tokenV2Count      int
	transactionCount  int
	solBalanceCount   int
	tokenBalanceCount int
}

// NewPerformanceMetrics initializes a new metrics struct.
func NewPerformanceMetrics() *PerformanceMetrics {
	return &PerformanceMetrics{
		messageHistory: make([]messageRecord, 0),
		pubsubLatency:  make([]time.Duration, 0, maxLatencySamples),
	}
}

// RecordMessage records metrics for a processed message.
func (m *PerformanceMetrics) RecordMessage(size int, eventType string, latency time.Duration, counts EventCounts) {
	m.mu.Lock()
	defer m.mu.Unlock()

	now := time.Now()
	record := messageRecord{
		timestamp:         now,
		size:              size,
		eventType:         eventType,
		latency:           latency,
		slotCount:         counts.SlotTimes,
		solV2Count:        counts.SolTransfersV2,
		tokenV2Count:      counts.TokenTransfersV2,
		transactionCount:  counts.Transactions,
		solBalanceCount:   counts.SolBalances,
		tokenBalanceCount: counts.TokenBalances,
	}

	// Add new record
	m.messageHistory = append(m.messageHistory, record)

	// Remove records older than 1 minute
	cutoff := now.Add(-1 * time.Minute)
	for i, record := range m.messageHistory {
		if record.timestamp.After(cutoff) {
			// Keep this record and all newer ones
			m.messageHistory = m.messageHistory[i:]
			break
		}
	}
}

// RecordPubSubLatency records the time from message receipt to acknowledgement.
func (m *PerformanceMetrics) RecordPubSubLatency(latency time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if len(m.pubsubLatency) >= maxLatencySamples {
		m.pubsubLatency = m.pubsubLatency[1:]
	}
	m.pubsubLatency = append(m.pubsubLatency, latency)
}

// GetStats calculates and returns the current performance statistics for the past 1 minute.
func (m *PerformanceMetrics) GetStats() map[string]interface{} {
	m.mu.Lock()
	defer m.mu.Unlock()

	now := time.Now()
	cutoff := now.Add(-1 * time.Minute)

	// Filter messages from the past 1 minute
	var recentMessages []messageRecord
	for _, record := range m.messageHistory {
		if record.timestamp.After(cutoff) {
			recentMessages = append(recentMessages, record)
		}
	}

	// Calculate totals for the past 1 minute
	var totalMessages int64
	var totalBytes int64
	var totalSlotTimes int64
	var totalSolV2Transfers int64
	var totalTokenV2Transfers int64
	var totalTransactions int64
	var totalSolBalances int64
	var totalTokenBalances int64
	processingLatency := make(map[string][]time.Duration)

	for _, record := range recentMessages {
		totalMessages++
		totalBytes += int64(record.size)
		totalSlotTimes += int64(record.slotCount)
		totalSolV2Transfers += int64(record.solV2Count)
		totalTokenV2Transfers += int64(record.tokenV2Count)
		totalTransactions += int64(record.transactionCount)
		totalSolBalances += int64(record.solBalanceCount)
		totalTokenBalances += int64(record.tokenBalanceCount)

		// Collect latencies by event type
		latencies := processingLatency[record.eventType]
		processingLatency[record.eventType] = append(latencies, record.latency)
	}

	stats := map[string]interface{}{
		"messages_last_minute":           totalMessages,
		"messages_per_second":            float64(totalMessages) / 60.0, // Average over 1 minute
		"bytes_last_minute":              totalBytes,
		"mb_per_second":                  float64(totalBytes) / (1024 * 1024) / 60.0,
		"slot_times_last_minute":         totalSlotTimes,
		"sol_transfers_v2_last_minute":   totalSolV2Transfers,
		"token_transfers_v2_last_minute": totalTokenV2Transfers,
		"transactions_last_minute":       totalTransactions,
		"sol_balances_last_minute":       totalSolBalances,
		"token_balances_last_minute":     totalTokenBalances,
	}

	// Calculate average processing latency per event type from the collected samples.
	for eventType, latencies := range processingLatency {
		if len(latencies) > 0 {
			var total time.Duration
			for _, l := range latencies {
				total += l
			}
			stats[eventType+"_avg_processing_latency_ms"] = float64(total.Milliseconds()) / float64(len(latencies))
		}
	}

	// Calculate average Pub/Sub acknowledge latency from the collected samples.
	if len(m.pubsubLatency) > 0 {
		var total time.Duration
		for _, l := range m.pubsubLatency {
			total += l
		}
		stats["pubsub_avg_ack_latency_ms"] = float64(total.Milliseconds()) / float64(len(m.pubsubLatency))
	}

	return stats
}

// EventCounts represents the count of each event type
type EventCounts struct {
	SlotTimes        int
	SolTransfersV2   int
	TokenTransfersV2 int
	Transactions     int
	SolBalances      int
	TokenBalances    int
}
