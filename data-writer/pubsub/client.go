package pubsub

import (
	"context"
	"os"
	"strconv"

	"cloud.google.com/go/pubsub"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

// Client wraps the Pub/Sub client with configuration
type Client struct {
	client       *pubsub.Client
	subscription *pubsub.Subscription
}

// NewClient creates a new Pub/Sub client with configured subscription
func NewClient(ctx context.Context, projectID, subscriptionID string) (*Client, error) {
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		return nil, err
	}

	subscription := client.Subscription(subscriptionID)

	// Configure concurrency and flow control settings
	configureSubscription(subscription)

	return &Client{
		client:       client,
		subscription: subscription,
	}, nil
}

// GetSubscription returns the configured subscription
func (c *Client) GetSubscription() *pubsub.Subscription {
	return c.subscription
}

// Close closes the Pub/Sub client
func (c *Client) Close() error {
	return c.client.Close()
}

// configureSubscription configures the subscription with concurrency and flow control settings
func configureSubscription(sub *pubsub.Subscription) {
	// Get concurrency settings from environment variables
	numGoroutines := getEnvIntOrDefault("PUBSUB_NUM_GOROUTINES", 1)
	maxOutstandingMessages := getEnvIntOrDefault("PUBSUB_MAX_OUTSTANDING_MESSAGES", 50)

	sub.ReceiveSettings.NumGoroutines = numGoroutines
	sub.ReceiveSettings.MaxOutstandingMessages = maxOutstandingMessages

	logger.Info(context.Background(), "Configured Pub/Sub subscription with %d goroutines and %d max outstanding messages",
		numGoroutines, maxOutstandingMessages)
}

// getEnvIntOrDefault gets an integer environment variable or returns a default value
func getEnvIntOrDefault(key string, defaultValue int) int {
	if envVal := os.Getenv(key); envVal != "" {
		if val, err := strconv.Atoi(envVal); err == nil && val > 0 {
			return val
		}
	}
	return defaultValue
}

// GetProjectID gets the project ID from environment variable
func GetProjectID() string {
	return getEnvOrDefault("GCP_PROJECT_ID", "kryptogo-wallet-data")
}

// GetSubscriptionID gets the subscription ID from environment variable
func GetSubscriptionID() string {
	return getEnvOrDefault("PUBSUB_SUBSCRIPTION_ID", "solana-events-sub")
}

// getEnvOrDefault gets an environment variable or returns a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
