//go:generate protoc --go_out=. --go_opt=paths=source_relative proto/events.proto

package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/handler"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"github.com/kryptogo/kg-solana-data/data-writer/metrics"
	"github.com/kryptogo/kg-solana-data/data-writer/processor"
	"github.com/kryptogo/kg-solana-data/data-writer/pubsub"
	"github.com/kryptogo/kg-solana-data/data-writer/receiver"
	"github.com/kryptogo/kg-solana-data/data-writer/server"
)

func main() {
	// Use a background context for initial setup.
	ctx := context.Background()

	// Initialize logger.
	if err := logger.Init(ctx, "data-writer"); err != nil {
		panic(fmt.Sprintf("Failed to initialize logger: %v", err))
	}
	defer func() {
		if err := logger.Close(); err != nil {
			fmt.Printf("Error closing logger: %v\n", err)
		}
	}()

	// Initialize database connection pool.
	if err := db.Initialize(); err != nil {
		logger.Error(ctx, "Failed to initialize database: %v", err)
		os.Exit(1)
	}
	defer db.Close()

	// Create Pub/Sub client.
	projectID := pubsub.GetProjectID()
	subscriptionID := pubsub.GetSubscriptionID()

	pubsubClient, err := pubsub.NewClient(ctx, projectID, subscriptionID)
	if err != nil {
		logger.Error(ctx, "Failed to create pubsub client: %v", err)
		os.Exit(1)
	}
	defer pubsubClient.Close()

	// Initialize components
	database := db.Get()
	eventProcessor := processor.NewEventProcessor(database)
	performanceMetrics := metrics.NewPerformanceMetrics()
	messageHandler := handler.NewMessageHandler(eventProcessor, performanceMetrics)
	messageReceiver := receiver.NewMessageReceiver(pubsubClient.GetSubscription(), messageHandler, performanceMetrics)

	// Create health server
	port := server.GetPortFromEnv()
	healthServer := server.NewHealthServer(port)

	// Create a new context that can be cancelled for graceful shutdown.
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Start the message processing loop in a goroutine.
	msgProcessDone := make(chan struct{})
	go func() {
		defer close(msgProcessDone)
		if err := messageReceiver.Start(ctx); err != nil {
			logger.Error(ctx, "Message receiver failed: %v", err)
		}
	}()

	// Start health check server.
	go func() {
		if err := healthServer.Start(ctx); err != nil {
			logger.Error(ctx, "Failed to start health check server: %v", err)
			os.Exit(1) // Exit if health check server fails to start.
		}
	}()

	// Wait for an interrupt signal (SIGINT or SIGTERM) to initiate shutdown.
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh
	logger.Info(ctx, "Shutdown signal received. Shutting down gracefully...")

	// Create a context for the shutdown procedure with a timeout.
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 1. Cancel the main context to signal message receiver to stop pulling new messages.
	cancel()

	// 2. Shutdown the HTTP server.
	if err := healthServer.Shutdown(shutdownCtx); err != nil {
		logger.Error(ctx, "HTTP server shutdown error: %v", err)
	}

	// 3. Wait for the message processing to finish.
	select {
	case <-msgProcessDone:
		logger.Info(ctx, "Message processing completed.")
	case <-shutdownCtx.Done():
		logger.Error(ctx, "Shutdown timed out waiting for message processing to complete.")
	}

	logger.Info(ctx, "Shutdown complete.")
}
