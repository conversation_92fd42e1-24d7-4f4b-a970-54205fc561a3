# Data Writer Modular Architecture

This document describes the modular architecture of the data-writer service, which has been refactored from a monolithic main.go file into separate, focused modules.

## Architecture Overview

The data-writer is now organized into the following modules:

```
data-writer/
├── main.go                 # Entry point and orchestration
├── processor/              # Event processing logic
│   └── event_processor.go
├── metrics/                # Performance metrics and statistics
│   └── performance_metrics.go
├── handler/                # Message handling and routing
│   └── message_handler.go
├── receiver/               # Pub/Sub message receiving
│   └── message_receiver.go
├── pubsub/                 # Pub/Sub client configuration
│   └── client.go
├── server/                 # HTTP server and health checks
│   └── health_server.go
├── db/                     # Database operations (existing)
├── logger/                 # Logging (existing)
└── proto/                  # Protocol buffers (existing)
```

## Module Responsibilities

### 1. main.go

- **Purpose**: Application entry point and orchestration
- **Responsibilities**:
  - Initialize all components
  - Set up graceful shutdown
  - Coordinate between modules
  - Handle signal processing

### 2. processor/event_processor.go

- **Purpose**: Core event processing logic
- **Responsibilities**:
  - Categorize events by type (legacy and V2)
  - Process events concurrently with proper error handling
  - Maintain backward compatibility with legacy events
  - Coordinate database operations for different event types

### 3. metrics/performance_metrics.go

- **Purpose**: Performance tracking and statistics
- **Responsibilities**:
  - Track message processing metrics
  - Calculate rolling window statistics
  - Monitor Pub/Sub latency
  - Provide performance insights

### 4. handler/message_handler.go

- **Purpose**: Individual message processing
- **Responsibilities**:
  - Unmarshal Pub/Sub messages
  - Route messages to appropriate processors
  - Handle message acknowledgment (Ack/Nack)
  - Record metrics for successful processing

### 5. receiver/message_receiver.go

- **Purpose**: Pub/Sub message receiving
- **Responsibilities**:
  - Start Pub/Sub message receiving loop
  - Handle message delivery to handlers
  - Log periodic performance metrics
  - Manage receiver lifecycle

### 6. pubsub/client.go

- **Purpose**: Pub/Sub client configuration
- **Responsibilities**:
  - Configure Pub/Sub client with environment variables
  - Set up concurrency and flow control settings
  - Manage client lifecycle
  - Provide subscription access

### 7. server/health_server.go

- **Purpose**: HTTP server and health checks
- **Responsibilities**:
  - Serve health check endpoints
  - Verify database connectivity
  - Handle graceful server shutdown
  - Manage server lifecycle

## Benefits of Modular Architecture

### 1. **Separation of Concerns**

Each module has a single, well-defined responsibility, making the code easier to understand and maintain.

### 2. **Testability**

Individual modules can be tested in isolation with mocked dependencies.

### 3. **Reusability**

Modules can be reused in different contexts or combined in different ways.

### 4. **Maintainability**

Changes to one module don't affect others, reducing the risk of introducing bugs.

### 5. **Scalability**

Modules can be optimized independently based on their specific performance characteristics.

## Configuration

The modular architecture uses environment variables for configuration:

### Pub/Sub Configuration

- `GCP_PROJECT_ID`: Google Cloud Project ID (default: "kryptogo-wallet-data")
- `PUBSUB_SUBSCRIPTION_ID`: Pub/Sub subscription ID (default: "solana-events-sub")
- `PUBSUB_NUM_GOROUTINES`: Number of concurrent goroutines (default: 1)
- `PUBSUB_MAX_OUTSTANDING_MESSAGES`: Max outstanding messages (default: 50)

### Health Server Configuration

- `HEALTH_CHECK_PORT`: HTTP server port (default: "8080")

### Database Configuration

- `DB_HOST`: Database host (default: "localhost")
- `DB_PORT`: Database port (default: 5432)
- `DB_USER`: Database user (default: "solana_data")
- `DB_PASSWORD`: Database password
- `DB_NAME`: Database name (default: "solana_data")
- `DB_SSL_MODE`: SSL mode (default: "disable")
- `DB_MAX_CONNS`: Max connections (default: 100)

## Event Processing Flow

1. **Message Reception**: `receiver` receives messages from Pub/Sub
2. **Message Handling**: `handler` unmarshals and routes messages
3. **Event Processing**: `processor` categorizes and processes events
4. **Database Operations**: Events are written to appropriate database tables
5. **Metrics Recording**: Performance metrics are tracked throughout the process
6. **Message Acknowledgment**: Messages are Acked or Nacked based on processing results

## Backward Compatibility

The modular architecture maintains full backward compatibility with:

- Legacy SOL transfer events
- Legacy token transfer events
- Legacy slot time events
- All existing database schemas and operations

## Error Handling

Each module implements appropriate error handling:

- **Graceful Degradation**: Individual module failures don't crash the entire system
- **Retry Logic**: Database operations include retry mechanisms
- **Error Propagation**: Errors are properly propagated up the call stack
- **Logging**: Comprehensive error logging for debugging

## Performance Considerations

- **Concurrent Processing**: Events are processed concurrently for efficiency
- **Batch Operations**: Database operations are batched for performance
- **Connection Pooling**: Database connections are pooled and reused
- **Metrics Tracking**: Performance is continuously monitored and logged

## Future Enhancements

The modular architecture enables easy future enhancements:

- **New Event Types**: Easy to add new event processors
- **Alternative Message Sources**: Pub/Sub client can be replaced with other sources
- **Different Storage Backends**: Database layer can be abstracted for different storage
- **Microservices**: Modules can be extracted into separate services
- **Load Balancing**: Multiple instances can be deployed with proper coordination
