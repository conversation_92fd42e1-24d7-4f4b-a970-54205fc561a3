package processor

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/metrics"
	pb "github.com/kryptogo/kg-solana-data/data-writer/proto"
)

const (
	// dbOperationTimeout is the maximum time to allow for a database operation to complete.
	dbOperationTimeout = 30 * time.Second
)

// EventProcessor handles the processing of Solana events
type EventProcessor struct {
	database *db.Repo
}

// NewEventProcessor creates a new event processor
func NewEventProcessor(database *db.Repo) *EventProcessor {
	return &EventProcessor{
		database: database,
	}
}

// ProcessBatchedEvents processes a batch of Solana events
func (ep *EventProcessor) ProcessBatchedEvents(ctx context.Context, batchedEvent *pb.BatchedSolanaEvent) error {
	// Collect events by type for batch processing, maintaining backward compatibility
	slotTimes := make([]*pb.SlotTime, 0)

	// New V2 events
	solTransfersV2 := make([]*pb.SolTransferEventV2, 0)
	tokenTransfersV2 := make([]*pb.TokenTransferEventV2, 0)
	transactions := make([]*pb.TransactionEvent, 0)
	solBalances := make([]*pb.SolBalanceEvent, 0)
	tokenBalances := make([]*pb.TokenBalanceEvent, 0)

	// Categorize events by type
	for _, event := range batchedEvent.Events {
		switch e := event.Event.(type) {
		case *pb.SolanaEvent_SlotTime:
			slotTimes = append(slotTimes, e.SlotTime)
		case *pb.SolanaEvent_SolTransferV2:
			solTransfersV2 = append(solTransfersV2, e.SolTransferV2)
		case *pb.SolanaEvent_TokenTransferV2:
			tokenTransfersV2 = append(tokenTransfersV2, e.TokenTransferV2)
		case *pb.SolanaEvent_Transaction:
			transactions = append(transactions, e.Transaction)
		case *pb.SolanaEvent_SolBalance:
			solBalances = append(solBalances, e.SolBalance)
		case *pb.SolanaEvent_TokenBalance:
			tokenBalances = append(tokenBalances, e.TokenBalance)
		default:
		}
	}

	// Process all event types within the message concurrently for efficiency.
	var wg sync.WaitGroup
	var processingError error
	var errMu sync.Mutex

	setErr := func(err error) {
		errMu.Lock()
		defer errMu.Unlock()
		if processingError == nil {
			processingError = err
		}
	}

	// --- Process Slot Times ---
	if len(slotTimes) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			dbCtx, cancel := context.WithTimeout(context.Background(), dbOperationTimeout)
			defer cancel()
			if err := ep.database.ProcessSlotTimes(dbCtx, slotTimes); err != nil {
				setErr(fmt.Errorf("error processing slot times: %w", err))
			}
		}()
	}

	// --- Process New V2 SOL Transfers ---
	if len(solTransfersV2) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			dbCtx, cancel := context.WithTimeout(context.Background(), dbOperationTimeout)
			defer cancel()
			if err := ep.database.ProcessSolTransferEventsV2(dbCtx, solTransfersV2); err != nil {
				setErr(fmt.Errorf("error processing SOL transfers V2: %w", err))
			}
		}()
	}

	// --- Process New V2 Token Transfers ---
	if len(tokenTransfersV2) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			dbCtx, cancel := context.WithTimeout(context.Background(), dbOperationTimeout)
			defer cancel()
			if err := ep.database.ProcessTokenTransferEventsV2(dbCtx, tokenTransfersV2); err != nil {
				setErr(fmt.Errorf("error processing token transfers V2: %w", err))
			}
		}()
	}

	// --- Process Transaction Events ---
	if len(transactions) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			dbCtx, cancel := context.WithTimeout(context.Background(), dbOperationTimeout)
			defer cancel()
			if err := ep.database.ProcessTransactionEvents(dbCtx, transactions); err != nil {
				setErr(fmt.Errorf("error processing transaction events: %w", err))
			}
		}()
	}

	// --- Process SOL Balance Events ---
	if len(solBalances) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			dbCtx, cancel := context.WithTimeout(context.Background(), dbOperationTimeout)
			defer cancel()
			if err := ep.database.ProcessSolBalanceEvents(dbCtx, solBalances); err != nil {
				setErr(fmt.Errorf("error processing SOL balance events: %w", err))
			}
		}()
	}

	// --- Process Token Balance Events ---
	if len(tokenBalances) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			dbCtx, cancel := context.WithTimeout(context.Background(), dbOperationTimeout)
			defer cancel()
			if err := ep.database.ProcessTokenBalanceEvents(dbCtx, tokenBalances); err != nil {
				setErr(fmt.Errorf("error processing token balance events: %w", err))
			}
		}()
	}

	// Wait for all database operations for this message to complete.
	wg.Wait()

	return processingError
}

// GetEventCounts returns the count of each event type in a batched event
func (ep *EventProcessor) GetEventCounts(batchedEvent *pb.BatchedSolanaEvent) metrics.EventCounts {
	counts := metrics.EventCounts{}

	for _, event := range batchedEvent.Events {
		switch event.Event.(type) {
		case *pb.SolanaEvent_SlotTime:
			counts.SlotTimes++
		case *pb.SolanaEvent_SolTransferV2:
			counts.SolTransfersV2++
		case *pb.SolanaEvent_TokenTransferV2:
			counts.TokenTransfersV2++
		case *pb.SolanaEvent_Transaction:
			counts.Transactions++
		case *pb.SolanaEvent_SolBalance:
			counts.SolBalances++
		case *pb.SolanaEvent_TokenBalance:
			counts.TokenBalances++
		}
	}

	return counts
}
