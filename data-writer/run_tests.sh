#!/bin/bash

set -e

echo "Setting up test database..."

# Start docker-compose services
docker-compose up -d postgres
sleep 5

# Wait for postgres to be ready
echo "Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
  if docker-compose exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
    break
  fi
  sleep 1
done

# Drop and recreate test database to ensure clean state
echo "Creating test database..."
docker-compose exec -T postgres psql -U postgres -c "DROP DATABASE IF EXISTS solana_data_test;"
docker-compose exec -T postgres psql -U postgres -c "CREATE DATABASE solana_data_test;"

# Apply schema to test database using existing liquibase changelog
echo "Applying schema to test database..."
docker run --rm \
  --network data-writer_default \
  -v "$(pwd)/db/changelog:/liquibase/changelog" \
  liquibase/liquibase:4.32.0 \
  update \
  --changelog-file=changelog/changelog.xml \
  --url=************************************************ \
  --username=postgres \
  --password=postgres

echo "Running tests..."
go test ./... -v

echo "Tests completed!"

# Cleanup
echo "Stopping docker services..."
docker-compose down 