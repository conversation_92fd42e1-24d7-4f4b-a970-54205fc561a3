package models

// BaseTransfer contains common fields for both SOL and token transfers
type BaseTransfer struct {
	Signature  []byte
	IxIndex    uint16
	Slot       uint64
	FromWallet []byte
	ToWallet   []byte
	Amount     uint64
}

// SolTransfer represents a SOL transfer event
type SolTransfer struct {
	BaseTransfer
}

// TokenTransfer represents a token transfer event
type TokenTransfer struct {
	BaseTransfer
	TokenMint []byte
}

// New V2 models for the updated schema
type SolTransferEventV2 struct {
	Slot       uint64
	TxIndex    uint32
	IxIndex    uint16
	FromWallet []byte
	ToWallet   []byte
	Amount     uint64
}

type TokenTransferEventV2 struct {
	Slot       uint64
	TxIndex    uint32
	IxIndex    uint16
	TokenMint  []byte
	FromWallet []byte
	ToWallet   []byte
	Amount     uint64
}

type TransactionEvent struct {
	Slot      uint64
	TxIndex   uint32
	Signature []byte
}

type SolBalanceEvent struct {
	Slot    uint64
	Account []byte
	Amount  uint64
}

type TokenBalanceEvent struct {
	Slot   uint64
	Owner  []byte
	Token  []byte
	Amount uint64
}

// SlotTime represents a slot and its corresponding timestamp
type SlotTime struct {
	Slot      uint64
	Timestamp uint64
}

// SolWalletInteractionMap represents a row in the sol_wallet_interaction_map table
// (source_wallet, paired_wallet)
type SolWalletInteractionMap struct {
	SourceWallet []byte
	PairedWallet []byte
}

// TokenWalletInteractionMap represents a row in the token_wallet_interaction_map table
// (token_mint, source_wallet, paired_wallet)
type TokenWalletInteractionMap struct {
	TokenMint    []byte
	SourceWallet []byte
	PairedWallet []byte
}
