services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: solana_data_test
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5

  liquibase:
    image: liquibase/liquibase:4.32.0
    volumes:
      - ./db/changelog:/liquibase/changelog
    command: update --changelog-file=changelog/changelog.xml --url=************************************************ --username=postgres --password=postgres
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data:
