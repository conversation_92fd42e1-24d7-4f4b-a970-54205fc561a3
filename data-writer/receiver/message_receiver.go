package receiver

import (
	"context"
	"errors"
	"time"

	"cloud.google.com/go/pubsub"

	"github.com/kryptogo/kg-solana-data/data-writer/handler"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"github.com/kryptogo/kg-solana-data/data-writer/metrics"
)

// MessageReceiver handles receiving and processing messages from Pub/Sub
type MessageReceiver struct {
	subscription *pubsub.Subscription
	handler      *handler.MessageHandler
	metrics      *metrics.PerformanceMetrics
}

// NewMessageReceiver creates a new message receiver
func NewMessageReceiver(subscription *pubsub.Subscription, handler *handler.MessageHandler, metrics *metrics.PerformanceMetrics) *MessageReceiver {
	return &MessageReceiver{
		subscription: subscription,
		handler:      handler,
		metrics:      metrics,
	}
}

// Start starts receiving messages
func (mr *MessageReceiver) Start(ctx context.Context) error {
	// Start a goroutine to log performance metrics periodically.
	go mr.logMetricsPeriodically(ctx)

	logger.Info(ctx, "Starting message receiver...")

	// subscription.Receive is a blocking call.
	// It will run until the context is cancelled or a fatal error occurs.
	err := mr.subscription.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		mr.handler.HandleMessage(ctx, msg)
	})

	// If Receive returns a non-cancellation error, it's a fatal problem.
	if err != nil && !errors.Is(err, context.Canceled) {
		logger.Error(ctx, "Pub/Sub Receive returned a fatal error: %v", err)
		return err
	}

	logger.Info(ctx, "Message receiver has stopped.")
	return nil
}

// logMetricsPeriodically logs performance metrics every minute
func (mr *MessageReceiver) logMetricsPeriodically(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			stats := mr.metrics.GetStats()
			// Use a background context for logging in case the parent is done.
			logger.Info(context.Background(), "Performance metrics: %+v", stats)
		case <-ctx.Done():
			return
		}
	}
}
