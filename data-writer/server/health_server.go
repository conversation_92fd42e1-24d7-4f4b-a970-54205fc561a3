package server

import (
	"context"
	"net/http"
	"os"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

// HealthServer handles HTTP server and health check functionality
type HealthServer struct {
	server *http.Server
}

// NewHealthServer creates a new health server
func NewHealthServer(port string) *HealthServer {
	if port == "" {
		port = "8080"
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/health", healthCheckHandler)

	server := &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}

	return &HealthServer{
		server: server,
	}
}

// Start starts the health server
func (hs *HealthServer) Start(ctx context.Context) error {
	logger.Info(ctx, "Starting health check server on port %s", hs.server.Addr)
	return hs.server.ListenAndServe()
}

// Shutdown gracefully shuts down the server
func (hs *HealthServer) Shutdown(ctx context.Context) error {
	return hs.server.Shutdown(ctx)
}

// healthCheckHandler handles health check requests
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	// A simple health check that verifies the database connection is alive.
	if db.Get() == nil {
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte("database not initialized"))
		return
	}
	// You might add a Ping() check here for a more robust health check.
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("ok"))
}

// GetPortFromEnv gets the port from environment variable
func GetPortFromEnv() string {
	return getEnvOrDefault("HEALTH_CHECK_PORT", "8080")
}

// getEnvOrDefault gets an environment variable or returns a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
