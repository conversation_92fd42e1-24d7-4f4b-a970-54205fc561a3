package handler

import (
	"context"
	"time"

	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"

	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"github.com/kryptogo/kg-solana-data/data-writer/metrics"
	"github.com/kryptogo/kg-solana-data/data-writer/processor"
	pb "github.com/kryptogo/kg-solana-data/data-writer/proto"
)

// MessageHandler handles Pub/Sub message processing
type MessageHandler struct {
	eventProcessor *processor.EventProcessor
	metrics        *metrics.PerformanceMetrics
}

// NewMessageHandler creates a new message handler
func NewMessageHandler(eventProcessor *processor.EventProcessor, metrics *metrics.PerformanceMetrics) *MessageHandler {
	return &MessageHandler{
		eventProcessor: eventProcessor,
		metrics:        metrics,
	}
}

// HandleMessage processes a single Pub/Sub message
func (h *MessageHandler) HandleMessage(ctx context.Context, msg *pubsub.Message) {
	receiveTime := time.Now()

	var batchedEvent pb.BatchedSolanaEvent
	if err := proto.Unmarshal(msg.Data, &batchedEvent); err != nil {
		logger.Error(ctx, "Failed to unmarshal message data: %v. Message will be Nacked.", err)
		// Nack the message if it's malformed and cannot be processed.
		msg.Nack()
		return
	}

	// Process the batched events
	if err := h.eventProcessor.ProcessBatchedEvents(ctx, &batchedEvent); err != nil {
		// If any DB operation failed, Nack the message to have Pub/Sub redeliver it.
		logger.Error(ctx, "Error processing batched events, Nacking message: %v", err)
		msg.Nack()
		return
	}

	// If all DB operations succeeded, Ack the message.
	msg.Ack()

	// Record metrics on success.
	latency := time.Since(receiveTime)
	h.metrics.RecordPubSubLatency(latency)

	// Get event counts for metrics
	eventCounts := h.eventProcessor.GetEventCounts(&batchedEvent)
	h.metrics.RecordMessage(
		len(msg.Data),
		"batch_success",
		latency,
		eventCounts,
	)
}
