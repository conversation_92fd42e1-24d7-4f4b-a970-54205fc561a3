package main

import (
	"context"
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

func main() {
	targetEndSlotStr := os.Getenv("TARGET_END_SLOT")
	periodsStr := os.Getenv("PERIODS") // Comma-separated list of periods to process

	if targetEndSlotStr == "" {
		log.Fatalf("TARGET_END_SLOT environment variable must be set")
	}

	targetEndSlot, err := strconv.ParseUint(targetEndSlotStr, 10, 64)
	if err != nil {
		log.Fatalf("Invalid TARGET_END_SLOT value: %v", err)
	}

	// Parse periods to process
	var periodsToProcess []string
	if periodsStr == "" {
		// Default to all periods
		periodsToProcess = []string{"1m", "5m", "15m", "1h", "4h", "1d"}
	} else {
		periodsToProcess = strings.Split(periodsStr, ",")
		for i, period := range periodsToProcess {
			periodsToProcess[i] = strings.TrimSpace(period)
		}
	}

	// Validate periods
	validPeriods := map[string]bool{"1m": true, "5m": true, "15m": true, "1h": true, "4h": true, "1d": true}
	for _, period := range periodsToProcess {
		if !validPeriods[period] {
			log.Fatalf("Invalid period: %s. Supported periods: 1m, 5m, 15m, 1h, 4h, 1d", period)
		}
	}

	if targetEndSlot == 0 {
		log.Fatalf("Invalid target end slot: %d", targetEndSlot)
	}

	ctx := context.Background()
	if err := logger.Init(ctx, "balance-history-backfill"); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	repo := db.Get()

	logger.Info(ctx, "Starting balance history backfill: target end slot %d, periods %v", targetEndSlot, periodsToProcess)

	// For now, we process all periods together in the main function
	// The multi-period processing is handled internally in the database layer
	if err := repo.BackfillBalanceHistory(ctx, targetEndSlot); err != nil {
		logger.Error(ctx, "Balance history backfill failed: %v", err)
		os.Exit(1)
	}

	logger.Info(ctx, "Balance history backfill complete.")
}
