package main

import (
	"context"
	"log"
	"os"
	"strings"
	"time"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

func main() {
	periodsStr := os.Getenv("PERIODS")   // Comma-separated list of periods to process
	intervalStr := os.Getenv("INTERVAL") // Processing interval in seconds

	// Parse periods to process
	var periodsToProcess []string
	if periodsStr == "" {
		// Default to all periods
		periodsToProcess = []string{"1m", "5m", "15m", "1h", "4h", "1d"}
	} else {
		periodsToProcess = strings.Split(periodsStr, ",")
		for i, period := range periodsToProcess {
			periodsToProcess[i] = strings.TrimSpace(period)
		}
	}

	// Validate periods
	validPeriods := map[string]bool{"1m": true, "5m": true, "15m": true, "1h": true, "4h": true, "1d": true}
	for _, period := range periodsToProcess {
		if !validPeriods[period] {
			log.Fatalf("Invalid period: %s. Supported periods: 1m, 5m, 15m, 1h, 4h, 1d", period)
		}
	}

	// Parse interval
	interval := 30 * time.Second // Default to 30 seconds
	if intervalStr != "" {
		if parsed, err := time.ParseDuration(intervalStr + "s"); err == nil {
			interval = parsed
		} else {
			log.Printf("Invalid INTERVAL value, using default 30s: %v", err)
		}
	}

	ctx := context.Background()
	if err := logger.Init(ctx, "balance-history-delta"); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	repo := db.Get()

	logger.Info(ctx, "Starting balance history delta processing: periods %v, interval %v", periodsToProcess, interval)

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Info(ctx, "Context cancelled, stopping delta processing")
			return
		case <-ticker.C:
			if err := repo.ProcessBalanceHistoryDelta(ctx); err != nil {
				logger.Error(ctx, "Balance history delta processing failed: %v", err)
				// Continue processing despite errors
			}
		}
	}
}
