package main

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

// WalletTxStatsOutput represents the output format for wallet transaction stats
type WalletTxStatsOutput struct {
	Wallet       string `json:"wallet"`
	MaxTxIn1h    int64  `json:"max_tx_in_1h"`
	TotalTxCount int64  `json:"total_tx_count"`
}

func main() {
	// Define command line flags
	var (
		action     = flag.String("action", "", "Action to perform: backfill, top-by-total, top-by-max-1h")
		startSlot  = flag.String("start-slot", "", "Start slot for backfill (required for backfill action)")
		endSlot    = flag.String("end-slot", "", "End slot for backfill (required for backfill action)")
		limit      = flag.Int("limit", 10, "Number of top wallets to return (default: 10)")
		outputFile = flag.String("output", "", "Output file path (optional, defaults to stdout)")
		format     = flag.String("format", "json", "Output format: json or csv")
	)
	flag.Parse()

	// Validate required flags
	if *action == "" {
		fmt.Println("Error: action flag is required")
		printUsage()
		os.Exit(1)
	}

	// Validate action
	validActions := map[string]bool{
		"backfill":      true,
		"top-by-total":  true,
		"top-by-max-1h": true,
	}
	if !validActions[*action] {
		fmt.Printf("Error: invalid action '%s'\n", *action)
		printUsage()
		os.Exit(1)
	}

	// Validate backfill-specific flags
	if *action == "backfill" {
		if *startSlot == "" || *endSlot == "" {
			fmt.Println("Error: start-slot and end-slot are required for backfill action")
			printUsage()
			os.Exit(1)
		}
	}

	// Validate format
	if *format != "json" && *format != "csv" {
		fmt.Printf("Error: invalid format '%s'. Supported formats: json, csv\n", *format)
		os.Exit(1)
	}

	// Initialize logger
	ctx := context.Background()
	if err := logger.Init(ctx, "wallet-tx-stats"); err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Close()

	// Initialize database
	if err := db.Initialize(); err != nil {
		fmt.Printf("Failed to initialize database: %v\n", err)
		os.Exit(1)
	}
	defer db.Close()

	repo := db.Get()

	// Execute the requested action
	switch *action {
	case "backfill":
		if err := performBackfill(ctx, repo, *startSlot, *endSlot); err != nil {
			fmt.Printf("Backfill failed: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("Backfill completed successfully")

	case "top-by-total":
		if err := queryTopWallets(ctx, repo, "total", *limit, *outputFile, *format); err != nil {
			fmt.Printf("Query failed: %v\n", err)
			os.Exit(1)
		}

	case "top-by-max-1h":
		if err := queryTopWallets(ctx, repo, "max-1h", *limit, *outputFile, *format); err != nil {
			fmt.Printf("Query failed: %v\n", err)
			os.Exit(1)
		}
	}
}

func performBackfill(ctx context.Context, repo *db.Repo, startSlotStr, endSlotStr string) error {
	startSlot, err := strconv.ParseUint(startSlotStr, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid start slot: %v", err)
	}

	endSlot, err := strconv.ParseUint(endSlotStr, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid end slot: %v", err)
	}

	if startSlot >= endSlot {
		return fmt.Errorf("start slot must be less than end slot")
	}

	logger.Info(ctx, "Starting wallet transaction stats backfill: slots %d to %d", startSlot, endSlot)
	return repo.BackfillWalletTxStats(ctx, startSlot, endSlot)
}

func queryTopWallets(ctx context.Context, repo *db.Repo, queryType string, limit int, outputFile, format string) error {
	var stats []db.WalletTxStats
	var err error

	switch queryType {
	case "total":
		stats, err = repo.GetTopWalletsByTxCount(ctx, limit)
	case "max-1h":
		stats, err = repo.GetTopWalletsByMaxTxIn1h(ctx, limit)
	default:
		return fmt.Errorf("invalid query type: %s", queryType)
	}

	if err != nil {
		return fmt.Errorf("failed to query wallet stats: %v", err)
	}

	// Convert to output format
	output := make([]WalletTxStatsOutput, len(stats))
	for i, stat := range stats {
		output[i] = WalletTxStatsOutput{
			Wallet:       hex.EncodeToString(stat.Wallet),
			MaxTxIn1h:    stat.MaxTxIn1h,
			TotalTxCount: stat.TotalTxCount,
		}
	}

	// Output results
	return outputResults(output, outputFile, format)
}

func outputResults(results []WalletTxStatsOutput, outputFile, format string) error {
	var output string

	switch format {
	case "json":
		jsonBytes, err := json.MarshalIndent(results, "", "  ")
		if err != nil {
			return fmt.Errorf("failed to marshal JSON: %v", err)
		}
		output = string(jsonBytes)

	case "csv":
		var lines []string
		lines = append(lines, "wallet,max_tx_in_1h,total_tx_count")
		for _, result := range results {
			lines = append(lines, fmt.Sprintf("%s,%d,%d",
				result.Wallet, result.MaxTxIn1h, result.TotalTxCount))
		}
		output = strings.Join(lines, "\n")

	default:
		return fmt.Errorf("unsupported format: %s", format)
	}

	// Write to file or stdout
	if outputFile != "" {
		if err := os.WriteFile(outputFile, []byte(output), 0644); err != nil {
			return fmt.Errorf("failed to write output file: %v", err)
		}
		fmt.Printf("Results written to %s\n", outputFile)
	} else {
		fmt.Println(output)
	}

	return nil
}

func printUsage() {
	fmt.Println("Wallet Transaction Stats CLI Tool")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  wallet-tx-stats -action=<action> [options]")
	fmt.Println()
	fmt.Println("Actions:")
	fmt.Println("  backfill       - Backfill wallet transaction stats from existing data")
	fmt.Println("  top-by-total   - Get top wallets by total transaction count")
	fmt.Println("  top-by-max-1h  - Get top wallets by maximum transactions in 1 hour")
	fmt.Println()
	fmt.Println("Options:")
	fmt.Println("  -start-slot    Start slot for backfill (required for backfill)")
	fmt.Println("  -end-slot      End slot for backfill (required for backfill)")
	fmt.Println("  -limit         Number of top wallets to return (default: 10)")
	fmt.Println("  -output        Output file path (optional, defaults to stdout)")
	fmt.Println("  -format        Output format: json or csv (default: json)")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  # Backfill wallet stats for slots 1000000 to 2000000")
	fmt.Println("  wallet-tx-stats -action=backfill -start-slot=1000000 -end-slot=2000000")
	fmt.Println()
	fmt.Println("  # Get top 20 wallets by total transaction count in JSON format")
	fmt.Println("  wallet-tx-stats -action=top-by-total -limit=20 -format=json")
	fmt.Println()
	fmt.Println("  # Get top 10 wallets by max transactions in 1h and save to CSV file")
	fmt.Println("  wallet-tx-stats -action=top-by-max-1h -limit=10 -format=csv -output=top_wallets.csv")
}
