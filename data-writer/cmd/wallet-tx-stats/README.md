# Wallet Transaction Stats CLI Tool

A command-line tool for managing and querying wallet transaction statistics in the Solana data pipeline.

## Overview

This tool provides functionality to:

- **Backfill** wallet transaction statistics from existing transaction data
- **Query** top wallets by total transaction count
- **Query** top wallets by maximum transactions in 1 hour
- **Export** results in JSON or CSV format

## Installation

```bash
cd data-writer/cmd/wallet-tx-stats

# Build using Go
go build -o wallet-tx-stats main.go

# Or build using Make
make build

# Install to GOPATH/bin
make install
```

## Usage

```bash
wallet-tx-stats -action=<action> [options]
```

### Actions

#### 1. Backfill (`backfill`)

Processes existing transaction data to calculate and store wallet transaction statistics.

**Required flags:**

- `-start-slot`: Starting slot number for processing
- `-end-slot`: Ending slot number for processing

**Example:**

```bash
# Backfill wallet stats for slots 1000000 to 2000000
wallet-tx-stats -action=backfill -start-slot=1000000 -end-slot=2000000
```

#### 2. Top by Total Transactions (`top-by-total`)

Retrieves wallets with the highest total transaction counts.

**Optional flags:**

- `-limit`: Number of results to return (default: 10)
- `-output`: Output file path (default: stdout)
- `-format`: Output format - json or csv (default: json)

**Example:**

```bash
# Get top 20 wallets by total transaction count
wallet-tx-stats -action=top-by-total -limit=20

# Save results to CSV file
wallet-tx-stats -action=top-by-total -limit=50 -format=csv -output=top_wallets_total.csv
```

#### 3. Top by Max Transactions in 1 Hour (`top-by-max-1h`)

Retrieves wallets with the highest transaction counts within any 1-hour period.

**Optional flags:**

- `-limit`: Number of results to return (default: 10)
- `-output`: Output file path (default: stdout)
- `-format`: Output format - json or csv (default: json)

**Example:**

```bash
# Get top 10 wallets by max transactions in 1 hour
wallet-tx-stats -action=top-by-max-1h -limit=10

# Save results to JSON file
wallet-tx-stats -action=top-by-max-1h -limit=25 -format=json -output=top_wallets_1h.json
```

## Output Formats

### JSON Format

```json
[
  {
    "wallet": "1234567890abcdef...",
    "max_tx_in_1h": 150,
    "total_tx_count": 5000
  }
]
```

### CSV Format

```csv
wallet,max_tx_in_1h,total_tx_count
1234567890abcdef...,150,5000
```

## Environment Variables

The tool uses the same database configuration as other data-writer components. Ensure the following environment variables are set:

- `DB_HOST`: Database host (default: localhost)
- `DB_PORT`: Database port (default: 5432)
- `DB_USER`: Database username
- `DB_PASSWORD`: Database password
- `DB_NAME`: Database name
- `DB_SSL_MODE`: SSL mode (default: disable)

## Examples

### Complete Workflow

1. **Backfill historical data:**

```bash
# Process last 1 million slots
wallet-tx-stats -action=backfill -start-slot=200000000 -end-slot=201000000
```

2. **Query top performers:**

```bash
# Get top 50 wallets by total transactions
wallet-tx-stats -action=top-by-total -limit=50 -format=csv -output=analysis/top_total.csv

# Get top 25 wallets by peak hourly activity
wallet-tx-stats -action=top-by-max-1h -limit=25 -format=json -output=analysis/top_hourly.json
```

### Monitoring and Analysis

```bash
# Daily top wallets report
wallet-tx-stats -action=top-by-total -limit=100 -format=csv -output="reports/daily_top_$(date +%Y%m%d).csv"

# High-frequency trading detection
wallet-tx-stats -action=top-by-max-1h -limit=20 -format=json
```

## Performance Notes

- **Backfill operations** can be resource-intensive for large slot ranges. Consider processing in smaller batches.
- **Query operations** are optimized with database indexes on the wallet_tx_stats table.
- **Large result sets** should be exported to files rather than printed to stdout.

## Error Handling

The tool provides detailed error messages for common issues:

- Invalid slot ranges
- Database connection problems
- Missing required flags
- Invalid output formats

Exit codes:

- `0`: Success
- `1`: Error (check stderr for details)

## Integration

This tool can be integrated into data processing pipelines:

```bash
#!/bin/bash
# Example pipeline script

# Backfill latest data
wallet-tx-stats -action=backfill -start-slot=$START_SLOT -end-slot=$END_SLOT

# Generate reports
wallet-tx-stats -action=top-by-total -limit=100 -format=csv -output=reports/top_total.csv
wallet-tx-stats -action=top-by-max-1h -limit=50 -format=json -output=reports/top_hourly.json

echo "Wallet transaction stats updated successfully"
```

## Testing

### Prerequisites

Ensure you have a PostgreSQL test database set up:

```bash
# From the data-writer root directory
cd ../..
./run_tests.sh  # This sets up the test database
```

### Running Tests

```bash
# Test command validation (no database required)
./wallet-tx-stats  # Should show usage message

# Test with invalid action
./wallet-tx-stats -action=invalid  # Should show error and usage

# Test backfill validation
./wallet-tx-stats -action=backfill  # Should require start-slot and end-slot

# Integration test with test database (requires test DB setup)
# Note: This will fail if no data exists, but tests the database connection
./wallet-tx-stats -action=top-by-total -limit=5
```

### Development Testing

For development and testing purposes, you can use the test database:

```bash
# Set test database environment variables
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=postgres
export DB_NAME=solana_data_test
export DB_SSL_MODE=disable

# Test the command
./wallet-tx-stats -action=top-by-total -limit=5
```
