package main

import (
	"context"
	"flag"

	"github.com/gagliardetto/solana-go/rpc"
	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

func main() {
	logger.Init(context.Background(), "create-partition")

	// Parse command line flags
	rpcEndpoint := flag.String("rpc", "https://api.mainnet-beta.solana.com", "Solana RPC endpoint")
	flag.Parse()

	// Initialize database connection
	if err := db.Initialize(); err != nil {
		panic(err)
	}
	defer db.Close()

	client := rpc.New(*rpcEndpoint)
	if err := createNextPartitions(context.Background(), client); err != nil {
		logger.Error(context.Background(), "Error creating partitions: %v", err)
	}
}

func createNextPartitions(ctx context.Context, client *rpc.Client) error {
	// Get current slot
	slot, err := client.GetSlot(ctx, rpc.CommitmentFinalized)
	if err != nil {
		return err
	}

	repo := db.Get()

	// Create next partition for sol_transfer_events
	if err := repo.CreateNextNewPartition(ctx, "sol_transfer_events", slot); err != nil {
		return err
	}

	// Create next partition for token_transfer_events
	if err := repo.CreateNextNewPartition(ctx, "token_transfer_events", slot); err != nil {
		return err
	}

	// Create next partition for transaction_signatures
	if err := repo.CreateNextNewPartition(ctx, "transaction_signatures", slot); err != nil {
		return err
	}

	// Create next partition for sol_balances
	if err := repo.CreateNextNewPartition(ctx, "sol_balances", slot); err != nil {
		return err
	}

	// Create next partition for token_balances
	if err := repo.CreateNextNewPartition(ctx, "token_balances", slot); err != nil {
		return err
	}

	logger.Info(ctx, "Successfully created next partitions for slot %d", slot)
	return nil
}
