package main

import (
	"context"
	"log"
	"os"
	"strconv"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"golang.org/x/sync/errgroup"
)

func main() {
	fromSlotStr := os.Getenv("FROM_SLOT")
	toSlotStr := os.Getenv("TO_SLOT")
	chunkSizeStr := os.Getenv("CHUNK_SIZE")
	onlyTokenStr := os.Getenv("ONLY_TOKEN")
	usdcMint := "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
	wsolMint := "So11111111111111111111111111111111111111112"

	if fromSlotStr == "" || toSlotStr == "" {
		log.Fatalf("FROM_SLOT and TO_SLOT environment variables must be set")
	}

	fromSlot, err := strconv.ParseUint(fromSlotStr, 10, 64)
	if err != nil {
		log.Fatalf("Invalid FROM_SLOT value: %v", err)
	}

	toSlot, err := strconv.ParseUint(toSlotStr, 10, 64)
	if err != nil {
		log.Fatalf("Invalid TO_SLOT value: %v", err)
	}

	chunkSize := uint64(1000)
	if chunkSizeStr != "" {
		chunkSize, err = strconv.ParseUint(chunkSizeStr, 10, 64)
		if err != nil {
			log.Fatalf("Invalid CHUNK_SIZE value: %v", err)
		}
	}

	onlyToken := false
	if onlyTokenStr != "" {
		onlyToken, err = strconv.ParseBool(onlyTokenStr)
		if err != nil {
			log.Fatalf("Invalid ONLY_TOKEN value: %v", err)
		}
	}

	if fromSlot == 0 || toSlot == 0 || toSlot <= fromSlot {
		log.Fatalf("Invalid slot range: from-slot=%d, to-slot=%d", fromSlot, toSlot)
	}

	ctx := context.Background()
	if err := logger.Init(ctx, "wallet-interaction-map-backfill"); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	repo := db.Get()

	logger.Info(ctx, "Starting SOL wallet interaction map backfill: slots [%d, %d)", fromSlot, toSlot)

	usdcMintBytes, err := db.DecodeBase58(usdcMint)
	if err != nil {
		logger.Error(ctx, "Failed to decode USDC mint: %v", err)
		os.Exit(1)
	}
	wsolMintBytes, err := db.DecodeBase58(wsolMint)
	if err != nil {
		logger.Error(ctx, "Failed to decode WSOL mint: %v", err)
		os.Exit(1)
	}

	// Process in chunks
	for currentSlot := fromSlot; currentSlot < toSlot; currentSlot += chunkSize {
		endSlot := currentSlot + chunkSize
		if endSlot > toSlot {
			endSlot = toSlot
		}

		logger.Info(ctx, "Processing wallet interaction map backfill: slots [%d, %d)", currentSlot, endSlot)
		var eg errgroup.Group

		if !onlyToken {
			eg.Go(func() error {
				if err := repo.BackfillSolWalletInteractionMap(ctx, currentSlot, endSlot); err != nil {
					logger.Error(ctx, "SOL wallet interaction map backfill failed for slots [%d, %d): %v", currentSlot, endSlot, err)
					return err
				}
				return nil
			})
		}

		eg.Go(func() error {
			if err := repo.BackfillTokenWalletInteractionMap(ctx, currentSlot, endSlot, usdcMintBytes, wsolMintBytes); err != nil {
				logger.Error(ctx, "Token wallet interaction map backfill failed for slots [%d, %d): %v", currentSlot, endSlot, err)
				return err
			}
			return nil
		})

		if err := eg.Wait(); err != nil {
			os.Exit(1)
		}
	}
	logger.Info(ctx, "Wallet interaction map backfill complete.")
}
