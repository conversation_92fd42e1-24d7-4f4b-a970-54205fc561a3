# Solana Token Transfer Data Writer

This service is responsible for ingesting Solana token transfer events from a Pub/Sub topic and storing them in a PostgreSQL database. It implements the design specified in the [design document](../docs/design-db.md).

## Features

- High-throughput token transfer event ingestion
- Efficient batch processing with PostgreSQL COPY
- Automatic table partitioning by week
- Idempotent writes to prevent duplicates
- Graceful shutdown handling
- Health check endpoint

## Prerequisites

- Go 1.21 or later
- Docker and Docker Compose
- Google Cloud SDK (for Pub/Sub access)

## Local Development Setup

1. Start the PostgreSQL database and run Liquibase migrations:

```bash
docker-compose up -d
```

2. Set up environment variables:

```bash
export DATABASE_URL="postgres://postgres:postgres@localhost:5432/solana_data?sslmode=disable"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"
```

3. Build and run the service:

```bash
go build
./data-writer
```

## Database Migrations

The database schema is managed using Liquibase. To apply migrations:

```bash
docker-compose run --rm liquibase update
```

To rollback changes:

```bash
docker-compose run --rm liquibase rollback <tag>
```

## Configuration

The service can be configured using the following environment variables:

- `DATABASE_URL`: PostgreSQL connection string (required)
- `HEALTH_CHECK_PORT`: Port for health check endpoint (default: 8080)
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to Google Cloud credentials file (required for Pub/Sub)

## Monitoring

The service exposes a health check endpoint at `/health` that returns a 200 OK response when the service is running properly.

## Production Deployment

For production deployment:

1. Ensure the PostgreSQL instance is configured according to the design document specifications
2. Set up appropriate connection pooling and resource limits
3. Configure monitoring and alerting
4. Use a managed Kubernetes cluster for running the service

## Database Maintenance

### Creating New Partitions

Partitions are created automatically by a trigger when new data is inserted. However, you can manually create partitions for future weeks:

```sql
CREATE TABLE token_transfers_y2024w25 PARTITION OF token_transfers
    FOR VALUES FROM ('2024-06-17 00:00:00') TO ('2024-06-24 00:00:00');
```

### Archiving Old Data

To archive old data:

1. Create a new table for the archived data
2. Move the partition to the archive table
3. Detach the partition from the main table

```sql
-- Create archive table
CREATE TABLE token_transfers_archive (LIKE token_transfers);

-- Move partition to archive
ALTER TABLE token_transfers_archive ATTACH PARTITION token_transfers_y2024w24
    FOR VALUES FROM ('2024-06-10 00:00:00') TO ('2024-06-17 00:00:00');

-- Detach from main table
ALTER TABLE token_transfers DETACH PARTITION token_transfers_y2024w24;
```

## Testing

### Running Tests

#### Quick Start with Test Script

Use the provided test runner script for comprehensive testing:

```bash
# Run all tests
./run_tests.sh

# Run specific test categories
./run_tests.sh unit        # Basic unit tests only
./run_tests.sh partition   # Partition functionality tests
./run_tests.sh bench       # Performance benchmarks
./run_tests.sh race        # Race condition detection
./run_tests.sh coverage    # Generate coverage report

# Get help
./run_tests.sh help
```

#### Manual Test Commands

To run all tests manually:

```bash
go test ./... -v
```

To run all test without db:

```bash
go test -v ./db -short
```

To run specific test files:

```bash
go test ./db -v
```

To run a specific test:

```bash
go test ./db -v -run TestCalculatePartitionName
```

### Test Coverage

The test suite includes comprehensive tests for:

#### Core Transfer Processing

- SOL transfer processing and validation
- Token transfer processing and validation
- Slot time processing and validation
- Duplicate handling and idempotency
- Empty batch handling

#### Partition Management (New)

- **Partition Calculation Tests**
  - `TestCalculatePartitionName` - Validates partition name generation for different slots
  - `TestCalculatePartitionBounds` - Verifies correct slot range calculation for partitions

- **Automatic Partition Creation Tests**
  - `TestEnsurePartitionExists` - Tests dynamic partition creation and existence checking
  - `TestPreCreateNextPartitionIfNeeded` - Validates pre-creation of partitions near boundaries

- **Cross-Partition Data Handling**
  - `TestCrossPartitionTransfers` - Tests SOL transfers spanning multiple partitions
  - `TestCrossPartitionTokenTransfers` - Tests token transfers across partition boundaries
  - `TestPartitionGrouping` - Validates proper grouping of transfers by partition

#### Database Integration

- Connection pool management
- Transaction handling and rollback
- Error handling and retry logic
- Conflict resolution (ON CONFLICT DO NOTHING)

### Test Database Setup

Tests require a PostgreSQL database. Configure test database connection in test files:

```go
config := defaultConfig()
config.Host = "localhost"
config.Port = 5432
config.User = "postgres"
config.Password = "postgres"
config.Database = "solana_data"
config.SSLMode = "disable"
```

### Partition Testing Scenarios

The partition tests cover the following scenarios:

1. **Normal Partition Operations**
   - Writing to existing partitions
   - Calculating correct partition names and bounds
   - Grouping transfers by target partition

2. **Partition Boundary Handling**
   - Writing data that spans partition boundaries
   - Automatic creation of missing partitions
   - Pre-creation of upcoming partitions

3. **Edge Cases**
   - Concurrent partition creation
   - Retrying on partition creation failures
   - Handling slots in different partition ranges

### Performance Testing

For performance testing of the partition optimization:

```bash
# Run with race detection
go test ./db -v -race

# Run benchmarks
go test ./db -v -bench=.

# Run with coverage
go test ./db -v -cover
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
