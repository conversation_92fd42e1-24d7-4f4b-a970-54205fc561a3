# Testing Guide for Partition Optimization

This guide helps you verify that the partition optimization features are working correctly.

## Prerequisites

1. **PostgreSQL Setup**
   ```bash
   # Make sure PostgreSQL is running
   pg_isready -h localhost -p 5432
   
   # Create test database
   createdb solana_data
   ```

2. **Apply Database Schema**
   ```bash
   # Run migrations to create partitioned tables
   liquibase update
   ```

## Quick Verification

### 1. Run the Test Script
```bash
# Navigate to data-writer directory
cd data-writer

# Run all tests
./run_tests.sh
```

Expected output should show:
```
🚀 Running Data Writer Tests
================================
[INFO] Checking PostgreSQL connection...
[SUCCESS] PostgreSQL is running
[INFO] Running unit tests...
...
[SUCCESS] All tests completed successfully! 🎉
```

### 2. Test Specific Partition Features

#### Test Partition Calculation
```bash
./run_tests.sh partition
```

This verifies:
- ✅ Partition name calculation for different slots
- ✅ Partition boundary calculations
- ✅ Automatic partition creation
- ✅ Cross-partition data handling

#### Test Performance Improvements
```bash
./run_tests.sh bench
```

This measures:
- ⚡ Partition calculation performance
- ⚡ Optimized insert performance
- ⚡ Cross-partition insert handling

## Manual Testing

### 1. Test Partition Auto-Creation

```go
// Example: Test creating a partition for a future slot
ctx := context.Background()
repo := db.Get()

// This should automatically create partition sol_transfers_p450
futureSlot := uint64(400000000)
err := repo.ensurePartitionExists(ctx, "sol_transfers", futureSlot)
// Should return no error and create the partition
```

### 2. Test Cross-Partition Writes

```go
// Test data spanning partition boundaries
events := []*pb.SolTransferEvent{
    {
        Metadata: &pb.EventMetadata{
            Slot: 346463999, // Last slot of p400
        },
        // ... other fields
    },
    {
        Metadata: &pb.EventMetadata{
            Slot: 346464000, // First slot of p401
        },
        // ... other fields
    },
}

err := repo.ProcessSolTransfers(ctx, events)
// Should automatically create p401 if it doesn't exist
```

## Verification Checklist

### ✅ Core Functionality
- [ ] `TestCalculatePartitionName` passes
- [ ] `TestCalculatePartitionBounds` passes  
- [ ] `TestEnsurePartitionExists` passes
- [ ] `TestPreCreateNextPartitionIfNeeded` passes

### ✅ Cross-Partition Handling
- [ ] `TestCrossPartitionTransfers` passes
- [ ] `TestCrossPartitionTokenTransfers` passes
- [ ] `TestPartitionGrouping` passes

### ✅ Performance
- [ ] Benchmark tests run without errors
- [ ] Memory usage is reasonable (check with `-benchmem`)
- [ ] No race conditions detected (`./run_tests.sh race`)

### ✅ Database Integration
- [ ] Partitions are created automatically
- [ ] Data is inserted into correct partitions
- [ ] Conflict resolution works (`ON CONFLICT DO NOTHING`)
- [ ] Transaction rollback works properly

## Troubleshooting

### Common Issues

1. **Test Database Connection Failed**
   ```
   Error: PostgreSQL is not running on localhost:5432
   ```
   **Solution**: Start PostgreSQL service
   ```bash
   # macOS
   brew services start postgresql
   
   # Linux (systemd)
   sudo systemctl start postgresql
   
   # Docker
   docker run -d -p 5432:5432 -e POSTGRES_PASSWORD=postgres postgres
   ```

2. **Partition Creation Failed**
   ```
   Error: failed to create partition sol_transfers_p999
   ```
   **Solution**: Check database permissions and schema
   ```sql
   -- Verify base tables exist
   \d sol_transfers
   \d token_transfers
   
   -- Check existing partitions
   SELECT schemaname, tablename FROM pg_tables WHERE tablename LIKE '%_p%';
   ```

3. **Test Timeouts**
   ```
   Error: test timed out after 30s
   ```
   **Solution**: Increase timeout or check database performance
   ```bash
   go test ./db -v -timeout=60s
   ```

## Performance Expectations

### Before Optimization
- INSERT operations scan all partitions for conflict checking
- Higher memory usage due to partition lookup
- Slower writes during partition boundaries

### After Optimization  
- Direct partition writes (5-10x faster for large datasets)
- Reduced memory footprint
- Seamless partition boundary handling
- Pre-creation eliminates boundary delays

### Benchmark Results Format
```
BenchmarkPartitionCalculation/CalculatePartitionName-8    1000000   1234 ns/op    0 B/op    0 allocs/op
BenchmarkSolTransferInsert/OptimizedPartitionInsert-8     100       12345678 ns/op    1024 B/op    10 allocs/op
```

## Coverage Analysis

Generate and view coverage report:
```bash
./run_tests.sh coverage
open coverage.html
```

Target coverage for partition functionality:
- **Partition calculation functions**: 100%
- **Auto-creation logic**: 95%+
- **Error handling**: 90%+
- **Integration tests**: 85%+

## Next Steps

After all tests pass:

1. **Deploy to staging environment**
2. **Monitor partition creation logs**
3. **Verify performance improvements**
4. **Test with real data workloads**

## Contact

If you encounter issues with the tests:
1. Check this guide first
2. Run `./run_tests.sh help` for options
3. Review test output for specific error messages
4. Verify PostgreSQL configuration and permissions 