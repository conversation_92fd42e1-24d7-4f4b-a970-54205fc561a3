package logger

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"cloud.google.com/go/logging"
)

var (
	client *logging.Client
	logger *Logger
	mu     sync.Mutex
)

// LogEntry represents a structured log entry in GCP format
type LogEntry struct {
	Severity       string                 `json:"severity"`
	Timestamp      string                 `json:"timestamp"`
	Message        string                 `json:"message"`
	JSONPayload    map[string]interface{} `json:"jsonPayload,omitempty"`
	Labels         map[string]string      `json:"labels,omitempty"`
	SourceLocation *SourceLocation        `json:"sourceLocation,omitempty"`
}

type SourceLocation struct {
	File string `json:"file"`
	Line string `json:"line"`
}

// init initializes the default logger
func init() {
	logger = NewLogger("data-writer")
}

// Init sets up the logging client
func Init(ctx context.Context, name string) error {
	var err error
	client, err = logging.NewClient(ctx, "")
	if err != nil {
		return fmt.Errorf("failed to create logging client: %w", err)
	}
	logger = NewLogger(name)
	return nil
}

// Close closes the logging client
func Close() error {
	if client != nil {
		return client.Close()
	}
	return nil
}

// Logger wraps the GCP Cloud Logging logger
type Logger struct {
	logger *logging.Logger
	name   string
}

// NewLogger creates a new logger instance
func NewLogger(name string) *Logger {
	return &Logger{
		name: name,
	}
}

// logEntry creates a logging entry with proper context and severity
func (l *Logger) logEntry(ctx context.Context, severity string, format string, args ...interface{}) {
	if l == nil {
		// Fallback to standard log if logger is nil
		fmt.Printf("[%s] %s: %s\n", time.Now().Format(time.RFC3339), severity, fmt.Sprintf(format, args...))
		return
	}

	entry := LogEntry{
		Severity:  severity,
		Timestamp: time.Now().UTC().Format(time.RFC3339Nano),
		Message:   fmt.Sprintf(format, args...),
		JSONPayload: map[string]interface{}{
			"message": fmt.Sprintf(format, args...),
		},
		Labels: map[string]string{
			"service": l.name,
		},
	}

	// Add trace context if available
	if traceID := ctx.Value("traceID"); traceID != nil {
		entry.JSONPayload["traceId"] = fmt.Sprintf("%v", traceID)
	}

	// Marshal to JSON and print to stdout
	jsonData, err := json.Marshal(entry)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error marshaling log entry: %v\n", err)
		return
	}
	fmt.Println(string(jsonData))
}

// Info logs an info message
func Info(ctx context.Context, format string, args ...interface{}) {
	if logger == nil {
		fmt.Printf("[%s] INFO: %s\n", time.Now().Format(time.RFC3339), fmt.Sprintf(format, args...))
		return
	}
	logger.logEntry(ctx, "INFO", format, args...)
}

// Error logs an error message
func Error(ctx context.Context, format string, args ...interface{}) {
	if logger == nil {
		fmt.Printf("[%s] ERROR: %s\n", time.Now().Format(time.RFC3339), fmt.Sprintf(format, args...))
		return
	}
	logger.logEntry(ctx, "ERROR", format, args...)
}

// Debug logs a debug message
func Debug(ctx context.Context, format string, args ...interface{}) {
	if logger == nil {
		fmt.Printf("[%s] DEBUG: %s\n", time.Now().Format(time.RFC3339), fmt.Sprintf(format, args...))
		return
	}
	logger.logEntry(ctx, "DEBUG", format, args...)
}

// Warning logs a warning message
func Warning(ctx context.Context, format string, args ...interface{}) {
	if logger == nil {
		fmt.Printf("[%s] WARNING: %s\n", time.Now().Format(time.RFC3339), fmt.Sprintf(format, args...))
		return
	}
	logger.logEntry(ctx, "WARNING", format, args...)
}
