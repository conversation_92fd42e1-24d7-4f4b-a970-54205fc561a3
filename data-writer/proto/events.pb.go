// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/events.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Signature     []byte                 `protobuf:"bytes,1,opt,name=signature,proto3" json:"signature,omitempty"`
	Slot          uint64                 `protobuf:"varint,2,opt,name=slot,proto3" json:"slot,omitempty"`
	IxIndex       uint32                 `protobuf:"varint,3,opt,name=ix_index,json=ixIndex,proto3" json:"ix_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventMetadata) Reset() {
	*x = EventMetadata{}
	mi := &file_proto_events_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventMetadata) ProtoMessage() {}

func (x *EventMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventMetadata.ProtoReflect.Descriptor instead.
func (*EventMetadata) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{0}
}

func (x *EventMetadata) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *EventMetadata) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *EventMetadata) GetIxIndex() uint32 {
	if x != nil {
		return x.IxIndex
	}
	return 0
}

type SolTransferEvent struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *EventMetadata         `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	FromWallet     string                 `protobuf:"bytes,2,opt,name=from_wallet,json=fromWallet,proto3" json:"from_wallet,omitempty"`
	ToWallet       string                 `protobuf:"bytes,3,opt,name=to_wallet,json=toWallet,proto3" json:"to_wallet,omitempty"`
	AmountLamports uint64                 `protobuf:"varint,4,opt,name=amount_lamports,json=amountLamports,proto3" json:"amount_lamports,omitempty"` // in lamports
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SolTransferEvent) Reset() {
	*x = SolTransferEvent{}
	mi := &file_proto_events_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SolTransferEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SolTransferEvent) ProtoMessage() {}

func (x *SolTransferEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SolTransferEvent.ProtoReflect.Descriptor instead.
func (*SolTransferEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{1}
}

func (x *SolTransferEvent) GetMetadata() *EventMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SolTransferEvent) GetFromWallet() string {
	if x != nil {
		return x.FromWallet
	}
	return ""
}

func (x *SolTransferEvent) GetToWallet() string {
	if x != nil {
		return x.ToWallet
	}
	return ""
}

func (x *SolTransferEvent) GetAmountLamports() uint64 {
	if x != nil {
		return x.AmountLamports
	}
	return 0
}

type TokenTransferEvent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *EventMetadata         `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	TokenMint     string                 `protobuf:"bytes,2,opt,name=token_mint,json=tokenMint,proto3" json:"token_mint,omitempty"`
	FromWallet    string                 `protobuf:"bytes,3,opt,name=from_wallet,json=fromWallet,proto3" json:"from_wallet,omitempty"`
	ToWallet      string                 `protobuf:"bytes,4,opt,name=to_wallet,json=toWallet,proto3" json:"to_wallet,omitempty"`
	Amount        string                 `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenTransferEvent) Reset() {
	*x = TokenTransferEvent{}
	mi := &file_proto_events_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenTransferEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenTransferEvent) ProtoMessage() {}

func (x *TokenTransferEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenTransferEvent.ProtoReflect.Descriptor instead.
func (*TokenTransferEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{2}
}

func (x *TokenTransferEvent) GetMetadata() *EventMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *TokenTransferEvent) GetTokenMint() string {
	if x != nil {
		return x.TokenMint
	}
	return ""
}

func (x *TokenTransferEvent) GetFromWallet() string {
	if x != nil {
		return x.FromWallet
	}
	return ""
}

func (x *TokenTransferEvent) GetToWallet() string {
	if x != nil {
		return x.ToWallet
	}
	return ""
}

func (x *TokenTransferEvent) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

// New event types for the updated schema
type SolTransferEventV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Slot          uint64                 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	TxIndex       uint32                 `protobuf:"varint,2,opt,name=tx_index,json=txIndex,proto3" json:"tx_index,omitempty"`
	IxIndex       uint32                 `protobuf:"varint,3,opt,name=ix_index,json=ixIndex,proto3" json:"ix_index,omitempty"`
	FromWallet    []byte                 `protobuf:"bytes,4,opt,name=from_wallet,json=fromWallet,proto3" json:"from_wallet,omitempty"`
	ToWallet      []byte                 `protobuf:"bytes,5,opt,name=to_wallet,json=toWallet,proto3" json:"to_wallet,omitempty"`
	Amount        uint64                 `protobuf:"varint,6,opt,name=amount,proto3" json:"amount,omitempty"` // in lamports
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SolTransferEventV2) Reset() {
	*x = SolTransferEventV2{}
	mi := &file_proto_events_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SolTransferEventV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SolTransferEventV2) ProtoMessage() {}

func (x *SolTransferEventV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SolTransferEventV2.ProtoReflect.Descriptor instead.
func (*SolTransferEventV2) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{3}
}

func (x *SolTransferEventV2) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SolTransferEventV2) GetTxIndex() uint32 {
	if x != nil {
		return x.TxIndex
	}
	return 0
}

func (x *SolTransferEventV2) GetIxIndex() uint32 {
	if x != nil {
		return x.IxIndex
	}
	return 0
}

func (x *SolTransferEventV2) GetFromWallet() []byte {
	if x != nil {
		return x.FromWallet
	}
	return nil
}

func (x *SolTransferEventV2) GetToWallet() []byte {
	if x != nil {
		return x.ToWallet
	}
	return nil
}

func (x *SolTransferEventV2) GetAmount() uint64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type TokenTransferEventV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Slot          uint64                 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	TxIndex       uint32                 `protobuf:"varint,2,opt,name=tx_index,json=txIndex,proto3" json:"tx_index,omitempty"`
	IxIndex       uint32                 `protobuf:"varint,3,opt,name=ix_index,json=ixIndex,proto3" json:"ix_index,omitempty"`
	TokenMint     []byte                 `protobuf:"bytes,4,opt,name=token_mint,json=tokenMint,proto3" json:"token_mint,omitempty"`
	FromWallet    []byte                 `protobuf:"bytes,5,opt,name=from_wallet,json=fromWallet,proto3" json:"from_wallet,omitempty"`
	ToWallet      []byte                 `protobuf:"bytes,6,opt,name=to_wallet,json=toWallet,proto3" json:"to_wallet,omitempty"`
	Amount        uint64                 `protobuf:"varint,7,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenTransferEventV2) Reset() {
	*x = TokenTransferEventV2{}
	mi := &file_proto_events_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenTransferEventV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenTransferEventV2) ProtoMessage() {}

func (x *TokenTransferEventV2) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenTransferEventV2.ProtoReflect.Descriptor instead.
func (*TokenTransferEventV2) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{4}
}

func (x *TokenTransferEventV2) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *TokenTransferEventV2) GetTxIndex() uint32 {
	if x != nil {
		return x.TxIndex
	}
	return 0
}

func (x *TokenTransferEventV2) GetIxIndex() uint32 {
	if x != nil {
		return x.IxIndex
	}
	return 0
}

func (x *TokenTransferEventV2) GetTokenMint() []byte {
	if x != nil {
		return x.TokenMint
	}
	return nil
}

func (x *TokenTransferEventV2) GetFromWallet() []byte {
	if x != nil {
		return x.FromWallet
	}
	return nil
}

func (x *TokenTransferEventV2) GetToWallet() []byte {
	if x != nil {
		return x.ToWallet
	}
	return nil
}

func (x *TokenTransferEventV2) GetAmount() uint64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type TransactionEvent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Slot          uint64                 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	TxIndex       uint32                 `protobuf:"varint,2,opt,name=tx_index,json=txIndex,proto3" json:"tx_index,omitempty"`
	Signature     []byte                 `protobuf:"bytes,3,opt,name=signature,proto3" json:"signature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionEvent) Reset() {
	*x = TransactionEvent{}
	mi := &file_proto_events_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionEvent) ProtoMessage() {}

func (x *TransactionEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionEvent.ProtoReflect.Descriptor instead.
func (*TransactionEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{5}
}

func (x *TransactionEvent) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *TransactionEvent) GetTxIndex() uint32 {
	if x != nil {
		return x.TxIndex
	}
	return 0
}

func (x *TransactionEvent) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

type SolBalanceEvent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Slot          uint64                 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Account       []byte                 `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Amount        uint64                 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"` // in lamports
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SolBalanceEvent) Reset() {
	*x = SolBalanceEvent{}
	mi := &file_proto_events_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SolBalanceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SolBalanceEvent) ProtoMessage() {}

func (x *SolBalanceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SolBalanceEvent.ProtoReflect.Descriptor instead.
func (*SolBalanceEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{6}
}

func (x *SolBalanceEvent) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SolBalanceEvent) GetAccount() []byte {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *SolBalanceEvent) GetAmount() uint64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type TokenBalanceEvent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Slot          uint64                 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Owner         []byte                 `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
	Token         []byte                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	Amount        uint64                 `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenBalanceEvent) Reset() {
	*x = TokenBalanceEvent{}
	mi := &file_proto_events_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenBalanceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenBalanceEvent) ProtoMessage() {}

func (x *TokenBalanceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenBalanceEvent.ProtoReflect.Descriptor instead.
func (*TokenBalanceEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{7}
}

func (x *TokenBalanceEvent) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *TokenBalanceEvent) GetOwner() []byte {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *TokenBalanceEvent) GetToken() []byte {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *TokenBalanceEvent) GetAmount() uint64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type SlotTime struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Slot          uint64                 `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"`
	Timestamp     uint64                 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SlotTime) Reset() {
	*x = SlotTime{}
	mi := &file_proto_events_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SlotTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotTime) ProtoMessage() {}

func (x *SlotTime) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotTime.ProtoReflect.Descriptor instead.
func (*SlotTime) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{8}
}

func (x *SlotTime) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *SlotTime) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type SolanaEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Event:
	//
	//	*SolanaEvent_SolTransfer
	//	*SolanaEvent_TokenTransfer
	//	*SolanaEvent_SlotTime
	//	*SolanaEvent_SolTransferV2
	//	*SolanaEvent_TokenTransferV2
	//	*SolanaEvent_Transaction
	//	*SolanaEvent_SolBalance
	//	*SolanaEvent_TokenBalance
	Event         isSolanaEvent_Event `protobuf_oneof:"event"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SolanaEvent) Reset() {
	*x = SolanaEvent{}
	mi := &file_proto_events_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SolanaEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SolanaEvent) ProtoMessage() {}

func (x *SolanaEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SolanaEvent.ProtoReflect.Descriptor instead.
func (*SolanaEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{9}
}

func (x *SolanaEvent) GetEvent() isSolanaEvent_Event {
	if x != nil {
		return x.Event
	}
	return nil
}

func (x *SolanaEvent) GetSolTransfer() *SolTransferEvent {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_SolTransfer); ok {
			return x.SolTransfer
		}
	}
	return nil
}

func (x *SolanaEvent) GetTokenTransfer() *TokenTransferEvent {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_TokenTransfer); ok {
			return x.TokenTransfer
		}
	}
	return nil
}

func (x *SolanaEvent) GetSlotTime() *SlotTime {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_SlotTime); ok {
			return x.SlotTime
		}
	}
	return nil
}

func (x *SolanaEvent) GetSolTransferV2() *SolTransferEventV2 {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_SolTransferV2); ok {
			return x.SolTransferV2
		}
	}
	return nil
}

func (x *SolanaEvent) GetTokenTransferV2() *TokenTransferEventV2 {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_TokenTransferV2); ok {
			return x.TokenTransferV2
		}
	}
	return nil
}

func (x *SolanaEvent) GetTransaction() *TransactionEvent {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_Transaction); ok {
			return x.Transaction
		}
	}
	return nil
}

func (x *SolanaEvent) GetSolBalance() *SolBalanceEvent {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_SolBalance); ok {
			return x.SolBalance
		}
	}
	return nil
}

func (x *SolanaEvent) GetTokenBalance() *TokenBalanceEvent {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_TokenBalance); ok {
			return x.TokenBalance
		}
	}
	return nil
}

type isSolanaEvent_Event interface {
	isSolanaEvent_Event()
}

type SolanaEvent_SolTransfer struct {
	SolTransfer *SolTransferEvent `protobuf:"bytes,1,opt,name=sol_transfer,json=solTransfer,proto3,oneof"`
}

type SolanaEvent_TokenTransfer struct {
	TokenTransfer *TokenTransferEvent `protobuf:"bytes,2,opt,name=token_transfer,json=tokenTransfer,proto3,oneof"`
}

type SolanaEvent_SlotTime struct {
	SlotTime *SlotTime `protobuf:"bytes,3,opt,name=slot_time,json=slotTime,proto3,oneof"`
}

type SolanaEvent_SolTransferV2 struct {
	SolTransferV2 *SolTransferEventV2 `protobuf:"bytes,4,opt,name=sol_transfer_v2,json=solTransferV2,proto3,oneof"`
}

type SolanaEvent_TokenTransferV2 struct {
	TokenTransferV2 *TokenTransferEventV2 `protobuf:"bytes,5,opt,name=token_transfer_v2,json=tokenTransferV2,proto3,oneof"`
}

type SolanaEvent_Transaction struct {
	Transaction *TransactionEvent `protobuf:"bytes,6,opt,name=transaction,proto3,oneof"`
}

type SolanaEvent_SolBalance struct {
	SolBalance *SolBalanceEvent `protobuf:"bytes,7,opt,name=sol_balance,json=solBalance,proto3,oneof"`
}

type SolanaEvent_TokenBalance struct {
	TokenBalance *TokenBalanceEvent `protobuf:"bytes,8,opt,name=token_balance,json=tokenBalance,proto3,oneof"`
}

func (*SolanaEvent_SolTransfer) isSolanaEvent_Event() {}

func (*SolanaEvent_TokenTransfer) isSolanaEvent_Event() {}

func (*SolanaEvent_SlotTime) isSolanaEvent_Event() {}

func (*SolanaEvent_SolTransferV2) isSolanaEvent_Event() {}

func (*SolanaEvent_TokenTransferV2) isSolanaEvent_Event() {}

func (*SolanaEvent_Transaction) isSolanaEvent_Event() {}

func (*SolanaEvent_SolBalance) isSolanaEvent_Event() {}

func (*SolanaEvent_TokenBalance) isSolanaEvent_Event() {}

// Topic: solana-events
type BatchedSolanaEvent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Events        []*SolanaEvent         `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchedSolanaEvent) Reset() {
	*x = BatchedSolanaEvent{}
	mi := &file_proto_events_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchedSolanaEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchedSolanaEvent) ProtoMessage() {}

func (x *BatchedSolanaEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchedSolanaEvent.ProtoReflect.Descriptor instead.
func (*BatchedSolanaEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{10}
}

func (x *BatchedSolanaEvent) GetEvents() []*SolanaEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

var File_proto_events_proto protoreflect.FileDescriptor

const file_proto_events_proto_rawDesc = "" +
	"\n" +
	"\x12proto/events.proto\x12\veventstream\"\\\n" +
	"\rEventMetadata\x12\x1c\n" +
	"\tsignature\x18\x01 \x01(\fR\tsignature\x12\x12\n" +
	"\x04slot\x18\x02 \x01(\x04R\x04slot\x12\x19\n" +
	"\bix_index\x18\x03 \x01(\rR\aixIndex\"\xb1\x01\n" +
	"\x10SolTransferEvent\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.eventstream.EventMetadataR\bmetadata\x12\x1f\n" +
	"\vfrom_wallet\x18\x02 \x01(\tR\n" +
	"fromWallet\x12\x1b\n" +
	"\tto_wallet\x18\x03 \x01(\tR\btoWallet\x12'\n" +
	"\x0famount_lamports\x18\x04 \x01(\x04R\x0eamountLamports\"\xc1\x01\n" +
	"\x12TokenTransferEvent\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.eventstream.EventMetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"token_mint\x18\x02 \x01(\tR\ttokenMint\x12\x1f\n" +
	"\vfrom_wallet\x18\x03 \x01(\tR\n" +
	"fromWallet\x12\x1b\n" +
	"\tto_wallet\x18\x04 \x01(\tR\btoWallet\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\"\xb4\x01\n" +
	"\x12SolTransferEventV2\x12\x12\n" +
	"\x04slot\x18\x01 \x01(\x04R\x04slot\x12\x19\n" +
	"\btx_index\x18\x02 \x01(\rR\atxIndex\x12\x19\n" +
	"\bix_index\x18\x03 \x01(\rR\aixIndex\x12\x1f\n" +
	"\vfrom_wallet\x18\x04 \x01(\fR\n" +
	"fromWallet\x12\x1b\n" +
	"\tto_wallet\x18\x05 \x01(\fR\btoWallet\x12\x16\n" +
	"\x06amount\x18\x06 \x01(\x04R\x06amount\"\xd5\x01\n" +
	"\x14TokenTransferEventV2\x12\x12\n" +
	"\x04slot\x18\x01 \x01(\x04R\x04slot\x12\x19\n" +
	"\btx_index\x18\x02 \x01(\rR\atxIndex\x12\x19\n" +
	"\bix_index\x18\x03 \x01(\rR\aixIndex\x12\x1d\n" +
	"\n" +
	"token_mint\x18\x04 \x01(\fR\ttokenMint\x12\x1f\n" +
	"\vfrom_wallet\x18\x05 \x01(\fR\n" +
	"fromWallet\x12\x1b\n" +
	"\tto_wallet\x18\x06 \x01(\fR\btoWallet\x12\x16\n" +
	"\x06amount\x18\a \x01(\x04R\x06amount\"_\n" +
	"\x10TransactionEvent\x12\x12\n" +
	"\x04slot\x18\x01 \x01(\x04R\x04slot\x12\x19\n" +
	"\btx_index\x18\x02 \x01(\rR\atxIndex\x12\x1c\n" +
	"\tsignature\x18\x03 \x01(\fR\tsignature\"W\n" +
	"\x0fSolBalanceEvent\x12\x12\n" +
	"\x04slot\x18\x01 \x01(\x04R\x04slot\x12\x18\n" +
	"\aaccount\x18\x02 \x01(\fR\aaccount\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x04R\x06amount\"k\n" +
	"\x11TokenBalanceEvent\x12\x12\n" +
	"\x04slot\x18\x01 \x01(\x04R\x04slot\x12\x14\n" +
	"\x05owner\x18\x02 \x01(\fR\x05owner\x12\x14\n" +
	"\x05token\x18\x03 \x01(\fR\x05token\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x04R\x06amount\"<\n" +
	"\bSlotTime\x12\x12\n" +
	"\x04slot\x18\x01 \x01(\x04R\x04slot\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x04R\ttimestamp\"\xc1\x04\n" +
	"\vSolanaEvent\x12B\n" +
	"\fsol_transfer\x18\x01 \x01(\v2\x1d.eventstream.SolTransferEventH\x00R\vsolTransfer\x12H\n" +
	"\x0etoken_transfer\x18\x02 \x01(\v2\x1f.eventstream.TokenTransferEventH\x00R\rtokenTransfer\x124\n" +
	"\tslot_time\x18\x03 \x01(\v2\x15.eventstream.SlotTimeH\x00R\bslotTime\x12I\n" +
	"\x0fsol_transfer_v2\x18\x04 \x01(\v2\x1f.eventstream.SolTransferEventV2H\x00R\rsolTransferV2\x12O\n" +
	"\x11token_transfer_v2\x18\x05 \x01(\v2!.eventstream.TokenTransferEventV2H\x00R\x0ftokenTransferV2\x12A\n" +
	"\vtransaction\x18\x06 \x01(\v2\x1d.eventstream.TransactionEventH\x00R\vtransaction\x12?\n" +
	"\vsol_balance\x18\a \x01(\v2\x1c.eventstream.SolBalanceEventH\x00R\n" +
	"solBalance\x12E\n" +
	"\rtoken_balance\x18\b \x01(\v2\x1e.eventstream.TokenBalanceEventH\x00R\ftokenBalanceB\a\n" +
	"\x05event\"F\n" +
	"\x12BatchedSolanaEvent\x120\n" +
	"\x06events\x18\x01 \x03(\v2\x18.eventstream.SolanaEventR\x06eventsB6Z4github.com/kryptogo/kg-solana-data/data-writer/protob\x06proto3"

var (
	file_proto_events_proto_rawDescOnce sync.Once
	file_proto_events_proto_rawDescData []byte
)

func file_proto_events_proto_rawDescGZIP() []byte {
	file_proto_events_proto_rawDescOnce.Do(func() {
		file_proto_events_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_events_proto_rawDesc), len(file_proto_events_proto_rawDesc)))
	})
	return file_proto_events_proto_rawDescData
}

var file_proto_events_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_proto_events_proto_goTypes = []any{
	(*EventMetadata)(nil),        // 0: eventstream.EventMetadata
	(*SolTransferEvent)(nil),     // 1: eventstream.SolTransferEvent
	(*TokenTransferEvent)(nil),   // 2: eventstream.TokenTransferEvent
	(*SolTransferEventV2)(nil),   // 3: eventstream.SolTransferEventV2
	(*TokenTransferEventV2)(nil), // 4: eventstream.TokenTransferEventV2
	(*TransactionEvent)(nil),     // 5: eventstream.TransactionEvent
	(*SolBalanceEvent)(nil),      // 6: eventstream.SolBalanceEvent
	(*TokenBalanceEvent)(nil),    // 7: eventstream.TokenBalanceEvent
	(*SlotTime)(nil),             // 8: eventstream.SlotTime
	(*SolanaEvent)(nil),          // 9: eventstream.SolanaEvent
	(*BatchedSolanaEvent)(nil),   // 10: eventstream.BatchedSolanaEvent
}
var file_proto_events_proto_depIdxs = []int32{
	0,  // 0: eventstream.SolTransferEvent.metadata:type_name -> eventstream.EventMetadata
	0,  // 1: eventstream.TokenTransferEvent.metadata:type_name -> eventstream.EventMetadata
	1,  // 2: eventstream.SolanaEvent.sol_transfer:type_name -> eventstream.SolTransferEvent
	2,  // 3: eventstream.SolanaEvent.token_transfer:type_name -> eventstream.TokenTransferEvent
	8,  // 4: eventstream.SolanaEvent.slot_time:type_name -> eventstream.SlotTime
	3,  // 5: eventstream.SolanaEvent.sol_transfer_v2:type_name -> eventstream.SolTransferEventV2
	4,  // 6: eventstream.SolanaEvent.token_transfer_v2:type_name -> eventstream.TokenTransferEventV2
	5,  // 7: eventstream.SolanaEvent.transaction:type_name -> eventstream.TransactionEvent
	6,  // 8: eventstream.SolanaEvent.sol_balance:type_name -> eventstream.SolBalanceEvent
	7,  // 9: eventstream.SolanaEvent.token_balance:type_name -> eventstream.TokenBalanceEvent
	9,  // 10: eventstream.BatchedSolanaEvent.events:type_name -> eventstream.SolanaEvent
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_proto_events_proto_init() }
func file_proto_events_proto_init() {
	if File_proto_events_proto != nil {
		return
	}
	file_proto_events_proto_msgTypes[9].OneofWrappers = []any{
		(*SolanaEvent_SolTransfer)(nil),
		(*SolanaEvent_TokenTransfer)(nil),
		(*SolanaEvent_SlotTime)(nil),
		(*SolanaEvent_SolTransferV2)(nil),
		(*SolanaEvent_TokenTransferV2)(nil),
		(*SolanaEvent_Transaction)(nil),
		(*SolanaEvent_SolBalance)(nil),
		(*SolanaEvent_TokenBalance)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_events_proto_rawDesc), len(file_proto_events_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_events_proto_goTypes,
		DependencyIndexes: file_proto_events_proto_depIdxs,
		MessageInfos:      file_proto_events_proto_msgTypes,
	}.Build()
	File_proto_events_proto = out.File
	file_proto_events_proto_goTypes = nil
	file_proto_events_proto_depIdxs = nil
}
