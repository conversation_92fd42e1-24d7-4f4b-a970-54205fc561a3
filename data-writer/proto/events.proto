syntax = "proto3";

package eventstream;

option go_package = "github.com/kryptogo/kg-solana-data/data-writer/proto";

message EventMetadata {
    bytes signature = 1;
    uint64 slot = 2;
    uint32 ix_index = 3;
}

message SolTransferEvent {
    EventMetadata metadata = 1;
    string from_wallet = 2;
    string to_wallet = 3;
    uint64 amount_lamports = 4; // in lamports
}

message TokenTransferEvent {
    EventMetadata metadata = 1;
    string token_mint = 2;
    string from_wallet = 3;
    string to_wallet = 4;
    string amount = 5;
}

// New event types for the updated schema
message SolTransferEventV2 {
    uint64 slot = 1;
    uint32 tx_index = 2;
    uint32 ix_index = 3;
    bytes from_wallet = 4;
    bytes to_wallet = 5;
    uint64 amount = 6; // in lamports
}

message TokenTransferEventV2 {
    uint64 slot = 1;
    uint32 tx_index = 2;
    uint32 ix_index = 3;
    bytes token_mint = 4;
    bytes from_wallet = 5;
    bytes to_wallet = 6;
    uint64 amount = 7;
}

message TransactionEvent {
    uint64 slot = 1;
    uint32 tx_index = 2;
    bytes signature = 3;
}

message SolBalanceEvent {
    uint64 slot = 1;
    bytes account = 2;
    uint64 amount = 3; // in lamports
}

message TokenBalanceEvent {
    uint64 slot = 1;
    bytes owner = 2;
    bytes token = 3;
    uint64 amount = 4;
}

message SlotTime {
    uint64 slot = 1;
    uint64 timestamp = 2;
}

message SolanaEvent {
    oneof event {
        SolTransferEvent sol_transfer = 1;
        TokenTransferEvent token_transfer = 2;
        SlotTime slot_time = 3;
        SolTransferEventV2 sol_transfer_v2 = 4;
        TokenTransferEventV2 token_transfer_v2 = 5;
        TransactionEvent transaction = 6;
        SolBalanceEvent sol_balance = 7;
        TokenBalanceEvent token_balance = 8;
    }
}

// Topic: solana-events
message BatchedSolanaEvent {
    repeated SolanaEvent events = 1;
}

// Topic: solana-burns
// message BurnEvent {
//     EventMetadata metadata = 1;
//     string token = 2;
//     string burner = 3;
//     string amount = 4;
// }

// enum EventSource {
//     UNKNOWN_SOURCE = 0;
//     JUPITER = 1;
//     RAYDIUM_CLMM = 2;
//     RAYDIUM_CPMM = 3;
//     ORCA_WHIRLPOOL = 4;
//     METEORA_DLMM = 5;
//     PUMP_FUN = 6;
// }

// enum LiquidityChangeType {
//     UNKNOWN_CHANGE = 0;
//     ADD = 1;
//     REMOVE = 2;
// }

// Topic: solana-new-pairs
// message NewPairEvent {
//     EventMetadata metadata = 1;
//     EventSource source = 2;
//     string amm_address = 3;
//     string base_token = 4;
//     string quote_token = 5;
//     string creator = 6;
//     uint64 initial_base_liquidity = 7;
//     uint64 initial_quote_liquidity = 8;
// }

// Topic: solana-trades
// message TradeEvent {
//     EventMetadata metadata = 1;
//     EventSource source = 2;
//     string amm_address = 3;
//     string trader = 4;
//     string input_token = 5;
//     string input_amount = 6;
//     string output_token = 7;
//     string output_amount = 8;
//     double price_in_quote = 9;
// }

// Topic: solana-liquidity-changes
// message LiquidityChangeEvent {
//     EventMetadata metadata = 1;
//     EventSource source = 2;
//     LiquidityChangeType type = 3;
//     string amm_address = 4;
//     string authority = 5;            // wallet addr
//     string token_a = 6;
//     string token_a_amount = 7;
//     string token_b = 8;
//     string token_b_amount = 9;
// }

// Topic: solana-limit-orders
// enum LimitOrderSide {
//     UNKNOWN_SIDE = 0;
//     BID = 1; // b
//     ASK = 2; // sell
// }

// enum LimitOrderStatus {
//     UNKNOWN_STATUS = 0;
//     CREATED = 1;
//     CANCELLED = 2;
//     FILLED = 3; // Trigger trade event as well
// }

// message LimitOrderEvent {
//     EventMetadata metadata = 1;
//     EventSource source = 2;
//     LimitOrderStatus status = 3;
//     string amm_address = 4;
//     string owner = 5;
//     string order_id = 6;          // order ID if any
//     LimitOrderSide side = 7;      // b/s
//     string base_token = 8;
//     string quote_token = 9;
//     double price = 10;
//     string amount = 11;
// }

// Topic: solana-account-balance-updates
// message AccountBalanceUpdateEvent {
//     EventMetadata metadata = 1;
//     string account = 2; // address
//     oneof balance_type {
//         SolBalance sol_balance = 3;
//         TokenBalance token_balance = 4;
//     }
// }

// message SolBalance {
//     uint64 lamports = 1;
// }

// message TokenBalance {
//     string token = 1;
//     string amount = 2;
// }