package db

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/kryptogo/kg-solana-data/data-writer/models"
	pb "github.com/kryptogo/kg-solana-data/data-writer/proto"
)

// GetTokenBalanceChanges returns the sum of token transfers for given wallets and token mint within a slot range
func (r *Repo) GetTokenBalanceChanges(ctx context.Context, wallets [][]byte, tokenMint []byte, startSlot, endSlot uint64) (int64, error) {
	query := `
		SELECT COALESCE(SUM(amount), 0) as total
		FROM token_transfer_events
		WHERE to_wallet = ANY($1)
		AND token_mint = $2
		AND slot >= $3 AND slot <= $4`

	var totalIn int64
	err := r.pool.QueryRow(ctx, query, wallets, tokenMint, startSlot, endSlot).Scan(&totalIn)
	if err != nil {
		return 0, fmt.Errorf("failed to get token balance changes (in): %v", err)
	}

	query = `
		SELECT COALESCE(SUM(amount), 0) as total
		FROM token_transfer_events
		WHERE from_wallet = ANY($1)
		AND token_mint = $2
		AND slot >= $3 AND slot <= $4`

	var totalOut int64
	err = r.pool.QueryRow(ctx, query, wallets, tokenMint, startSlot, endSlot).Scan(&totalOut)
	if err != nil {
		return 0, fmt.Errorf("failed to get token balance changes (out): %v", err)
	}

	return totalIn - totalOut, nil
}

func (r *Repo) ProcessSlotTimes(ctx context.Context, events []*pb.SlotTime) error {
	if len(events) == 0 {
		return nil
	}

	// Convert events to slot times
	slotTimes := make([]models.SlotTime, 0, len(events))
	for _, event := range events {
		slotTimes = append(slotTimes, models.SlotTime{
			Slot:      event.Slot,
			Timestamp: event.Timestamp,
		})
	}

	// Process in batches
	for i := 0; i < len(slotTimes); i += maxBatchSize {
		end := i + maxBatchSize
		if end > len(slotTimes) {
			end = len(slotTimes)
		}

		batch := slotTimes[i:end]
		if err := r.insertSlotTimeBatch(ctx, batch); err != nil {
			return fmt.Errorf("failed to insert slot time batch: %v", err)
		}
	}

	return nil
}

func (r *Repo) insertSlotTimeBatch(ctx context.Context, slotTimes []models.SlotTime) error {
	var err error
	for retry := 0; retry < maxRetries; retry++ {
		err = r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
			// Create temporary table
			if _, err := tx.Exec(ctx, "CREATE TEMPORARY TABLE temp_batch (LIKE slot_times) ON COMMIT DROP"); err != nil {
				return fmt.Errorf("failed to create temporary table: %v", err)
			}

			// Copy data to temporary table
			rows := make([][]interface{}, len(slotTimes))
			for i, st := range slotTimes {
				rows[i] = []interface{}{
					st.Slot,
					st.Timestamp,
				}
			}

			_, err = tx.CopyFrom(ctx,
				pgx.Identifier{"temp_batch"},
				[]string{"slot", "timestamp"},
				pgx.CopyFromRows(rows))
			if err != nil {
				return fmt.Errorf("failed to copy to temporary table: %v", err)
			}

			// Insert from temporary table with conflict handling
			if _, err := tx.Exec(ctx, `
				INSERT INTO slot_times 
				SELECT slot, timestamp FROM temp_batch 
				ON CONFLICT (slot) DO UPDATE 
				SET timestamp = EXCLUDED.timestamp`); err != nil {
				return fmt.Errorf("failed to insert from temporary table: %v", err)
			}

			return nil
		})
		if err == nil {
			return nil
		}
		time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
	}
	return err
}
