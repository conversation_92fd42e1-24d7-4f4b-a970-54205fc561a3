package db

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
)

const (

	// Process name for watermark persistence.
	BALANCE_HISTORY_PROCESS = "balance_history_etl"
)

// --- Watermark and Utility Functions ---

// GetBalanceHistoryWatermark retrieves the watermark for balance history processing.
func (r *Repo) GetBalanceHistoryWatermark(ctx context.Context) (lastProcessedSlot, firstProcessedSlot uint64, err error) {
	query := `
        SELECT last_processed_slot, first_processed_slot 
        FROM etl_watermark 
        WHERE process_name = $1`

	err = r.pool.QueryRow(ctx, query, BALANCE_HISTORY_PROCESS).Scan(&lastProcessedSlot, &firstProcessedSlot)
	if err != nil {
		if err == pgx.ErrNoRows {
			// No watermark exists, return defaults.
			return 0, 0, nil
		}
		return 0, 0, fmt.Errorf("failed to get balance history watermark: %w", err)
	}
	return lastProcessedSlot, firstProcessedSlot, nil
}

// SetBalanceHistoryWatermark sets the watermark for balance history processing.
func (r *Repo) SetBalanceHistoryWatermark(ctx context.Context, lastProcessedSlot, firstProcessedSlot uint64) error {
	query := `
        INSERT INTO etl_watermark (process_name, last_processed_slot, first_processed_slot)
        VALUES ($1, $2, $3)
        ON CONFLICT (process_name) 
        DO UPDATE SET 
            last_processed_slot = EXCLUDED.last_processed_slot,
            first_processed_slot = EXCLUDED.first_processed_slot`

	_, err := r.pool.Exec(ctx, query, BALANCE_HISTORY_PROCESS, lastProcessedSlot, firstProcessedSlot)
	if err != nil {
		return fmt.Errorf("failed to set balance history watermark: %w", err)
	}
	return nil
}
