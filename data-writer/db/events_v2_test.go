package db

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-solana-data/data-writer/models"
	pb "github.com/kryptogo/kg-solana-data/data-writer/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// ensureTestPartitionsExistV2 creates the necessary partitions for V2 events tests
func ensureTestPartitionsExistV2(t *testing.T, repo *Repo, ctx context.Context) {
	// Test slots for new partition size (216000 per partition)
	testSlots := []uint64{
		0,
		86400000, // p400
		86616000, // p401
		86832000, // p402
		87048000, // p403
		87264000, // p404
		87480000, // p405
	}

	tables := []string{
		"sol_transfer_events",
		"token_transfer_events",
		"transaction_signatures",
		"sol_balances",
		"token_balances",
	}

	for _, table := range tables {
		for _, slot := range testSlots {
			if err := repo.EnsureNewPartitionExists(ctx, table, slot); err != nil {
				t.Fatalf("Failed to ensure %s partition exists for slot %d: %v", table, slot, err)
			}
		}
	}
}

func TestProcessSolTransferEventsV2(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	// Ensure test partitions exist
	ensureTestPartitionsExistV2(t, repo, ctx)

	// Test data
	events := []*pb.SolTransferEventV2{
		{
			Slot:       86400000,
			TxIndex:    0,
			IxIndex:    0,
			FromWallet: []byte("from_wallet_1"),
			ToWallet:   []byte("to_wallet_1"),
			Amount:     1000000,
		},
		{
			Slot:       86400001,
			TxIndex:    1,
			IxIndex:    1,
			FromWallet: []byte("from_wallet_2"),
			ToWallet:   []byte("to_wallet_2"),
			Amount:     2000000,
		},
	}

	// Test inserting events
	err = repo.ProcessSolTransferEventsV2(ctx, events)
	require.NoError(t, err)

	// Verify the events were inserted correctly
	for _, event := range events {
		var transfer models.SolTransferEventV2
		err := repo.pool.QueryRow(ctx, `
			SELECT slot, tx_index, ix_index, from_wallet, to_wallet, amount
			FROM sol_transfer_events
			WHERE slot = $1 AND tx_index = $2 AND ix_index = $3`,
			event.Slot, event.TxIndex, event.IxIndex,
		).Scan(
			&transfer.Slot,
			&transfer.TxIndex,
			&transfer.IxIndex,
			&transfer.FromWallet,
			&transfer.ToWallet,
			&transfer.Amount,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Slot, transfer.Slot)
		assert.Equal(t, event.TxIndex, transfer.TxIndex)
		assert.Equal(t, uint16(event.IxIndex), transfer.IxIndex)
		assert.Equal(t, event.FromWallet, transfer.FromWallet)
		assert.Equal(t, event.ToWallet, transfer.ToWallet)
		assert.Equal(t, event.Amount, transfer.Amount)

		// Also verify sol_wallet_interaction_map (both directions, only if amount >= 1_000_000_000 and from != to)
		if event.Amount >= 1_000_000_000 && string(event.FromWallet) != string(event.ToWallet) {
			var count int
			err = repo.pool.QueryRow(ctx, `
				SELECT COUNT(*) FROM sol_wallet_interaction_map
				WHERE source_wallet = $1 AND paired_wallet = $2`,
				event.FromWallet, event.ToWallet,
			).Scan(&count)
			require.NoError(t, err)
			assert.Equal(t, 1, count)

			err = repo.pool.QueryRow(ctx, `
				SELECT COUNT(*) FROM sol_wallet_interaction_map
				WHERE source_wallet = $1 AND paired_wallet = $2`,
				event.ToWallet, event.FromWallet,
			).Scan(&count)
			require.NoError(t, err)
			assert.Equal(t, 1, count)
		}
	}

	// Test duplicate handling
	err = repo.ProcessSolTransferEventsV2(ctx, events)
	require.NoError(t, err) // Should not error on duplicates

	// Verify no duplicates were inserted in sol_wallet_interaction_map
	for _, event := range events {
		if event.Amount >= 1_000_000_000 && string(event.FromWallet) != string(event.ToWallet) {
			var count int
			err = repo.pool.QueryRow(ctx, `
				SELECT COUNT(*) FROM sol_wallet_interaction_map
				WHERE source_wallet = $1 AND paired_wallet = $2`,
				event.FromWallet, event.ToWallet,
			).Scan(&count)
			require.NoError(t, err)
			assert.Equal(t, 1, count)

			err = repo.pool.QueryRow(ctx, `
				SELECT COUNT(*) FROM sol_wallet_interaction_map
				WHERE source_wallet = $1 AND paired_wallet = $2`,
				event.ToWallet, event.FromWallet,
			).Scan(&count)
			require.NoError(t, err)
			assert.Equal(t, 1, count)
		}
	}
}

func TestProcessTokenTransferEventsV2(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	// Ensure test partitions exist
	ensureTestPartitionsExistV2(t, repo, ctx)

	// Test data
	events := []*pb.TokenTransferEventV2{
		{
			Slot:       86400001,
			TxIndex:    0,
			IxIndex:    0,
			TokenMint:  []byte("token_mint_1"),
			FromWallet: []byte("from_wallet_1"),
			ToWallet:   []byte("to_wallet_1"),
			Amount:     1000000,
		},
		{
			Slot:       86400002,
			TxIndex:    1,
			IxIndex:    1,
			TokenMint:  []byte("token_mint_2"),
			FromWallet: []byte("from_wallet_2"),
			ToWallet:   []byte("to_wallet_2"),
			Amount:     2000000,
		},
	}

	// Test inserting events
	err = repo.ProcessTokenTransferEventsV2(ctx, events)
	require.NoError(t, err)

	// Verify the events were inserted correctly
	for _, event := range events {
		var transfer models.TokenTransferEventV2
		err := repo.pool.QueryRow(ctx, `
			SELECT slot, tx_index, ix_index, token_mint, from_wallet, to_wallet, amount
			FROM token_transfer_events
			WHERE slot = $1 AND tx_index = $2 AND ix_index = $3`,
			event.Slot, event.TxIndex, event.IxIndex,
		).Scan(
			&transfer.Slot,
			&transfer.TxIndex,
			&transfer.IxIndex,
			&transfer.TokenMint,
			&transfer.FromWallet,
			&transfer.ToWallet,
			&transfer.Amount,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Slot, transfer.Slot)
		assert.Equal(t, event.TxIndex, transfer.TxIndex)
		assert.Equal(t, uint16(event.IxIndex), transfer.IxIndex)
		assert.Equal(t, event.TokenMint, transfer.TokenMint)
		assert.Equal(t, event.FromWallet, transfer.FromWallet)
		assert.Equal(t, event.ToWallet, transfer.ToWallet)
		assert.Equal(t, event.Amount, transfer.Amount)

		// Also verify token_wallet_interaction_map (both directions)
		var count int
		err = repo.pool.QueryRow(ctx, `
			SELECT COUNT(*) FROM token_wallet_interaction_map
			WHERE token_mint = $1 AND source_wallet = $2 AND paired_wallet = $3`,
			event.TokenMint, event.FromWallet, event.ToWallet,
		).Scan(&count)
		require.NoError(t, err)
		assert.Equal(t, 1, count)

		err = repo.pool.QueryRow(ctx, `
			SELECT COUNT(*) FROM token_wallet_interaction_map
			WHERE token_mint = $1 AND source_wallet = $2 AND paired_wallet = $3`,
			event.TokenMint, event.ToWallet, event.FromWallet,
		).Scan(&count)
		require.NoError(t, err)
		assert.Equal(t, 1, count)
	}

	// Test duplicate handling
	err = repo.ProcessTokenTransferEventsV2(ctx, events)
	require.NoError(t, err) // Should not error on duplicates

	// Verify no duplicates were inserted in token_wallet_interaction_map
	for _, event := range events {
		var count int
		err = repo.pool.QueryRow(ctx, `
			SELECT COUNT(*) FROM token_wallet_interaction_map
			WHERE token_mint = $1 AND source_wallet = $2 AND paired_wallet = $3`,
			event.TokenMint, event.FromWallet, event.ToWallet,
		).Scan(&count)
		require.NoError(t, err)
		assert.Equal(t, 1, count)

		err = repo.pool.QueryRow(ctx, `
			SELECT COUNT(*) FROM token_wallet_interaction_map
			WHERE token_mint = $1 AND source_wallet = $2 AND paired_wallet = $3`,
			event.TokenMint, event.ToWallet, event.FromWallet,
		).Scan(&count)
		require.NoError(t, err)
		assert.Equal(t, 1, count)
	}
}

func TestProcessTransactionEvents(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	// Ensure test partitions exist
	ensureTestPartitionsExistV2(t, repo, ctx)

	// Test data
	events := []*pb.TransactionEvent{
		{
			Slot:      100,
			TxIndex:   0,
			Signature: []byte("signature_1"),
		},
		{
			Slot:      200,
			TxIndex:   1,
			Signature: []byte("signature_2"),
		},
	}

	// Test inserting events
	err = repo.ProcessTransactionEvents(ctx, events)
	require.NoError(t, err)

	// Verify the events were inserted correctly
	for _, event := range events {
		var transaction models.TransactionEvent
		err := repo.pool.QueryRow(ctx, `
			SELECT slot, tx_index, signature
			FROM transaction_signatures
			WHERE slot = $1 AND tx_index = $2`,
			event.Slot, event.TxIndex,
		).Scan(
			&transaction.Slot,
			&transaction.TxIndex,
			&transaction.Signature,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Slot, transaction.Slot)
		assert.Equal(t, event.TxIndex, transaction.TxIndex)
		assert.Equal(t, event.Signature, transaction.Signature)
	}

	// Test duplicate handling
	err = repo.ProcessTransactionEvents(ctx, events)
	require.NoError(t, err) // Should not error on duplicates

	// Verify no duplicates were inserted
	var count int
	err = repo.pool.QueryRow(ctx, `
		SELECT COUNT(*)
		FROM transaction_signatures
		WHERE slot = $1 AND tx_index = $2`,
		events[0].Slot, events[0].TxIndex,
	).Scan(&count)
	require.NoError(t, err)
	assert.Equal(t, 1, count)
}

func TestProcessSolBalanceEvents(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	// Ensure test partitions exist
	ensureTestPartitionsExistV2(t, repo, ctx)

	// Test data
	events := []*pb.SolBalanceEvent{
		{
			Slot:    100,
			Account: []byte("account_1"),
			Amount:  1000000,
		},
		{
			Slot:    200,
			Account: []byte("account_2"),
			Amount:  2000000,
		},
	}

	// Test inserting events
	err = repo.ProcessSolBalanceEvents(ctx, events)
	require.NoError(t, err)

	// Verify the events were inserted correctly
	for _, event := range events {
		var balance models.SolBalanceEvent
		err := repo.pool.QueryRow(ctx, `
			SELECT slot, account, amount
			FROM sol_balances
			WHERE slot = $1 AND account = $2`,
			event.Slot, event.Account,
		).Scan(
			&balance.Slot,
			&balance.Account,
			&balance.Amount,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Slot, balance.Slot)
		assert.Equal(t, event.Account, balance.Account)
		assert.Equal(t, event.Amount, balance.Amount)
	}

	// Test update functionality (same slot and account, different amount)
	updatedEvents := []*pb.SolBalanceEvent{
		{
			Slot:    100, // Same slot as first event
			Account: []byte("account_1"),
			Amount:  1500000, // Different amount
		},
	}

	err = repo.ProcessSolBalanceEvents(ctx, updatedEvents)
	require.NoError(t, err)

	// Verify the balance was updated
	var updatedBalance models.SolBalanceEvent
	err = repo.pool.QueryRow(ctx, `
		SELECT slot, account, amount
		FROM sol_balances
		WHERE slot = $1 AND account = $2`,
		updatedEvents[0].Slot, updatedEvents[0].Account,
	).Scan(
		&updatedBalance.Slot,
		&updatedBalance.Account,
		&updatedBalance.Amount,
	)
	require.NoError(t, err)

	assert.Equal(t, updatedEvents[0].Slot, updatedBalance.Slot)
	assert.Equal(t, updatedEvents[0].Account, updatedBalance.Account)
	assert.Equal(t, updatedEvents[0].Amount, updatedBalance.Amount)

	// Verify total count is still 2 (no duplicates)
	var count int
	err = repo.pool.QueryRow(ctx, `
		SELECT COUNT(*)
		FROM sol_balances`).Scan(&count)
	require.NoError(t, err)
	assert.Equal(t, 2, count)
}

func TestProcessTokenBalanceEvents(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	// Ensure test partitions exist
	ensureTestPartitionsExistV2(t, repo, ctx)

	// Test data
	events := []*pb.TokenBalanceEvent{
		{
			Slot:   100,
			Owner:  []byte("owner_1"),
			Token:  []byte("token_1"),
			Amount: 1000000,
		},
		{
			Slot:   200,
			Owner:  []byte("owner_2"),
			Token:  []byte("token_2"),
			Amount: 2000000,
		},
	}

	// Test inserting events
	err = repo.ProcessTokenBalanceEvents(ctx, events)
	require.NoError(t, err)

	// Verify the events were inserted correctly
	for _, event := range events {
		var balance models.TokenBalanceEvent
		err := repo.pool.QueryRow(ctx, `
			SELECT slot, owner, token, amount
			FROM token_balances
			WHERE slot = $1 AND owner = $2 AND token = $3`,
			event.Slot, event.Owner, event.Token,
		).Scan(
			&balance.Slot,
			&balance.Owner,
			&balance.Token,
			&balance.Amount,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Slot, balance.Slot)
		assert.Equal(t, event.Owner, balance.Owner)
		assert.Equal(t, event.Token, balance.Token)
		assert.Equal(t, event.Amount, balance.Amount)
	}

	// Test update functionality (same slot, owner, and token, different amount)
	updatedEvents := []*pb.TokenBalanceEvent{
		{
			Slot:   100, // Same slot as first event
			Owner:  []byte("owner_1"),
			Token:  []byte("token_1"),
			Amount: 1500000, // Different amount
		},
	}

	err = repo.ProcessTokenBalanceEvents(ctx, updatedEvents)
	require.NoError(t, err)

	// Verify the balance was updated
	var updatedBalance models.TokenBalanceEvent
	err = repo.pool.QueryRow(ctx, `
		SELECT slot, owner, token, amount
		FROM token_balances
		WHERE slot = $1 AND owner = $2 AND token = $3`,
		updatedEvents[0].Slot, updatedEvents[0].Owner, updatedEvents[0].Token,
	).Scan(
		&updatedBalance.Slot,
		&updatedBalance.Owner,
		&updatedBalance.Token,
		&updatedBalance.Amount,
	)
	require.NoError(t, err)

	assert.Equal(t, updatedEvents[0].Slot, updatedBalance.Slot)
	assert.Equal(t, updatedEvents[0].Owner, updatedBalance.Owner)
	assert.Equal(t, updatedEvents[0].Token, updatedBalance.Token)
	assert.Equal(t, updatedEvents[0].Amount, updatedBalance.Amount)

	// Verify total count is still 2 (no duplicates)
	var count int
	err = repo.pool.QueryRow(ctx, `
		SELECT COUNT(*)
		FROM token_balances`).Scan(&count)
	require.NoError(t, err)
	assert.Equal(t, 2, count)
}

func TestCrossPartitionV2Events(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	// Ensure test partitions exist
	ensureTestPartitionsExistV2(t, repo, ctx)

	// Create events that span across two partitions
	solEvents := []*pb.SolTransferEventV2{
		{
			Slot:       86615999, // Last slot of p400
			TxIndex:    0,
			IxIndex:    0,
			FromWallet: []byte("from_wallet_p400"),
			ToWallet:   []byte("to_wallet_p400"),
			Amount:     1000000,
		},
		{
			Slot:       86616000, // First slot of p401
			TxIndex:    0,
			IxIndex:    0,
			FromWallet: []byte("from_wallet_p401"),
			ToWallet:   []byte("to_wallet_p401"),
			Amount:     2000000,
		},
	}

	tokenEvents := []*pb.TokenTransferEventV2{
		{
			Slot:       86615999, // Last slot of p400
			TxIndex:    0,
			IxIndex:    0,
			TokenMint:  []byte("token_mint_p400"),
			FromWallet: []byte("from_wallet_p400"),
			ToWallet:   []byte("to_wallet_p400"),
			Amount:     1000000,
		},
		{
			Slot:       86616000, // First slot of p401
			TxIndex:    0,
			IxIndex:    0,
			TokenMint:  []byte("token_mint_p401"),
			FromWallet: []byte("from_wallet_p401"),
			ToWallet:   []byte("to_wallet_p401"),
			Amount:     2000000,
		},
	}

	// Process events (should automatically create required partitions)
	err = repo.ProcessSolTransferEventsV2(ctx, solEvents)
	require.NoError(t, err)

	err = repo.ProcessTokenTransferEventsV2(ctx, tokenEvents)
	require.NoError(t, err)

	// Verify all events were inserted correctly
	for _, event := range solEvents {
		var transfer models.SolTransferEventV2
		err := repo.pool.QueryRow(ctx, `
			SELECT slot, tx_index, ix_index, from_wallet, to_wallet, amount
			FROM sol_transfer_events
			WHERE slot = $1 AND tx_index = $2 AND ix_index = $3`,
			event.Slot, event.TxIndex, event.IxIndex,
		).Scan(
			&transfer.Slot,
			&transfer.TxIndex,
			&transfer.IxIndex,
			&transfer.FromWallet,
			&transfer.ToWallet,
			&transfer.Amount,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Slot, transfer.Slot)
		assert.Equal(t, event.Amount, transfer.Amount)
	}

	for _, event := range tokenEvents {
		var transfer models.TokenTransferEventV2
		err := repo.pool.QueryRow(ctx, `
			SELECT slot, tx_index, ix_index, token_mint, from_wallet, to_wallet, amount
			FROM token_transfer_events
			WHERE slot = $1 AND tx_index = $2 AND ix_index = $3`,
			event.Slot, event.TxIndex, event.IxIndex,
		).Scan(
			&transfer.Slot,
			&transfer.TxIndex,
			&transfer.IxIndex,
			&transfer.TokenMint,
			&transfer.FromWallet,
			&transfer.ToWallet,
			&transfer.Amount,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Slot, transfer.Slot)
		assert.Equal(t, event.Amount, transfer.Amount)
	}

	// Verify that both partitions exist for each table
	tables := []string{"sol_transfer_events", "token_transfer_events"}
	for _, table := range tables {
		for _, partitionSuffix := range []string{"p400", "p401"} {
			partitionName := fmt.Sprintf("%s_%s", table, partitionSuffix)
			var exists int
			checkQuery := fmt.Sprintf("SELECT 1 FROM information_schema.tables WHERE table_name = '%s' LIMIT 1", partitionName)
			err = repo.pool.QueryRow(ctx, checkQuery).Scan(&exists)
			require.NoError(t, err, "Partition %s should exist", partitionName)
			assert.Equal(t, 1, exists)
		}
	}
}

func TestParallelProcessingV2Events(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	// Ensure test partitions exist
	ensureTestPartitionsExistV2(t, repo, ctx)

	// Create test events that span multiple partitions
	numPartitions := 3
	eventsPerPartition := maxBatchSize + 500 // Ensure multiple batches per partition
	totalEvents := numPartitions * eventsPerPartition

	solEvents := make([]*pb.SolTransferEventV2, totalEvents)
	tokenEvents := make([]*pb.TokenTransferEventV2, totalEvents)

	for partitionIndex := 0; partitionIndex < numPartitions; partitionIndex++ {
		baseSlot := uint64(86400000 + (partitionIndex * 216000))

		for itemIndex := 0; itemIndex < eventsPerPartition; itemIndex++ {
			eventIndex := partitionIndex*eventsPerPartition + itemIndex
			slot := baseSlot + uint64(itemIndex)

			solEvents[eventIndex] = &pb.SolTransferEventV2{
				Slot:       slot,
				TxIndex:    uint32(itemIndex % 10),
				IxIndex:    uint32(itemIndex % 5),
				FromWallet: []byte(fmt.Sprintf("from_wallet_%d", eventIndex)),
				ToWallet:   []byte(fmt.Sprintf("to_wallet_%d", eventIndex)),
				Amount:     uint64(1000000 + eventIndex),
			}

			tokenEvents[eventIndex] = &pb.TokenTransferEventV2{
				Slot:       slot,
				TxIndex:    uint32(itemIndex % 10),
				IxIndex:    uint32(itemIndex % 5),
				TokenMint:  []byte(fmt.Sprintf("token_mint_%d", eventIndex)),
				FromWallet: []byte(fmt.Sprintf("from_wallet_%d", eventIndex)),
				ToWallet:   []byte(fmt.Sprintf("to_wallet_%d", eventIndex)),
				Amount:     uint64(1000000 + eventIndex),
			}
		}
	}

	// Measure execution time to verify parallel processing
	startTime := time.Now()
	err = repo.ProcessSolTransferEventsV2(ctx, solEvents)
	executionTime := time.Since(startTime)
	require.NoError(t, err)
	t.Logf("Parallel SOL transfer processing completed in %v for %d events", executionTime, totalEvents)

	startTime = time.Now()
	err = repo.ProcessTokenTransferEventsV2(ctx, tokenEvents)
	executionTime = time.Since(startTime)
	require.NoError(t, err)
	t.Logf("Parallel token transfer processing completed in %v for %d events", executionTime, totalEvents)

	// Verify all events were inserted
	var solCount, tokenCount int
	err = repo.pool.QueryRow(ctx, "SELECT COUNT(*) FROM sol_transfer_events").Scan(&solCount)
	require.NoError(t, err)
	assert.GreaterOrEqual(t, solCount, totalEvents)

	err = repo.pool.QueryRow(ctx, "SELECT COUNT(*) FROM token_transfer_events").Scan(&tokenCount)
	require.NoError(t, err)
	assert.GreaterOrEqual(t, tokenCount, totalEvents)
}

// Benchmark tests for V2 events
func BenchmarkSolTransferEventsV2(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	if err != nil {
		b.Skip("Database not available for benchmark")
	}
	defer repo.Close()

	// Create test events
	events := make([]*pb.SolTransferEventV2, 1000)
	for i := 0; i < 1000; i++ {
		events[i] = &pb.SolTransferEventV2{
			Slot:       uint64(86400000 + i),
			TxIndex:    uint32(i % 10),
			IxIndex:    uint32(i % 5),
			FromWallet: []byte(fmt.Sprintf("from_wallet_%d", i)),
			ToWallet:   []byte(fmt.Sprintf("to_wallet_%d", i)),
			Amount:     uint64(1000000 + i),
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := repo.ProcessSolTransferEventsV2(ctx, events)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkTokenTransferEventsV2(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	if err != nil {
		b.Skip("Database not available for benchmark")
	}
	defer repo.Close()

	// Create test events
	events := make([]*pb.TokenTransferEventV2, 1000)
	for i := 0; i < 1000; i++ {
		events[i] = &pb.TokenTransferEventV2{
			Slot:       uint64(86400000 + i),
			TxIndex:    uint32(i % 10),
			IxIndex:    uint32(i % 5),
			TokenMint:  []byte(fmt.Sprintf("token_mint_%d", i)),
			FromWallet: []byte(fmt.Sprintf("from_wallet_%d", i)),
			ToWallet:   []byte(fmt.Sprintf("to_wallet_%d", i)),
			Amount:     uint64(1000000 + i),
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := repo.ProcessTokenTransferEventsV2(ctx, events)
		if err != nil {
			b.Fatal(err)
		}
	}
}
