package db

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"github.com/mr-tron/base58"
)

var (
	maxBatchSize = 3000
	maxRetries   = 3
	instance     *Repo
	once         sync.Once
	initErr      error

	USDC_MINT_BYTES []byte
	WSOL_MINT_BYTES []byte
)

func init() {
	var err error
	USDC_MINT_BYTES, err = base58.Decode("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")
	if err != nil {
		panic("Failed to decode USDC mint base58: " + err.Error())
	}
	WSOL_MINT_BYTES, err = base58.Decode("So11111111111111111111111111111111111111112")
	if err != nil {
		panic("Failed to decode WSOL mint base58: " + err.Error())
	}
}

// config holds the database connection configuration
type config struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	SSLMode  string
	MaxConns int
}

// defaultConfig creates a new DBConfig with default values
func defaultConfig() *config {
	return &config{
		Host:     "localhost",
		Port:     5432,
		User:     "solana_data",
		Password: "",
		Database: "solana_data",
		SSLMode:  "disable",
		MaxConns: 100,
	}
}

// ConnectionString returns the PostgreSQL connection string
func (c *config) ConnectionString() string {
	return fmt.Sprintf(
		"postgres://%s:%s@%s:%d/%s?sslmode=%s",
		c.User,
		c.Password,
		c.Host,
		c.Port,
		c.Database,
		c.SSLMode,
	)
}

func initOnce() {
	logger.Info(context.Background(), "Starting database initialization...")
	// Create default configuration
	config := defaultConfig()

	// Override with environment variables
	if host := os.Getenv("DB_HOST"); host != "" {
		config.Host = host
	}
	if port := os.Getenv("DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
		}
	}
	if user := os.Getenv("DB_USER"); user != "" {
		config.User = user
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Password = password
	}
	if database := os.Getenv("DB_NAME"); database != "" {
		config.Database = database
	}
	if sslMode := os.Getenv("DB_SSL_MODE"); sslMode != "" {
		config.SSLMode = sslMode
	}
	if maxConns := os.Getenv("DB_MAX_CONNS"); maxConns != "" {
		if val, err := strconv.Atoi(maxConns); err == nil && val > 0 {
			config.MaxConns = val
		}
	}

	// Create new database instance with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	db, err := newRepo(ctx, config)
	if err != nil {
		initErr = fmt.Errorf("failed to initialize database: %w", err)
		logger.Error(context.Background(), "Database initialization failed: %v", initErr)
		return
	}

	// Test the connection
	if err := db.pool.Ping(ctx); err != nil {
		initErr = fmt.Errorf("failed to ping database: %w", err)
		logger.Error(context.Background(), "Database ping failed: %v", initErr)
		return
	}
	instance = db
}

// Initialize sets up the database connection using environment variables
func Initialize() error {
	once.Do(initOnce)
	if initErr != nil {
		logger.Error(context.Background(), "Database initialization failed: %v", initErr)
	}
	return initErr
}

// Get returns the singleton database instance
func Get() *Repo {
	if instance == nil {
		logger.Error(context.Background(), "database not initialized. Call db.Initialize() first")
	}
	return instance
}

// Close closes the database connection
func Close() error {
	if instance == nil {
		return nil
	}
	instance.Close()
	instance = nil
	return nil
}

// DecodeBase58 decodes a base58 string to bytes
func DecodeBase58(s string) ([]byte, error) {
	return base58.Decode(s)
}
