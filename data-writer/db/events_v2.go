package db

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/kryptogo/kg-solana-data/data-writer/models"
	pb "github.com/kryptogo/kg-solana-data/data-writer/proto"
	"golang.org/x/sync/errgroup"
)

// Generic function to group events by partition
func groupEventsByPartition[T any](events []T, getSlot func(T) uint64, tableName string) map[string][]T {
	partitionGroups := make(map[string][]T)

	for _, event := range events {
		partitionName, _, _ := calculateNewPartitionInfo(tableName, getSlot(event))
		partitionGroups[partitionName] = append(partitionGroups[partitionName], event)
	}

	return partitionGroups
}

// Generic function to process events in parallel by partition
func processEventsByPartition[T any](
	ctx context.Context,
	events []T,
	tableName string,
	getSlot func(T) uint64,
	insertBatch func(context.Context, []T, string) error,
) error {
	if len(events) == 0 {
		return nil
	}

	// Group events by partition
	partitionGroups := groupEventsByPartition(events, getSlot, tableName)

	// Process each partition group in parallel
	var wg sync.WaitGroup
	errorChan := make(chan error, len(partitionGroups))

	for partitionName, partitionEvents := range partitionGroups {
		wg.Add(1)
		go func(pName string, pEvents []T) {
			defer wg.Done()

			// Process batches within this partition in parallel
			numBatches := (len(pEvents) + maxBatchSize - 1) / maxBatchSize
			var batchWg sync.WaitGroup
			batchErrorChan := make(chan error, numBatches)

			for i := 0; i < len(pEvents); i += maxBatchSize {
				end := i + maxBatchSize
				if end > len(pEvents) {
					end = len(pEvents)
				}

				batch := pEvents[i:end]
				batchWg.Add(1)
				go func(b []T) {
					defer batchWg.Done()
					if err := insertBatch(ctx, b, pName); err != nil {
						batchErrorChan <- fmt.Errorf("failed to insert batch to partition %s: %v", pName, err)
					}
				}(batch)
			}

			// Wait for all batches in this partition to complete
			batchWg.Wait()
			close(batchErrorChan)

			// Check for batch errors
			for err := range batchErrorChan {
				if err != nil {
					errorChan <- err
					return
				}
			}
		}(partitionName, partitionEvents)
	}

	// Wait for all partitions to complete
	wg.Wait()
	close(errorChan)

	// Check for partition errors
	for err := range errorChan {
		if err != nil {
			return err
		}
	}

	return nil
}

// ProcessSolTransferEventsV2 processes the new V2 SOL transfer events
func (r *Repo) ProcessSolTransferEventsV2(ctx context.Context, events []*pb.SolTransferEventV2) error {
	// Convert events to models
	transfers := make([]models.SolTransferEventV2, 0, len(events))
	walletMapRows := make([]models.SolWalletInteractionMap, 0, len(events)*2)
	for _, event := range events {
		transfers = append(transfers, models.SolTransferEventV2{
			Slot:       event.Slot,
			TxIndex:    event.TxIndex,
			IxIndex:    uint16(event.IxIndex),
			FromWallet: event.FromWallet,
			ToWallet:   event.ToWallet,
			Amount:     event.Amount,
		})
		// Write to wallet interaction map if > 1 SOL and from != to
		if event.Amount >= 1_000_000_000 && string(event.FromWallet) != string(event.ToWallet) {
			walletMapRows = append(walletMapRows,
				models.SolWalletInteractionMap{SourceWallet: event.FromWallet, PairedWallet: event.ToWallet},
				models.SolWalletInteractionMap{SourceWallet: event.ToWallet, PairedWallet: event.FromWallet},
			)
		}
	}

	var eg errgroup.Group
	eg.Go(func() error {
		return processEventsByPartition(
			ctx,
			transfers,
			"sol_transfer_events",
			func(t models.SolTransferEventV2) uint64 { return t.Slot },
			r.insertSolTransferEventV2Batch,
		)
	})
	eg.Go(func() error {
		return r.InsertSolWalletInteractionMapBatch(ctx, walletMapRows)
	})
	return eg.Wait()
}

// ProcessTokenTransferEventsV2 processes the new V2 token transfer events
func (r *Repo) ProcessTokenTransferEventsV2(ctx context.Context, events []*pb.TokenTransferEventV2) error {
	// Convert events to models
	transfers := make([]models.TokenTransferEventV2, 0, len(events))
	walletMapRows := make([]models.TokenWalletInteractionMap, 0, len(events)*2)
	for _, event := range events {
		transfers = append(transfers, models.TokenTransferEventV2{
			Slot:       event.Slot,
			TxIndex:    event.TxIndex,
			IxIndex:    uint16(event.IxIndex),
			TokenMint:  event.TokenMint,
			FromWallet: event.FromWallet,
			ToWallet:   event.ToWallet,
			Amount:     event.Amount,
		})
		if string(event.FromWallet) != string(event.ToWallet) {
			isUSDC := string(event.TokenMint) == string(USDC_MINT_BYTES)
			isWSOL := string(event.TokenMint) == string(WSOL_MINT_BYTES)
			if (isUSDC && event.Amount >= 100_000_000) || (isWSOL && event.Amount >= 1_000_000_000) || (!isUSDC && !isWSOL) {
				walletMapRows = append(walletMapRows,
					models.TokenWalletInteractionMap{TokenMint: event.TokenMint, SourceWallet: event.FromWallet, PairedWallet: event.ToWallet},
					models.TokenWalletInteractionMap{TokenMint: event.TokenMint, SourceWallet: event.ToWallet, PairedWallet: event.FromWallet},
				)
			}
		}
	}

	var eg errgroup.Group
	eg.Go(func() error {
		return processEventsByPartition(
			ctx,
			transfers,
			"token_transfer_events",
			func(t models.TokenTransferEventV2) uint64 { return t.Slot },
			r.insertTokenTransferEventV2Batch,
		)
	})
	eg.Go(func() error {
		return r.InsertTokenWalletInteractionMapBatch(ctx, walletMapRows)
	})
	return eg.Wait()
}

// ProcessTransactionEvents processes transaction signature events
func (r *Repo) ProcessTransactionEvents(ctx context.Context, events []*pb.TransactionEvent) error {
	// Convert events to models
	transactions := make([]models.TransactionEvent, 0, len(events))
	for _, event := range events {
		transactions = append(transactions, models.TransactionEvent{
			Slot:      event.Slot,
			TxIndex:   event.TxIndex,
			Signature: event.Signature,
		})
	}

	return processEventsByPartition(
		ctx,
		transactions,
		"transaction_signatures",
		func(t models.TransactionEvent) uint64 { return t.Slot },
		r.insertTransactionEventBatch,
	)
}

// ProcessSolBalanceEvents processes SOL balance events
func (r *Repo) ProcessSolBalanceEvents(ctx context.Context, events []*pb.SolBalanceEvent) error {
	// Convert events to models
	balances := make([]models.SolBalanceEvent, 0, len(events))
	for _, event := range events {
		balances = append(balances, models.SolBalanceEvent{
			Slot:    event.Slot,
			Account: event.Account,
			Amount:  event.Amount,
		})
	}

	return processEventsByPartition(
		ctx,
		balances,
		"sol_balances",
		func(b models.SolBalanceEvent) uint64 { return b.Slot },
		r.insertSolBalanceEventBatch,
	)
}

// ProcessTokenBalanceEvents processes token balance events
func (r *Repo) ProcessTokenBalanceEvents(ctx context.Context, events []*pb.TokenBalanceEvent) error {
	// Convert events to models
	balances := make([]models.TokenBalanceEvent, 0, len(events))
	for _, event := range events {
		balances = append(balances, models.TokenBalanceEvent{
			Slot:   event.Slot,
			Owner:  event.Owner,
			Token:  event.Token,
			Amount: event.Amount,
		})
	}

	return processEventsByPartition(
		ctx,
		balances,
		"token_balances",
		func(b models.TokenBalanceEvent) uint64 { return b.Slot },
		r.insertTokenBalanceEventBatch,
	)
}

// Batch insertion functions for V2 events
func (r *Repo) insertSolTransferEventV2Batch(ctx context.Context, transfers []models.SolTransferEventV2, partitionName string) error {
	var err error
	for retry := 0; retry < maxRetries; retry++ {
		err = r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
			// Create temporary table
			tempTableName := fmt.Sprintf("temp_batch_%s", partitionName)
			if _, err := tx.Exec(ctx, fmt.Sprintf("CREATE TEMPORARY TABLE %s (LIKE %s) ON COMMIT DROP", tempTableName, partitionName)); err != nil {
				return fmt.Errorf("failed to create temporary table: %v", err)
			}

			// Copy data to temporary table
			rows := make([][]interface{}, len(transfers))
			for i, t := range transfers {
				rows[i] = []interface{}{
					t.Slot,
					t.TxIndex,
					t.IxIndex,
					t.FromWallet,
					t.ToWallet,
					t.Amount,
				}
			}

			_, err = tx.CopyFrom(ctx,
				pgx.Identifier{tempTableName},
				[]string{"slot", "tx_index", "ix_index", "from_wallet", "to_wallet", "amount"},
				pgx.CopyFromRows(rows))
			if err != nil {
				return fmt.Errorf("failed to copy to temporary table: %v", err)
			}

			// Insert directly to specific partition with conflict handling
			query := fmt.Sprintf(`
				INSERT INTO %s 
				SELECT * FROM %s 
				ON CONFLICT (slot, tx_index, ix_index) DO NOTHING`, partitionName, tempTableName)
			if _, err := tx.Exec(ctx, query); err != nil {
				return fmt.Errorf("failed to insert from temporary table: %v", err)
			}

			return nil
		})
		if err == nil {
			return nil
		}
		time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
	}
	return err
}

func (r *Repo) insertTokenTransferEventV2Batch(ctx context.Context, transfers []models.TokenTransferEventV2, partitionName string) error {
	var err error
	for retry := 0; retry < maxRetries; retry++ {
		err = r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
			// Create temporary table
			tempTableName := fmt.Sprintf("temp_batch_%s", partitionName)
			if _, err := tx.Exec(ctx, fmt.Sprintf("CREATE TEMPORARY TABLE %s (LIKE %s) ON COMMIT DROP", tempTableName, partitionName)); err != nil {
				return fmt.Errorf("failed to create temporary table: %v", err)
			}

			// Copy data to temporary table
			rows := make([][]interface{}, len(transfers))
			for i, t := range transfers {
				rows[i] = []interface{}{
					t.Slot,
					t.TxIndex,
					t.IxIndex,
					t.TokenMint,
					t.FromWallet,
					t.ToWallet,
					t.Amount,
				}
			}

			_, err = tx.CopyFrom(ctx,
				pgx.Identifier{tempTableName},
				[]string{"slot", "tx_index", "ix_index", "token_mint", "from_wallet", "to_wallet", "amount"},
				pgx.CopyFromRows(rows))
			if err != nil {
				return fmt.Errorf("failed to copy to temporary table: %v", err)
			}

			// Insert directly to specific partition with conflict handling
			query := fmt.Sprintf(`
				INSERT INTO %s 
				SELECT * FROM %s 
				ON CONFLICT (slot, tx_index, ix_index) DO NOTHING`, partitionName, tempTableName)
			if _, err := tx.Exec(ctx, query); err != nil {
				return fmt.Errorf("failed to insert from temporary table: %v", err)
			}

			return nil
		})
		if err == nil {
			return nil
		}
		time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
	}
	return err
}

func (r *Repo) insertTransactionEventBatch(ctx context.Context, transactions []models.TransactionEvent, partitionName string) error {
	var err error
	for retry := 0; retry < maxRetries; retry++ {
		err = r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
			// Create temporary table
			tempTableName := fmt.Sprintf("temp_batch_%s", partitionName)
			if _, err := tx.Exec(ctx, fmt.Sprintf("CREATE TEMPORARY TABLE %s (LIKE %s) ON COMMIT DROP", tempTableName, partitionName)); err != nil {
				return fmt.Errorf("failed to create temporary table: %v", err)
			}

			// Copy data to temporary table
			rows := make([][]interface{}, len(transactions))
			for i, t := range transactions {
				rows[i] = []interface{}{
					t.Slot,
					t.TxIndex,
					t.Signature,
				}
			}

			_, err = tx.CopyFrom(ctx,
				pgx.Identifier{tempTableName},
				[]string{"slot", "tx_index", "signature"},
				pgx.CopyFromRows(rows))
			if err != nil {
				return fmt.Errorf("failed to copy to temporary table: %v", err)
			}

			// Insert directly to specific partition with conflict handling
			query := fmt.Sprintf(`
				INSERT INTO %s 
				SELECT * FROM %s 
				ON CONFLICT (slot, tx_index) DO NOTHING`, partitionName, tempTableName)
			if _, err := tx.Exec(ctx, query); err != nil {
				return fmt.Errorf("failed to insert from temporary table: %v", err)
			}

			return nil
		})
		if err == nil {
			return nil
		}
		time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
	}
	return err
}

func (r *Repo) insertSolBalanceEventBatch(ctx context.Context, balances []models.SolBalanceEvent, partitionName string) error {
	// Deduplicate balances before processing
	dedupedBalances := make([]models.SolBalanceEvent, 0, len(balances))
	seen := make(map[string]bool)

	for _, balance := range balances {
		key := fmt.Sprintf("%d_%s", balance.Slot, balance.Account)
		if !seen[key] {
			seen[key] = true
			dedupedBalances = append(dedupedBalances, balance)
		}
	}

	var err error
	for retry := 0; retry < maxRetries; retry++ {
		err = r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
			// Create temporary table
			tempTableName := fmt.Sprintf("temp_batch_%s", partitionName)
			if _, err := tx.Exec(ctx, fmt.Sprintf("CREATE TEMPORARY TABLE %s (LIKE %s) ON COMMIT DROP", tempTableName, partitionName)); err != nil {
				return fmt.Errorf("failed to create temporary table: %v", err)
			}

			// Copy data to temporary table
			rows := make([][]interface{}, len(dedupedBalances))
			for i, b := range dedupedBalances {
				rows[i] = []interface{}{
					b.Slot,
					b.Account,
					b.Amount,
				}
			}

			_, err = tx.CopyFrom(ctx,
				pgx.Identifier{tempTableName},
				[]string{"slot", "account", "amount"},
				pgx.CopyFromRows(rows))
			if err != nil {
				return fmt.Errorf("failed to copy to temporary table: %v", err)
			}

			// Insert directly to specific partition with conflict handling
			query := fmt.Sprintf(`
				INSERT INTO %s 
				SELECT * FROM %s 
				ON CONFLICT (slot, account) DO UPDATE 
				SET amount = EXCLUDED.amount`, partitionName, tempTableName)
			if _, err := tx.Exec(ctx, query); err != nil {
				return fmt.Errorf("failed to insert from temporary table: %v", err)
			}

			return nil
		})
		if err == nil {
			return nil
		}
		time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
	}
	return err
}

func (r *Repo) insertTokenBalanceEventBatch(ctx context.Context, balances []models.TokenBalanceEvent, partitionName string) error {
	// Deduplicate balances before processing
	dedupedBalances := make([]models.TokenBalanceEvent, 0, len(balances))
	seen := make(map[string]bool)

	for _, balance := range balances {
		key := fmt.Sprintf("%d_%s_%s", balance.Slot, balance.Owner, balance.Token)
		if !seen[key] {
			seen[key] = true
			dedupedBalances = append(dedupedBalances, balance)
		}
	}

	var err error
	for retry := 0; retry < maxRetries; retry++ {
		err = r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
			// Create temporary table
			tempTableName := fmt.Sprintf("temp_batch_%s", partitionName)
			if _, err := tx.Exec(ctx, fmt.Sprintf("CREATE TEMPORARY TABLE %s (LIKE %s) ON COMMIT DROP", tempTableName, partitionName)); err != nil {
				return fmt.Errorf("failed to create temporary table: %v", err)
			}

			// Copy data to temporary table
			rows := make([][]interface{}, len(dedupedBalances))
			for i, b := range dedupedBalances {
				rows[i] = []interface{}{
					b.Slot,
					b.Owner,
					b.Token,
					b.Amount,
				}
			}

			_, err = tx.CopyFrom(ctx,
				pgx.Identifier{tempTableName},
				[]string{"slot", "owner", "token", "amount"},
				pgx.CopyFromRows(rows))
			if err != nil {
				return fmt.Errorf("failed to copy to temporary table: %v", err)
			}

			// Insert directly to specific partition with conflict handling
			query := fmt.Sprintf(`
				INSERT INTO %s 
				SELECT * FROM %s 
				ON CONFLICT (slot, owner, token) DO UPDATE 
				SET amount = EXCLUDED.amount`, partitionName, tempTableName)
			if _, err := tx.Exec(ctx, query); err != nil {
				return fmt.Errorf("failed to insert from temporary table: %v", err)
			}

			return nil
		})
		if err == nil {
			return nil
		}
		time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
	}
	return err
}
