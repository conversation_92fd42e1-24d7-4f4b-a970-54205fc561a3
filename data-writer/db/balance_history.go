package db

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

// --- Constants and Configuration ---

const (
	// Default starting slot for balance history backfill.
	DEFAULT_START_SLOT = 432000 * 700
	// Chunk size for processing slots.
	CHUNK_SIZE = 500
)

// PeriodConfigs defines the entire aggregation chain, from finest to coarsest.
var PeriodConfigs = []PeriodConfig{
	{Name: "1m", Duration: time.Minute, Table: "balance_history"},
	{Name: "5m", Duration: 5 * time.Minute, Table: "balance_history_5m"},
	{Name: "15m", Duration: 15 * time.Minute, Table: "balance_history_15m"},
	{Name: "1h", Duration: time.Hour, Table: "balance_history_1h"},
	{Name: "4h", Duration: 4 * time.Hour, Table: "balance_history_4h"},
	{Name: "1d", Duration: 24 * time.Hour, Table: "balance_history_1d"},
}

// --- Structs ---

// BalancePoint represents a single data point for a chart.
type BalancePoint struct {
	Timestamp time.Time
	Balance   string
}

// AffectedRange represents the precise time range of a new or modified record.
type AffectedRange struct {
	Owner     []byte
	Token     []byte
	StartTime time.Time
	EndTime   *time.Time // Use a pointer to handle infinity (NULL)
}

// GetLatestTokenBalanceSlot gets the latest slot in the token_balances table.
func (r *Repo) GetLatestTokenBalanceSlot(ctx context.Context) (uint64, error) {
	query := `SELECT COALESCE(MAX(slot), 0) FROM token_balances`
	var slot uint64
	err := r.pool.QueryRow(ctx, query).Scan(&slot)
	if err != nil {
		return 0, fmt.Errorf("failed to get latest token balance slot: %w", err)
	}
	return slot, nil
}

// --- ETL Processing Logic (Cascading Pipeline) ---
func (r *Repo) ProcessSourceChunkOnly1mWithRetry(ctx context.Context, startSlot, endSlot uint64) error {
	err := r.ProcessSourceChunkOnly1m(ctx, startSlot, endSlot)
	if err != nil {
		if strings.Contains(err.Error(), "temporary file size exceeds temp_file_limit") && endSlot-startSlot == CHUNK_SIZE {
			// try smaller chunks
			mid := (startSlot + endSlot) / 2
			if err := r.ProcessSourceChunkOnly1m(ctx, startSlot, mid); err != nil {
				return fmt.Errorf("failed to process chunk [%d, %d): %w", startSlot, mid, err)
			}
			if err := r.ProcessSourceChunkOnly1m(ctx, mid, endSlot); err != nil {
				return fmt.Errorf("failed to process chunk [%d, %d): %w", mid, endSlot, err)
			}
		}
		return err
	}
	return nil
}

func (r *Repo) ProcessSourceChunkOnly1m(ctx context.Context, startSlot, endSlot uint64) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Phase 1: Fix up any open-ended ranges from the PREVIOUS chunk. This remains separate.
	if err := r.fixupChunkBoundaryOnly1m(ctx, tx, startSlot, endSlot); err != nil {
		return fmt.Errorf("failed to fixup chunk boundary: %w", err)
	}
	logger.Info(ctx, "Chunk boundary fixup complete")

	// Phase 2: Process base history only
	query := fmt.Sprintf(`
		WITH minute_aligned_transactions AS (
			SELECT DISTINCT ON (owner, token, date_trunc('minute', to_timestamp(st.timestamp)))
				owner, token, amount,
				date_trunc('minute', to_timestamp(st.timestamp)) as minute_ts
			FROM token_balances tb JOIN slot_times st ON tb.slot = st.slot
			WHERE tb.slot >= %d AND tb.slot < %d
			ORDER BY owner, token, date_trunc('minute', to_timestamp(st.timestamp)), st.timestamp DESC
		),
		balance_periods AS (
			SELECT
				owner, token, amount AS balance, minute_ts AS start_ts,
				LEAD(minute_ts, 1) OVER (PARTITION BY owner, token ORDER BY minute_ts ASC) AS next_ts
			FROM minute_aligned_transactions
		)
		INSERT INTO balance_history (owner, token, balance, valid_range)
		SELECT owner, token, balance, tstzrange(start_ts, next_ts, '[)')
		FROM balance_periods
		ON CONFLICT (owner, token, valid_range) DO UPDATE
		SET balance = EXCLUDED.balance
	`, startSlot, endSlot)

	_, err = tx.Exec(ctx, query)
	if err != nil {
		return fmt.Errorf("failed executing base history processing for chunk [%d, %d): %w", startSlot, endSlot, err)
	}

	return tx.Commit(ctx)
}

// fixupChunkBoundaryOnly1m handles the "stitching" of a new chunk to the previous one.
// This optimized version avoids a potentially costly sort operation by using a GROUP BY
// aggregation strategy, which can better leverage the (slot, owner, token) primary key.
func (r *Repo) fixupChunkBoundaryOnly1m(ctx context.Context, tx pgx.Tx, startSlot, endSlot uint64) error {
	// The interval is hardcoded, so using Sprintf is acceptable here.
	// In a general case, care should be taken to prevent SQL injection.
	const interval = "1 minute"

	// Note: PostgreSQL's date_bin function does not support an INTERVAL as a parameterized argument,
	// necessitating the use of fmt.Sprintf here. This is safe as the interval is a constant.
	query := fmt.Sprintf(`
        WITH first_slots_in_chunk AS (
            -- Step 1: Efficiently find the first slot for each (owner, token) pair in the range.
            -- This uses a GROUP BY strategy which can leverage a "Loose Index Scan" on the
            -- (slot, owner, token) primary key. This is more efficient than fetching all rows
            -- and sorting them, which the original DISTINCT ON query would have to do.
            SELECT
                owner,
                token,
                min(slot) as first_slot
            FROM token_balances
            WHERE slot >= $1 AND slot < $2
            GROUP BY owner, token
        ),
        first_events_in_chunk AS MATERIALIZED (
            -- Step 2: Join the small result from the first CTE back to slot_times to get the
            -- timestamp and pre-calculate the time bin. The join on first_slot (the PK) is very fast.
            SELECT
                fs.owner,
                fs.token,
                date_bin('%s', to_timestamp(st.timestamp), '2001-01-01') AS new_valid_from
            FROM first_slots_in_chunk fs
            JOIN slot_times st ON fs.first_slot = st.slot
        ),
        deleted_overlapping AS (
            -- Step 4: Delete historical records that are fully superseded by a new record
            -- starting in the exact same time bin. This uses the pre-calculated time bin.
            DELETE FROM balance_history target
            USING first_events_in_chunk fe
            WHERE target.owner = fe.owner
              AND target.token = fe.token
              AND upper_inf(target.valid_range) -- Only touch "current" (infinitely valid) records
              AND lower(target.valid_range) = fe.new_valid_from
            RETURNING target.owner, target.token
        )
        -- Step 5: Update (close off) the validity of historical records that precede the new
        -- chunk's first event. This sets the end of the old time range to the beginning of the new one.
        UPDATE balance_history target
        SET valid_range = tstzrange(lower(target.valid_range), fe.new_valid_from, '[)')
        FROM first_events_in_chunk fe
        WHERE target.owner = fe.owner
          AND target.token = fe.token
          AND upper_inf(target.valid_range) -- Only touch "current" records
          AND lower(target.valid_range) < fe.new_valid_from;
    `, interval)

	if _, err := tx.Exec(ctx, query, startSlot, endSlot); err != nil {
		// It's idiomatic to check for pgx.ErrNoRows, as an UPDATE/DELETE affecting
		// zero rows is not an application error in this business logic.
		if err == pgx.ErrNoRows {
			return nil
		}
		return fmt.Errorf("failed to execute fixup query: %w", err)
	}

	return nil
}

// --- Main Orchestration Logic ---

// BackfillBalanceHistory processes balance history backfill from a given end slot.
func (r *Repo) BackfillBalanceHistory(ctx context.Context, targetEndSlot uint64) error {
	lastProcessedSlot, firstProcessedSlot, err := r.GetBalanceHistoryWatermark(ctx)
	if err != nil {
		return fmt.Errorf("failed to get watermark: %w", err)
	}

	startSlot := lastProcessedSlot + 1
	if startSlot == 1 { // No previous processing
		startSlot = DEFAULT_START_SLOT
	}

	if startSlot >= targetEndSlot {
		logger.Info(ctx, "No data to backfill")
		return nil
	}

	logger.Info(ctx, "Starting balance history backfill: slots [%d, %d]", startSlot, targetEndSlot)

	// The loop simply calls the main chunk processor.
	for currentSlot := startSlot; currentSlot < targetEndSlot; currentSlot += CHUNK_SIZE {
		endSlot := currentSlot + CHUNK_SIZE
		if endSlot > targetEndSlot {
			endSlot = targetEndSlot
		}

		logger.Info(ctx, "Backfilling chunk [%d, %d)...", currentSlot, endSlot)
		if err := r.ProcessSourceChunkOnly1mWithRetry(ctx, currentSlot, endSlot); err != nil {
			return fmt.Errorf("failed to process chunk [%d, %d): %w", currentSlot, endSlot, err)
		}

		if firstProcessedSlot == 0 {
			firstProcessedSlot = currentSlot
		}
		if err := r.SetBalanceHistoryWatermark(ctx, endSlot-1, firstProcessedSlot); err != nil {
			return fmt.Errorf("failed to update watermark: %w", err)
		}
	}

	logger.Info(ctx, "Balance history backfill complete")
	return nil
}

// ProcessBalanceHistoryDelta processes new balance data for real-time updates.
func (r *Repo) ProcessBalanceHistoryDelta(ctx context.Context) error {
	lastProcessedSlot, firstProcessedSlot, err := r.GetBalanceHistoryWatermark(ctx)
	if err != nil {
		return fmt.Errorf("failed to get watermark: %w", err)
	}

	latestSlot, err := r.GetLatestTokenBalanceSlot(ctx)
	if err != nil {
		return fmt.Errorf("failed to get latest slot: %w", err)
	}

	// Leave a small buffer for data consistency
	targetSlot := latestSlot
	if latestSlot > 10 {
		targetSlot = latestSlot - 10
	}

	if lastProcessedSlot >= targetSlot {
		logger.Info(ctx, "No new data to process")
		return nil
	}

	// Process the entire delta in one go for simplicity, but could also be chunked.
	startSlot := lastProcessedSlot + 1
	logger.Info(ctx, "Processing balance history delta: slots [%d, %d)", startSlot, targetSlot)
	if err := r.ProcessSourceChunkOnly1mWithRetry(ctx, startSlot, targetSlot); err != nil {
		return fmt.Errorf("failed to process delta chunk: %w", err)
	}

	if err := r.SetBalanceHistoryWatermark(ctx, targetSlot, firstProcessedSlot); err != nil {
		return fmt.Errorf("failed to update watermark for delta: %w", err)
	}

	logger.Info(ctx, "Balance history delta processing complete")
	return nil
}

// --- Query Logic ---

// GetBalanceHistoryPoints retrieves balance history for a given wallet, token, time range, and period.
// Uses database-side LOCF logic for optimal performance.
func (r *Repo) GetBalanceHistoryPoints(ctx context.Context, wallet, token string, fromTime, toTime time.Time, period string) ([]BalancePoint, error) {
	walletBytes, err := DecodeBase58(wallet)
	if err != nil {
		return nil, fmt.Errorf("invalid wallet address: %v", err)
	}

	tokenBytes, err := DecodeBase58(token)
	if err != nil {
		return nil, fmt.Errorf("invalid token address: %v", err)
	}

	dur, err := ParsePeriod(period)
	if err != nil {
		return nil, err
	}

	// --- OPTIMAL TABLE SELECTION LOGIC ---
	optimalTable := PeriodConfigs[0].Table // Default to base table
	for i := len(PeriodConfigs) - 1; i >= 0; i-- {
		// Select the coarsest table that is finer than or equal to the query period.
		if dur >= PeriodConfigs[i].Duration {
			optimalTable = PeriodConfigs[i].Table
			break
		}
	}

	// Use database-side LOCF logic with generate_series and GIST index
	query := fmt.Sprintf(`
        -- Step 1: Generate the series of timestamps for our chart's x-axis.
        WITH points_in_time AS (
            SELECT generate_series($3::timestamptz, $4::timestamptz, $5::interval) AS point
        )
        -- Step 2: For each generated point, efficiently find the correct balance.
        SELECT
            p.point,
            -- This subquery uses the GIST index to perform a highly optimized
            -- lookup for the state at that exact point in time.
            (SELECT bh.balance
             FROM %s bh
             WHERE bh.owner = $1
               AND bh.token = $2
               AND bh.valid_range @> p.point -- The magic is here!
             LIMIT 1) AS balance
        FROM
            points_in_time p
        ORDER BY
            p.point;`, optimalTable)

	rows, err := r.pool.Query(ctx, query, walletBytes, tokenBytes, fromTime, toTime, PostgreSQLInterval(dur))
	if err != nil {
		return nil, fmt.Errorf("failed to query balance history: %v", err)
	}
	defer rows.Close()

	var points []BalancePoint
	for rows.Next() {
		var timestamp time.Time
		var balance *string // Use pointer to handle NULL values

		if err := rows.Scan(&timestamp, &balance); err != nil {
			return nil, fmt.Errorf("failed to scan balance point: %v", err)
		}

		// Handle NULL balance (no data for this timestamp)
		balanceStr := "0"
		if balance != nil {
			balanceStr = *balance
		}

		points = append(points, BalancePoint{
			Timestamp: timestamp,
			Balance:   balanceStr,
		})
	}

	return points, nil
}
