package db

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// --- Test Suite Setup ---

// ETLTestSuite is a test suite for the database ETL logic.
type ETLTestSuite struct {
	suite.Suite
	repo *Repo
}

// SetupSuite runs once before all tests in the suite.
// It sets up a database connection and runs migrations.
func (s *ETLTestSuite) SetupSuite() {
	if testing.Short() {
		s.T().Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(s.T(), err, "Failed to connect to database")

	s.repo = repo

	// Ensure test partitions exist for balance history tables
	s.ensureTestPartitionsExist()
}

// ensureTestPartitionsExist creates the necessary partitions for balance history tests
func (s *ETLTestSuite) ensureTestPartitionsExist() {
	ctx := context.Background()

	// Test slots for balance history
	testSlots := []uint64{
		0,
		50,
		100,
		200,
		300,
		400,
		5000,
		6000,
	}

	tables := []string{
		"token_balances",
	}

	for _, table := range tables {
		for _, slot := range testSlots {
			if err := s.repo.EnsureNewPartitionExists(ctx, table, slot); err != nil {
				s.T().Fatalf("Failed to ensure %s partition exists for slot %d: %v", table, slot, err)
			}
		}
	}
}

// TearDownSuite runs once after all tests in the suite.
func (s *ETLTestSuite) TearDownSuite() {
	if s.repo != nil {
		config := defaultConfig()
		config.Host = "localhost"
		config.Port = 5432
		config.User = "postgres"
		config.Password = "postgres"
		config.Database = "solana_data_test"
		config.SSLMode = "disable"

		cleanupTestDB(s.T(), config)
		s.repo.Close()
	}
}

// BeforeTest runs before each test. It cleans the tables to ensure a fresh state.
func (s *ETLTestSuite) BeforeTest(suiteName, testName string) {
	s.clearAllTables()
}

// Run the test suite
func TestETLSuite(t *testing.T) {
	suite.Run(t, new(ETLTestSuite))
}

// --- Helper Functions ---

func (s *ETLTestSuite) clearAllTables() {
	ctx := context.Background()
	for _, period := range PeriodConfigs {
		_, err := s.repo.pool.Exec(ctx, fmt.Sprintf("TRUNCATE TABLE %s;", period.Table))
		require.NoError(s.T(), err)
	}
	_, err := s.repo.pool.Exec(ctx, "TRUNCATE TABLE token_balances, slot_times, etl_watermark;")
	require.NoError(s.T(), err)
}

func (s *ETLTestSuite) insertTokenBalance(slot uint64, ts time.Time, owner, token string, amount int) {
	ctx := context.Background()
	_, err := s.repo.pool.Exec(ctx, "INSERT INTO slot_times (slot, timestamp) VALUES ($1, $2) ON CONFLICT(slot) DO NOTHING;", slot, ts.Unix())
	require.NoError(s.T(), err)

	query := `
        INSERT INTO token_balances (slot, owner, token, amount)
        VALUES ($1, DECODE($2, 'hex'), DECODE($3, 'hex'), $4);
    `
	_, err = s.repo.pool.Exec(ctx, query, slot, owner, token, amount)
	require.NoError(s.T(), err)
}

// --- Test Cases ---

func (s *ETLTestSuite) TestProcessSourceChunk_Cascade() {
	t := s.T()
	ctx := context.Background()

	// --- Test Data Setup ---
	// A series of transactions for a single wallet/token.
	// This will test the base case, state changes, and carry-forward logic.
	owner := "01"
	token := "02"

	// 1. Initial balance
	s.insertTokenBalance(50, time.Date(2023, 12, 31, 10, 1, 0, 0, time.UTC), owner, token, 10) // 10:00 - State becomes 100
	s.insertTokenBalance(100, time.Date(2024, 1, 1, 10, 1, 0, 0, time.UTC), owner, token, 100) // 10:00 - State becomes 100

	// 2. A 5m state change
	s.insertTokenBalance(200, time.Date(2024, 1, 1, 10, 5, 0, 0, time.UTC), owner, token, 150) // 10:05 - State becomes 150

	// 3. No change for a while
	// ...

	// 4. A 1h state change
	s.insertTokenBalance(300, time.Date(2024, 1, 1, 11, 2, 0, 0, time.UTC), owner, token, 200) // 11:02 - State becomes 200

	// --- Action ---
	err := s.repo.ProcessSourceChunk(ctx, 50, 400)
	require.NoError(t, err)

	// --- Assertions ---
	// Check the 1m table (base)
	var count int
	s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history").Scan(&count)
	require.Equal(t, 4, count, "Base 1m table should have 4 distinct state periods")

	// Check the 5m table
	var balance5m int
	// At 10:00, balance was 100. At 10:05, it changed to 150.
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_5m WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-01-01 10:05:00'::timestamptz", owner).Scan(&balance5m)
	require.NoError(t, err)
	require.Equal(t, 150, balance5m)

	// Check the 1h table - this is the most important check for the cascade
	var balance1h int
	// At 10:59, the last state change was at 10:05, so balance should be 150
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_1h WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-01-01 10:59:00'::timestamptz", owner).Scan(&balance1h)
	require.NoError(t, err)
	require.Equal(t, 150, balance1h)

	// At 11:00, the state is 200 because the change happened during 11:00-12:00
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_1h WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-01-01 11:00:00'::timestamptz", owner).Scan(&balance1h)
	require.NoError(t, err)
	require.Equal(t, 200, balance1h)

	// At 11:07, the state change to 200 has happened. The state from 10:05 is carried forward.
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_1h WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-01-01 11:07:00'::timestamptz", owner).Scan(&balance1h)
	require.NoError(t, err)
	require.Equal(t, 200, balance1h)
}

func (s *ETLTestSuite) TestProcessSourceChunk_Cascade_2() {
	t := s.T()
	ctx := context.Background()

	// --- Test Data Setup ---
	// A series of transactions for a single wallet/token.
	// This will test the base case, state changes, and carry-forward logic.
	owner := "01"
	token := "02"

	// 1. Initial balance 10:49 balance 100
	s.insertTokenBalance(100, time.Date(2024, 1, 1, 10, 49, 0, 0, time.UTC), owner, token, 100)
	s.insertTokenBalance(101, time.Date(2024, 1, 1, 10, 53, 0, 0, time.UTC), owner, token, 100)

	// 2. 10:55 balance 0
	s.insertTokenBalance(200, time.Date(2024, 1, 1, 10, 54, 0, 0, time.UTC), owner, token, 0)

	// 3. 10:56 balance 0
	s.insertTokenBalance(300, time.Date(2024, 1, 1, 10, 56, 0, 0, time.UTC), owner, token, 0)

	// --- Action ---
	err := s.repo.ProcessSourceChunk(ctx, 100, 201)
	require.NoError(t, err)
	err = s.repo.ProcessSourceChunk(ctx, 201, 301)
	require.NoError(t, err)

	// --- Assertions ---
	// Check the 1m table (base)
	var count int
	s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history").Scan(&count)
	require.Equal(t, 4, count, "Base 1m table should have 4 distinct state periods")

	// Check the 5m table
	var balance5m int
	// At 10:45, balance was 100
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_5m WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-01-01 10:45:00'::timestamptz", owner).Scan(&balance5m)
	require.NoError(t, err)
	require.Equal(t, 100, balance5m)

	// At 10:50, balance changed to 0
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_5m WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-01-01 10:50:00'::timestamptz", owner).Scan(&balance5m)
	require.NoError(t, err)
	require.Equal(t, 0, balance5m)

	// At 10:55, balance is still 0
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_5m WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-01-01 10:55:00'::timestamptz", owner).Scan(&balance5m)
	require.NoError(t, err)
	require.Equal(t, 0, balance5m)

	// Assert all lower(valid_range) are different
	var ranges []time.Time
	rows, err := s.repo.pool.Query(ctx, "SELECT lower(valid_range) FROM balance_history_5m WHERE owner = DECODE($1, 'hex') ORDER BY lower(valid_range)", owner)
	require.NoError(t, err)
	defer rows.Close()

	for rows.Next() {
		var rangeStart time.Time
		err = rows.Scan(&rangeStart)
		require.NoError(t, err)
		ranges = append(ranges, rangeStart)
	}
	require.NoError(t, rows.Err())

	// Check that all ranges are unique
	rangeMap := make(map[time.Time]bool)
	for _, r := range ranges {
		require.False(t, rangeMap[r], "Duplicate range start time found: %v", r)
		rangeMap[r] = true
	}
}

func (s *ETLTestSuite) TestFixupChunkBoundary() {
	t := s.T()
	ctx := context.Background()

	owner := "03"
	token := "04"

	// --- Test Data Setup ---
	// 1. Manually insert a record with an infinity end time from a "previous" chunk
	rangeStart := time.Date(2024, 2, 1, 10, 0, 0, 0, time.UTC)
	tables := []string{"balance_history", "balance_history_5m", "balance_history_15m", "balance_history_1h"}
	for _, table := range tables {
		_, err := s.repo.pool.Exec(ctx,
			fmt.Sprintf("INSERT INTO %s (owner, token, balance, valid_range) VALUES (DECODE($1, 'hex'), DECODE($2, 'hex'), 100, tstzrange($3, NULL, '[)'))", table),
			owner, token, rangeStart)
		require.NoError(t, err)
	}

	// 2. Add a raw transaction that belongs to the "next" chunk
	s.insertTokenBalance(5000, time.Date(2024, 2, 1, 10, 30, 0, 0, time.UTC), owner, token, 200)

	// --- Action ---
	// Run the fixup logic for the new chunk range
	tx, err := s.repo.pool.Begin(ctx)
	require.NoError(t, err)
	err = s.repo.fixupChunkBoundary(ctx, tx, 5000, 6000)
	require.NoError(t, tx.Commit(ctx))
	require.NoError(t, err)

	// --- Assertions ---
	var lower, upper time.Time
	err = s.repo.pool.QueryRow(ctx, "SELECT lower(valid_range), upper(valid_range) FROM balance_history WHERE owner = DECODE($1, 'hex')", owner).Scan(&lower, &upper)
	require.NoError(t, err)

	expectedEndTime := rangeStart.Add(30 * time.Minute) // 10:30
	require.Equal(t, rangeStart.UTC(), lower.UTC(), "Start of range should be unchanged")
	require.Equal(t, expectedEndTime.UTC(), upper.UTC(), "End of range should be closed by the fixup logic")

	var count int
	err = s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history_5m WHERE owner = DECODE($1, 'hex')", owner).Scan(&count)
	require.NoError(t, err)
	require.Equal(t, 1, count, "Balance history_5m should have 2 record")

	// new data will overwrite this row so we should remove it first
	err = s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history_1h WHERE owner = DECODE($1, 'hex')", owner).Scan(&count)
	require.NoError(t, err)
	require.Equal(t, 0, count, "Balance history_1h should have 0 record")
}

func (s *ETLTestSuite) TestGetBalanceHistoryPoints_LOCF() {
	t := s.T()
	ctx := context.Background()

	owner := "HEW8TmjdtJmAeMWZYG2pZd97FwZybtvJMkFjJmqMz64E"
	token := "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"

	// --- Test Data Setup ---
	// Manually populate the 1h table for a clean test of query logic
	// Balance is 100 from 10:00 to 11:00
	_, err := s.repo.pool.Exec(ctx, "INSERT INTO balance_history_1h (owner, token, balance, valid_range) VALUES (from_base58($1), from_base58($2), 100, '[2024-03-01 10:00:00, 2024-03-01 11:00:00)')", owner, token)
	require.NoError(t, err)
	// Balance is 200 from 11:00 to 12:00
	_, err = s.repo.pool.Exec(ctx, "INSERT INTO balance_history_1h (owner, token, balance, valid_range) VALUES (from_base58($1), from_base58($2), 200, '[2024-03-01 11:00:00, 2024-03-01 12:00:00)')", owner, token)
	require.NoError(t, err)
	// Balance is 150 from 12:00 onwards
	_, err = s.repo.pool.Exec(ctx, "INSERT INTO balance_history_1h (owner, token, balance, valid_range) VALUES (from_base58($1), from_base58($2), 150, '[2024-03-01 12:00:00,)')", owner, token)
	require.NoError(t, err)

	// --- Action ---
	// Query for 30-minute intervals over a 5-hour period
	fromTime := time.Date(2024, 3, 1, 10, 0, 0, 0, time.UTC)
	toTime := time.Date(2024, 3, 1, 15, 0, 0, 0, time.UTC)
	// Note: Our GetBalanceHistoryPoints will correctly choose balance_history_1h for a 1h query.
	points, err := s.repo.GetBalanceHistoryPoints(ctx, owner, token, fromTime, toTime, "1h")
	require.NoError(t, err)

	// --- Assertions ---
	require.Len(t, points, 6)

	// 10:00 - Exact boundary
	require.Equal(t, "100", points[0].Balance)
	require.Equal(t, "2024-03-01T10:00:00Z", points[0].Timestamp.UTC().Format(time.RFC3339))

	// 11:00 - Exact boundary
	require.Equal(t, "200", points[1].Balance)
	require.Equal(t, "2024-03-01T11:00:00Z", points[1].Timestamp.UTC().Format(time.RFC3339))

	// 12:00 - Exact boundary
	require.Equal(t, "150", points[2].Balance)
	require.Equal(t, "2024-03-01T12:00:00Z", points[2].Timestamp.UTC().Format(time.RFC3339))

	// 13:00 - carry forward
	require.Equal(t, "150", points[3].Balance)
	require.Equal(t, "2024-03-01T13:00:00Z", points[3].Timestamp.UTC().Format(time.RFC3339))

	// 14:00 - carry forward, balance from 12:00 is carried forward
	require.Equal(t, "150", points[4].Balance)
	require.Equal(t, "2024-03-01T14:00:00Z", points[4].Timestamp.UTC().Format(time.RFC3339))

	// 15:00 - carry forward
	require.Equal(t, "150", points[5].Balance)
	require.Equal(t, "2024-03-01T15:00:00Z", points[5].Timestamp.UTC().Format(time.RFC3339))
}

func (s *ETLTestSuite) TestProcessSourceChunk_ComplexMultiWallet() {
	t := s.T()
	ctx := context.Background()

	// --- Test Data Setup ---
	// Wallet 1: Active trader, multiple transactions.
	owner1, token1 := "11", "AA"
	// Wallet 2: Long-term holder, few transactions, long inactivity.
	owner2, token2 := "22", "BB"

	// Day 1
	s.insertTokenBalance(1000, time.Date(2024, 4, 1, 9, 3, 0, 0, time.UTC), owner1, token1, 1000)
	s.insertTokenBalance(1001, time.Date(2024, 4, 1, 10, 14, 0, 0, time.UTC), owner1, token1, 1200) // Crosses 15m boundary
	s.insertTokenBalance(1002, time.Date(2024, 4, 1, 10, 15, 0, 0, time.UTC), owner1, token1, 1250) // Exactly on 15m boundary
	s.insertTokenBalance(1003, time.Date(2024, 4, 1, 12, 5, 0, 0, time.UTC), owner1, token1, 800)   // Crosses 1h boundary

	s.insertTokenBalance(2000, time.Date(2024, 4, 1, 8, 0, 0, 0, time.UTC), owner2, token2, 5000) // Sits unchanged for a long time

	// Day 2 - Wallet 1 goes to zero, Wallet 2 finally transacts
	s.insertTokenBalance(3000, time.Date(2024, 4, 2, 13, 0, 0, 0, time.UTC), owner1, token1, 0)    // Goes to zero, crosses 4h and 1d boundary
	s.insertTokenBalance(3001, time.Date(2024, 4, 2, 14, 0, 0, 0, time.UTC), owner2, token2, 4500) // First change in over a day

	// --- Action ---
	err := s.repo.ProcessSourceChunk(ctx, 1000, 4000)
	require.NoError(t, err)

	// --- Assertions ---
	// Check row counts to ensure sparse storage is working
	var count1m, count5m, count15m, count1h, count4h, count1d int
	s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history WHERE owner=DECODE($1,'hex')", owner1).Scan(&count1m)
	s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history_5m WHERE owner=DECODE($1,'hex')", owner1).Scan(&count5m)
	s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history_15m WHERE owner=DECODE($1,'hex')", owner1).Scan(&count15m)
	s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history_1h WHERE owner=DECODE($1,'hex')", owner1).Scan(&count1h)
	s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history_4h WHERE owner=DECODE($1,'hex')", owner1).Scan(&count4h)
	s.repo.pool.QueryRow(ctx, "SELECT count(*) FROM balance_history_1d WHERE owner=DECODE($1,'hex')", owner1).Scan(&count1d)

	require.Equal(t, 5, count1m, "Owner 1 should have 5 base records")
	require.Equal(t, 5, count5m, "Owner 1 should have 5 state changes at 5m level")
	require.Equal(t, 5, count15m, "Owner 1 should have 5 state changes at 15m level") // 10:14 and 10:15 are in the same 15m block
	require.Equal(t, 4, count1h, "Owner 1 should have 4 state changes at 1h level")
	require.Equal(t, 3, count4h, "Owner 1 should have 3 state changes at 4h level")
	require.Equal(t, 2, count1d, "Owner 1 should have 2 state changes at 1d level")

	// Check Wallet 2 (the long-term holder)
	var balance2 int
	// Check balance on Day 2 at 08:00, long after the last transaction. Should still be 5000.
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_4h WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-04-02 08:00:00'::timestamptz", owner2).Scan(&balance2)
	require.NoError(t, err)
	require.Equal(t, 5000, balance2, "Wallet 2 balance should be carried forward for over a day")

	// Check balance after the final transaction
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_1d WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-04-03 00:00:00'::timestamptz", owner2).Scan(&balance2)
	require.NoError(t, err)
	require.Equal(t, 4500, balance2, "Wallet 2 balance should be updated after its late transaction")

	// Check Wallet 1 zero-balance case
	var balance1 int
	err = s.repo.pool.QueryRow(ctx, "SELECT balance FROM balance_history_1d WHERE owner = DECODE($1, 'hex') AND valid_range @> '2024-04-03 00:00:00'::timestamptz", owner1).Scan(&balance1)
	require.NoError(t, err)
	require.Equal(t, 0, balance1, "Wallet 1 should have a zero balance on day 3")
}

func (s *ETLTestSuite) TestFixupChunkBoundary_MultiWalletComplex() {
	t := s.T()
	ctx := context.Background()

	// --- Test Data Setup ---
	// Wallet A will get an update in the new chunk.
	// Wallet B will NOT get an update, its range should remain infinite.
	// Wallet C will get an update that is very close to its last boundary.
	ownerA, tokenA := "AA", "01"
	ownerB, tokenB := "BB", "02"
	ownerC, tokenC := "CC", "03"

	// 1. Manually insert records with infinity end times for all wallets from a "previous" chunk.
	// We insert into both 1m and 1h tables to test the fixup cascade.
	rangeStartA := time.Date(2024, 5, 1, 10, 0, 0, 0, time.UTC)
	_, err := s.repo.pool.Exec(ctx, "INSERT INTO balance_history (owner, token, balance, valid_range) VALUES (DECODE($1, 'hex'), DECODE($2, 'hex'), 100, tstzrange($3, NULL, '[)'))", ownerA, tokenA, rangeStartA)
	require.NoError(t, err)
	_, err = s.repo.pool.Exec(ctx, "INSERT INTO balance_history_1h (owner, token, balance, valid_range) VALUES (DECODE($1, 'hex'), DECODE($2, 'hex'), 100, tstzrange($3, NULL, '[)'))", ownerA, tokenA, rangeStartA.Truncate(time.Hour))
	require.NoError(t, err)

	rangeStartB := time.Date(2024, 5, 1, 11, 0, 0, 0, time.UTC)
	_, err = s.repo.pool.Exec(ctx, "INSERT INTO balance_history (owner, token, balance, valid_range) VALUES (DECODE($1, 'hex'), DECODE($2, 'hex'), 500, tstzrange($3, NULL, '[)'))", ownerB, tokenB, rangeStartB)
	require.NoError(t, err)

	rangeStartC := time.Date(2024, 5, 1, 12, 0, 0, 0, time.UTC)
	_, err = s.repo.pool.Exec(ctx, "INSERT INTO balance_history (owner, token, balance, valid_range) VALUES (DECODE($1, 'hex'), DECODE($2, 'hex'), 1000, tstzrange($3, NULL, '[)'))", ownerC, tokenC, rangeStartC)
	require.NoError(t, err)

	// 2. Add raw transactions that belong to the "next" chunk FOR WALLETS A and C ONLY.
	s.insertTokenBalance(7000, time.Date(2024, 5, 1, 11, 30, 0, 0, time.UTC), ownerA, tokenA, 150)
	s.insertTokenBalance(7001, time.Date(2024, 5, 1, 12, 1, 0, 0, time.UTC), ownerC, tokenC, 1100)

	// --- Action ---
	// Run ONLY the fixup logic for the new chunk range.
	tx, err := s.repo.pool.Begin(ctx)
	require.NoError(t, err)
	err = s.repo.fixupChunkBoundary(ctx, tx, 7000, 8000)
	require.NoError(t, err)
	require.NoError(t, tx.Commit(ctx))

	// --- Assertions ---
	// Assert Wallet A was fixed correctly at all levels
	var lowerA, upperA time.Time
	err = s.repo.pool.QueryRow(ctx, "SELECT lower(valid_range), upper(valid_range) FROM balance_history WHERE owner = DECODE($1, 'hex')", ownerA).Scan(&lowerA, &upperA)
	require.NoError(t, err)
	require.Equal(t, time.Date(2024, 5, 1, 11, 30, 0, 0, time.UTC), upperA.UTC(), "Wallet A base range should be closed")

	var upperA_1h *time.Time
	err = s.repo.pool.QueryRow(ctx, "SELECT upper(valid_range) FROM balance_history_1h WHERE owner = DECODE($1, 'hex')", ownerA).Scan(&upperA_1h)
	require.NoError(t, err)
	require.NotNil(t, upperA_1h, "Wallet A 1h range should have an upper bound")
	require.Equal(t, time.Date(2024, 5, 1, 11, 0, 0, 0, time.UTC), upperA_1h.UTC(), "Wallet A 1h range should be closed to the truncated hour of the next event")

	// Assert Wallet B was NOT fixed (is still infinite)
	var upperB *time.Time
	err = s.repo.pool.QueryRow(ctx, "SELECT upper(valid_range) FROM balance_history WHERE owner = DECODE($1, 'hex')", ownerB).Scan(&upperB)
	require.NoError(t, err)
	require.Nil(t, upperB, "Wallet B range should remain infinite as it had no new transactions")

	// Assert Wallet C was fixed correctly
	var upperC time.Time
	err = s.repo.pool.QueryRow(ctx, "SELECT upper(valid_range) FROM balance_history WHERE owner = DECODE($1, 'hex')", ownerC).Scan(&upperC)
	require.NoError(t, err)
	require.Equal(t, time.Date(2024, 5, 1, 12, 1, 0, 0, time.UTC), upperC.UTC(), "Wallet C base range should be closed")
}
