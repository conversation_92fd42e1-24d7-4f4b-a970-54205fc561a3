package db

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

const (
	newPartitionSlotSize = 216000 // 1 day worth of slots (new schema)
)

// calculateNewPartitionInfo calculates partition information for the new schema (1 day per partition)
func calculateNewPartitionInfo(tableName string, slot uint64) (partitionName string, startSlot, endSlot uint64) {
	// Calculate partition number (floor(slot/216000))
	partitionNum := slot / newPartitionSlotSize
	startSlot = partitionNum * newPartitionSlotSize
	endSlot = startSlot + newPartitionSlotSize

	// Use partition number as suffix to match new schema naming (e.g., p1600, p1601)
	partitionName = fmt.Sprintf("%s_p%d", tableName, partitionNum)

	return partitionName, startSlot, endSlot
}

// EnsureNewPartitionExists ensures that the partition required for the new schema exists.
func (r *Repo) EnsureNewPartitionExists(ctx context.Context, tableName string, slot uint64) error {
	partitionName, startSlot, endSlot := calculateNewPartitionInfo(tableName, slot)

	// Use "CREATE TABLE IF NOT EXISTS" to atomically create the partition
	query := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s PARTITION OF %s
		FOR VALUES FROM (%d) TO (%d)`,
		partitionName, tableName, startSlot, endSlot)

	_, err := r.pool.Exec(ctx, query)
	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok && pgErr.Code == "42P07" {
			// Partition was created by another process concurrently. This is not an error.
			return nil
		}
		return fmt.Errorf("failed to ensure new partition %s exists: %v", partitionName, err)
	}

	logger.Info(ctx, "Successfully ensured new partition %s exists for slot range [%d, %d)", partitionName, startSlot, endSlot)

	return nil
}

// CreateNextNewPartition creates the next partition for the new schema
func (r *Repo) CreateNextNewPartition(ctx context.Context, tableName string, currentSlot uint64) error {
	// Calculate the end point of the partition containing currentSlot
	_, _, currentEndSlot := calculateNewPartitionInfo(tableName, currentSlot)

	// The next slot will fall into the next partition
	nextSlot := currentEndSlot + 1

	// Ensure the next partition exists
	return r.EnsureNewPartitionExists(ctx, tableName, nextSlot)
}

type Repo struct {
	pool *pgxpool.Pool
}

// PartitionInfo represents a partition's slot range
type PartitionInfo struct {
	TableName     string
	PartitionName string
	StartSlot     int64
	EndSlot       int64
}

func newRepo(ctx context.Context, config *config) (*Repo, error) {
	poolConfig, err := pgxpool.ParseConfig(config.ConnectionString())
	if err != nil {
		return nil, fmt.Errorf("failed to parse connection string: %v", err)
	}

	// Configure connection pool
	poolConfig.MaxConns = int32(config.MaxConns)
	poolConfig.MinConns = 2
	poolConfig.MaxConnLifetime = time.Hour
	poolConfig.MaxConnIdleTime = 30 * time.Minute

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %v", err)
	}

	return &Repo{pool: pool}, nil
}

func (r *Repo) Close() {
	r.pool.Close()
}

func (r *Repo) WithTransaction(ctx context.Context, fn func(ctx context.Context, tx pgx.Tx) error) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	defer func() {
		if err != nil {
			tx.Rollback(ctx)
		}
	}()

	if err := fn(ctx, tx); err != nil {
		return err
	}

	return tx.Commit(ctx)
}

// GetCurrentPartitions returns all existing partitions for a given table
func (r *Repo) GetCurrentPartitions(ctx context.Context, tableName string) ([]PartitionInfo, error) {
	query := `
		SELECT 
			c.relname as table_name,
			p.relname as partition_name,
			pg_get_expr(p.relpartbound, p.oid) as bounds
		FROM pg_class c
		JOIN pg_inherits i ON i.inhparent = c.oid
		JOIN pg_class p ON p.oid = i.inhrelid
		WHERE c.relname = $1
		ORDER BY p.relname ASC`

	rows, err := r.pool.Query(ctx, query, tableName)
	if err != nil {
		return nil, fmt.Errorf("failed to query partitions: %v", err)
	}
	defer rows.Close()

	var partitions []PartitionInfo
	for rows.Next() {
		var p PartitionInfo
		var bounds string
		if err := rows.Scan(&p.TableName, &p.PartitionName, &bounds); err != nil {
			return nil, fmt.Errorf("failed to scan partition info: %v", err)
		}

		// Parse the bounds string to extract start and end slots
		var startSlot, endSlot int64
		_, err := fmt.Sscanf(bounds, "FOR VALUES FROM ('%d') TO ('%d')", &startSlot, &endSlot)
		if err != nil {
			logger.Error(ctx, "failed to parse partition bounds: %s", bounds)
			return nil, fmt.Errorf("failed to parse partition bounds: %v", err)
		}

		p.StartSlot = startSlot
		p.EndSlot = endSlot
		partitions = append(partitions, p)
	}

	return partitions, nil
}
