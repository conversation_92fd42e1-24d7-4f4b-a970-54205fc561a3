package db

import (
	"fmt"
	"strings"
	"time"
)

// PostgreSQLInterval converts a Go duration to PostgreSQL interval syntax
func PostgreSQLInterval(d time.Duration) string {
	switch d {
	case time.Minute:
		return "1 minute"
	case 5 * time.Minute:
		return "5 minutes"
	case 15 * time.Minute:
		return "15 minutes"
	case time.Hour:
		return "1 hour"
	case 4 * time.Hour:
		return "4 hours"
	case 24 * time.Hour:
		return "1 day"
	default:
		// Fallback for unknown durations
		if d < time.Hour {
			return fmt.Sprintf("%d minutes", int(d.Minutes()))
		} else if d < 24*time.Hour {
			return fmt.Sprintf("%d hours", int(d.Hours()))
		} else {
			return fmt.Sprintf("%d days", int(d.Hours()/24))
		}
	}
}

// PostgreSQLDateTruncUnit converts a period name to PostgreSQL date_trunc unit
func PostgreSQLDateTruncUnit(periodName string) string {
	switch periodName {
	case "1m":
		return "minute"
	case "5m":
		return "minute" // 5m boundaries are handled by the interval, date_trunc uses minute
	case "15m":
		return "minute" // 15m boundaries are handled by the interval, date_trunc uses minute
	case "1h":
		return "hour"
	case "4h":
		return "hour" // 4h boundaries are handled by the interval, date_trunc uses hour
	case "1d":
		return "day"
	default:
		return "minute" // Default fallback
	}
}

// PeriodConfig defines the properties of each aggregation level.
type PeriodConfig struct {
	Name     string
	Duration time.Duration
	Table    string
}

// ParsePeriod parses a period string.
func ParsePeriod(period string) (time.Duration, error) {
	switch strings.ToLower(period) {
	case "1m":
		return time.Minute, nil
	case "5m":
		return 5 * time.Minute, nil
	case "15m":
		return 15 * time.Minute, nil
	case "30m":
		return 30 * time.Minute, nil
	case "1h":
		return time.Hour, nil
	case "4h":
		return 4 * time.Hour, nil
	case "1d":
		return 24 * time.Hour, nil
	default:
		return 0, fmt.Errorf("unsupported period: %s", period)
	}
}
