package db

// Wallet Transaction Stats Tests
//
// This file contains tests for wallet transaction statistics functionality.
// All tests are organized using testify/suite for better setup/teardown management.
//
// To run all wallet transaction stats tests:
//   go test -v ./db -run TestWalletTxStatsSuite
//
// To run a specific test:
//   go test -v ./db -run TestWalletTxStatsSuite/TestWalletTxStatsUpsert

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// WalletTxStatsTestSuite is a test suite for wallet transaction stats functionality
type WalletTxStatsTestSuite struct {
	suite.Suite
	repo *Repo
	ctx  context.Context
}

// SetupSuite runs once before all tests in the suite
func (s *WalletTxStatsTestSuite) SetupSuite() {
	if testing.Short() {
		s.T().Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	s.ctx = context.Background()
	repo, err := newRepo(s.ctx, config)
	require.NoError(s.T(), err, "Failed to connect to database")

	s.repo = repo
}

// TearDownSuite runs once after all tests in the suite
func (s *WalletTxStatsTestSuite) TearDownSuite() {
	if s.repo != nil {
		s.repo.Close()
	}
}

// BeforeTest runs before each test to ensure clean state
func (s *WalletTxStatsTestSuite) BeforeTest(suiteName, testName string) {
	cleanupWalletTxStatsTestDB(s.T(), s.repo)
}

// Run the wallet transaction stats test suite
func TestWalletTxStatsSuite(t *testing.T) {
	suite.Run(t, new(WalletTxStatsTestSuite))
}

// setupWalletTxStatsTable creates the wallet_tx_stats table for testing
func setupWalletTxStatsTable(ctx context.Context, repo *Repo) error {
	query := `
		CREATE TABLE IF NOT EXISTS wallet_tx_stats (
			wallet BYTEA NOT NULL PRIMARY KEY,
			max_tx_in_1h BIGINT NOT NULL DEFAULT 0,
			total_tx_count BIGINT NOT NULL DEFAULT 0
		);

		CREATE INDEX IF NOT EXISTS idx_wallet_tx_stats_max_tx_in_1h ON wallet_tx_stats (max_tx_in_1h DESC);
		CREATE INDEX IF NOT EXISTS idx_wallet_tx_stats_total_tx_count ON wallet_tx_stats (total_tx_count DESC);
	`
	_, err := repo.pool.Exec(ctx, query)
	return err
}

// createSolTransferEventsTable creates a basic sol_transfer_events table for testing
func createSolTransferEventsTable(ctx context.Context, repo *Repo) error {
	query := `
		CREATE TABLE IF NOT EXISTS sol_transfer_events (
			slot BIGINT NOT NULL,
			tx_index INTEGER NOT NULL,
			ix_index SMALLINT NOT NULL,
			from_wallet BYTEA NOT NULL,
			to_wallet BYTEA NOT NULL,
			amount NUMERIC(20, 0) NOT NULL,
			PRIMARY KEY (slot, tx_index, ix_index)
		) PARTITION BY RANGE (slot);
	`
	_, err := repo.pool.Exec(ctx, query)
	return err
}

// TestWalletTxStatsUpsert tests the upsert functionality for wallet transaction stats
func (s *WalletTxStatsTestSuite) TestWalletTxStatsUpsert() {
	// Setup test tables
	err := setupWalletTxStatsTable(s.ctx, s.repo)
	require.NoError(s.T(), err)

	// Create test wallet transaction stats
	wallet1 := []byte("wallet1234567890123456789012345678901")
	wallet2 := []byte("wallet2345678901234567890123456789012")

	testStats := []WalletTxStats{
		{
			Wallet:       wallet1,
			MaxTxIn1h:    50,
			TotalTxCount: 1000,
		},
		{
			Wallet:       wallet2,
			MaxTxIn1h:    25,
			TotalTxCount: 500,
		},
	}

	// First upsert
	err = s.repo.UpsertWalletTxStats(s.ctx, testStats)
	require.NoError(s.T(), err)

	// Verify data was inserted
	topByTotal, err := s.repo.GetTopWalletsByTxCount(s.ctx, 10)
	require.NoError(s.T(), err)
	require.Len(s.T(), topByTotal, 2)

	// Check the results
	assert.Equal(s.T(), wallet1, topByTotal[0].Wallet)
	assert.Equal(s.T(), int64(50), topByTotal[0].MaxTxIn1h)
	assert.Equal(s.T(), int64(1000), topByTotal[0].TotalTxCount)

	assert.Equal(s.T(), wallet2, topByTotal[1].Wallet)
	assert.Equal(s.T(), int64(25), topByTotal[1].MaxTxIn1h)
	assert.Equal(s.T(), int64(500), topByTotal[1].TotalTxCount)

	// Update existing records (should accumulate total_tx_count and take max of max_tx_in_1h)
	updateStats := []WalletTxStats{
		{
			Wallet:       wallet1,
			MaxTxIn1h:    30,  // Lower than existing, should keep 50
			TotalTxCount: 200, // Should add to existing 1000
		},
		{
			Wallet:       wallet2,
			MaxTxIn1h:    75,  // Higher than existing, should update to 75
			TotalTxCount: 300, // Should add to existing 500
		},
	}

	err = s.repo.UpsertWalletTxStats(s.ctx, updateStats)
	require.NoError(s.T(), err)

	// Verify updates
	topByTotal, err = s.repo.GetTopWalletsByTxCount(s.ctx, 10)
	require.NoError(s.T(), err)
	require.Len(s.T(), topByTotal, 2)

	// wallet1 should still have max_tx_in_1h=50 and total_tx_count=1200
	assert.Equal(s.T(), wallet1, topByTotal[0].Wallet)
	assert.Equal(s.T(), int64(50), topByTotal[0].MaxTxIn1h)
	assert.Equal(s.T(), int64(1200), topByTotal[0].TotalTxCount)

	// wallet2 should have max_tx_in_1h=75 and total_tx_count=800
	assert.Equal(s.T(), wallet2, topByTotal[1].Wallet)
	assert.Equal(s.T(), int64(75), topByTotal[1].MaxTxIn1h)
	assert.Equal(s.T(), int64(800), topByTotal[1].TotalTxCount)
}

// TestGetTopWalletsByMaxTxIn1h tests querying top wallets by max transactions in 1 hour
func (s *WalletTxStatsTestSuite) TestGetTopWalletsByMaxTxIn1h() {
	// Setup test tables
	err := setupWalletTxStatsTable(s.ctx, s.repo)
	require.NoError(s.T(), err)

	// Create test data with different max_tx_in_1h values
	testStats := []WalletTxStats{
		{
			Wallet:       []byte("low_activity_wallet123456789012345678"),
			MaxTxIn1h:    10,
			TotalTxCount: 100,
		},
		{
			Wallet:       []byte("high_activity_wallet12345678901234567"),
			MaxTxIn1h:    500,
			TotalTxCount: 1000,
		},
		{
			Wallet:       []byte("medium_activity_wallet1234567890123456"),
			MaxTxIn1h:    100,
			TotalTxCount: 2000,
		},
	}

	err = s.repo.UpsertWalletTxStats(s.ctx, testStats)
	require.NoError(s.T(), err)

	// Get top wallets by max tx in 1h
	topByMaxTx, err := s.repo.GetTopWalletsByMaxTxIn1h(s.ctx, 3)
	require.NoError(s.T(), err)
	require.Len(s.T(), topByMaxTx, 3)

	// Should be ordered by max_tx_in_1h DESC
	assert.Equal(s.T(), int64(500), topByMaxTx[0].MaxTxIn1h)
	assert.Equal(s.T(), int64(100), topByMaxTx[1].MaxTxIn1h)
	assert.Equal(s.T(), int64(10), topByMaxTx[2].MaxTxIn1h)

	// Test with limit
	topByMaxTx, err = s.repo.GetTopWalletsByMaxTxIn1h(s.ctx, 1)
	require.NoError(s.T(), err)
	require.Len(s.T(), topByMaxTx, 1)
	assert.Equal(s.T(), int64(500), topByMaxTx[0].MaxTxIn1h)
}

// TestBackfillWalletTxStatsPartitionCalculation tests the partition range calculation
func (s *WalletTxStatsTestSuite) TestBackfillWalletTxStatsPartitionCalculation() {
	// Setup test tables
	err := createSolTransferEventsTable(s.ctx, s.repo)
	require.NoError(s.T(), err)
	err = setupWalletTxStatsTable(s.ctx, s.repo)
	require.NoError(s.T(), err)

	// Create some sol_transfer_events partitions for testing
	testSlots := []uint64{100, 216000, 432000, 648000}
	for _, slot := range testSlots {
		err := s.repo.EnsureNewPartitionExists(s.ctx, "sol_transfer_events", slot)
		require.NoError(s.T(), err)
	}

	// Test getting partitions in range
	partitions, err := s.repo.getPartitionsInRange(s.ctx, "sol_transfer_events", 200000, 500000)
	require.NoError(s.T(), err)

	// Should include partitions that overlap with range [200000, 500000)
	// p0: [0, 216000) - overlaps
	// p1: [216000, 432000) - overlaps
	// p2: [432000, 648000) - overlaps
	// p3: [648000, 864000) - no overlap
	assert.GreaterOrEqual(s.T(), len(partitions), 2) // At least p1 and p2

	var foundP1, foundP2 bool
	for _, p := range partitions {
		if p.PartitionName == "sol_transfer_events_p1" {
			foundP1 = true
			assert.Equal(s.T(), int64(216000), p.StartSlot)
			assert.Equal(s.T(), int64(432000), p.EndSlot)
		}
		if p.PartitionName == "sol_transfer_events_p2" {
			foundP2 = true
			assert.Equal(s.T(), int64(432000), p.StartSlot)
			assert.Equal(s.T(), int64(648000), p.EndSlot)
		}
	}
	assert.True(s.T(), foundP1, "Should find partition p1")
	assert.True(s.T(), foundP2, "Should find partition p2")
}

// // TestEmptyWalletTxStatsOperations tests operations with empty data
// func (s *WalletTxStatsTestSuite) TestEmptyWalletTxStatsOperations() {
// 	// Setup test tables
// 	err := setupWalletTxStatsTable(s.ctx, s.repo)
// 	require.NoError(s.T(), err)
//
// 	// Test upsert with empty slice
// 	err = s.repo.UpsertWalletTxStats(s.ctx, []WalletTxStats{})
// 	require.NoError(s.T(), err)
//
// 	// Test queries on empty table
// 	topByTotal, err := s.repo.GetTopWalletsByTxCount(s.ctx, 10)
// 	require.NoError(s.T(), err)
// 	assert.Empty(s.T(), topByTotal)
//
// 	topByMaxTx, err := s.repo.GetTopWalletsByMaxTxIn1h(s.ctx, 10)
// 	require.NoError(s.T(), err)
// 	assert.Empty(s.T(), topByMaxTx)
//
// 	// Test backfill with no partitions
// 	err = s.repo.BackfillWalletTxStats(s.ctx, 100, 200)
// 	require.NoError(s.T(), err) // Should not error even with no data
// }

// TestWalletTxStatsTypes tests data type handling
func (s *WalletTxStatsTestSuite) TestWalletTxStatsTypes() {
	// Test boundary values
	testStats := []WalletTxStats{
		{
			Wallet:       make([]byte, 32), // 32 byte wallet address
			MaxTxIn1h:    0,
			TotalTxCount: 0,
		},
		{
			Wallet:       make([]byte, 32),
			MaxTxIn1h:    9223372036854775807, // Max int64
			TotalTxCount: 9223372036854775807, // Max int64
		},
	}

	// Fill wallet addresses with test data
	for i := range testStats[0].Wallet {
		testStats[0].Wallet[i] = byte(i)
		testStats[1].Wallet[i] = byte(255 - i)
	}

	// Validate struct values
	assert.Len(s.T(), testStats[0].Wallet, 32)
	assert.Len(s.T(), testStats[1].Wallet, 32)
	assert.Equal(s.T(), int64(0), testStats[0].MaxTxIn1h)
	assert.Equal(s.T(), int64(9223372036854775807), testStats[1].MaxTxIn1h)
}

// TestBackfillWalletTxStatsWithSolTransfers tests the backfill functionality with actual SOL transfer data
func (s *WalletTxStatsTestSuite) TestBackfillWalletTxStatsWithSolTransfers() {
	// Setup test tables
	err := createSolTransferEventsTable(s.ctx, s.repo)
	require.NoError(s.T(), err)
	err = setupWalletTxStatsTable(s.ctx, s.repo)
	require.NoError(s.T(), err)

	// Create partition for test slot range
	testSlot := uint64(100000)
	err = s.repo.EnsureNewPartitionExists(s.ctx, "sol_transfer_events", testSlot)
	require.NoError(s.T(), err)

	// Insert test SOL transfer events
	wallet1 := []byte("wallet1234567890123456789012345678901")
	wallet2 := []byte("wallet2345678901234567890123456789012")
	wallet3 := []byte("wallet3456789012345678901234567890123")

	// Insert test data directly into sol_transfer_events
	testTransfers := []struct {
		slot       uint64
		txIndex    int32
		ixIndex    int16
		fromWallet []byte
		toWallet   []byte
		amount     int64
	}{
		// wallet1 sends 2 SOL to wallet2 (counts for both wallets)
		{100000, 1, 0, wallet1, wallet2, 2000000000},
		// wallet1 sends 3 SOL to wallet3 (counts for both wallets)
		{100000, 2, 0, wallet1, wallet3, 3000000000},
		// wallet2 sends 1.5 SOL to wallet3 (counts for both wallets)
		{100000, 3, 0, wallet2, wallet3, 1500000000},
		// Small transfer (< 1 SOL) - should be ignored
		{100000, 4, 0, wallet1, wallet2, 500000000},
		// Self-transfer - should be ignored
		{100000, 5, 0, wallet1, wallet1, 2000000000},
	}

	for _, transfer := range testTransfers {
		_, err := s.repo.pool.Exec(s.ctx, `
			INSERT INTO sol_transfer_events (slot, tx_index, ix_index, from_wallet, to_wallet, amount)
			VALUES ($1, $2, $3, $4, $5, $6)
		`, transfer.slot, transfer.txIndex, transfer.ixIndex, transfer.fromWallet, transfer.toWallet, transfer.amount)
		require.NoError(s.T(), err)
	}

	// Run backfill
	err = s.repo.BackfillWalletTxStats(s.ctx, 100000, 100001)
	require.NoError(s.T(), err)

	// Verify results
	topByTotal, err := s.repo.GetTopWalletsByTxCount(s.ctx, 10)
	require.NoError(s.T(), err)
	require.Len(s.T(), topByTotal, 3)

	// wallet1: 2 outgoing transactions (to wallet2, wallet3)
	// wallet2: 1 incoming + 1 outgoing = 2 transactions
	// wallet3: 2 incoming transactions (from wallet1, wallet2)

	// Find each wallet in results
	walletStats := make(map[string]WalletTxStats)
	for _, stats := range topByTotal {
		walletStats[string(stats.Wallet)] = stats
	}

	// wallet1 should have 2 transactions (2 sends)
	assert.Contains(s.T(), walletStats, string(wallet1))
	assert.Equal(s.T(), int64(2), walletStats[string(wallet1)].TotalTxCount)

	// wallet2 should have 2 transactions (1 receive + 1 send)
	assert.Contains(s.T(), walletStats, string(wallet2))
	assert.Equal(s.T(), int64(2), walletStats[string(wallet2)].TotalTxCount)

	// wallet3 should have 2 transactions (2 receives)
	assert.Contains(s.T(), walletStats, string(wallet3))
	assert.Equal(s.T(), int64(2), walletStats[string(wallet3)].TotalTxCount)
}

// Helper function to clean up test data
func cleanupWalletTxStatsTestDB(t *testing.T, repo *Repo) {
	return

	if repo == nil || repo.pool == nil {
		return
	}

	ctx := context.Background()

	// Check if pool is still open
	if err := repo.pool.Ping(ctx); err != nil {
		// Pool is closed or not available, skip cleanup
		return
	}

	// Clean up wallet_tx_stats table
	_, err := repo.pool.Exec(ctx, "DROP TABLE IF EXISTS wallet_tx_stats CASCADE")
	if err != nil {
		t.Logf("Failed to cleanup wallet_tx_stats table: %v", err)
	}

	// Clean up sol_transfer_events partitions
	_, err = repo.pool.Exec(ctx, `
		DO $$
		DECLARE
			partition_name TEXT;
		BEGIN
			FOR partition_name IN
				SELECT tablename FROM pg_tables
				WHERE tablename LIKE 'sol_transfer_events_p%' AND schemaname = 'public'
			LOOP
				EXECUTE 'DROP TABLE IF EXISTS ' || partition_name || ' CASCADE';
			END LOOP;
		END $$;
	`)
	if err != nil {
		t.Logf("Failed to cleanup sol_transfer_events partitions: %v", err)
	}

	// Clean up main sol_transfer_events table
	_, err = repo.pool.Exec(ctx, "DROP TABLE IF EXISTS sol_transfer_events CASCADE")
	if err != nil {
		t.Logf("Failed to cleanup sol_transfer_events table: %v", err)
	}
}
