<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <include file="changes/001-initial-schema.sql" relativeToChangelogFile="true"/>
    <changeSet id="add-base58-function-xml" author="harry">
        <sql splitStatements="false">
            <![CDATA[
CREATE OR REPLACE FUNCTION from_base58(p_text text)
RETURNS bytea AS $FUNCTIONBODY$
DECLARE
    alphabet text := '**********************************************************';
    base58_val int;
    carry bigint;
    i int;
    j int;
    zeros int := 0;
    -- Solana signatures are 64 bytes, which is a sufficient buffer size.
    buffer_size int := 64;
    -- PL/pgSQL arrays are 1-based by default.
    buffer int[] := array_fill(0, ARRAY[buffer_size]);
    result_bytea bytea;
    text_len int := length(p_text);
BEGIN
    -- 1. Count leading '1's which represent zero bytes in the output.
    i := 1;
    WHILE i <= text_len AND substr(p_text, i, 1) = '1' LOOP
        zeros := zeros + 1;
        i := i + 1;
    END LOOP;

    -- 2. Process the rest of the string, performing base conversion.
    FOR i IN (zeros + 1)..text_len LOOP
        -- Find the decimal value of the Base58 character.
        base58_val := strpos(alphabet, substr(p_text, i, 1)) - 1;

        IF base58_val < 0 THEN
            RAISE EXCEPTION 'from_base58: invalid character found: %', substr(p_text, i, 1);
        END IF;

        carry := base58_val;
        -- This loop performs the core arithmetic: buffer = (buffer * 58) + carry.
        -- It iterates from the least significant byte (right) to the most significant (left).
        FOR j IN REVERSE (buffer_size - 1)..0 LOOP
            -- PostgreSQL arrays are 1-based, so buffer indexes are j + 1.
            carry := carry + (buffer[j + 1] * 58);
            buffer[j + 1] := carry % 256;
            carry := carry / 256;
        END LOOP;

        -- For a valid signature, the number should not overflow a 64-byte buffer.
        IF carry > 0 THEN
             RAISE EXCEPTION 'from_base58: input string decodes to a number larger than 64 bytes';
        END IF;
    END LOOP;

    -- 3. Assemble the final bytea result from the buffer using a safer method.

    -- Find the first significant (non-zero) byte in our buffer to trim leading zero padding from the number.
    j := 1;
    WHILE j <= buffer_size AND buffer[j] = 0 LOOP
        j := j + 1;
    END LOOP;

    -- Start with the explicit leading zero bytes from the input '1' characters.
    result_bytea := repeat(E'\\x00', zeros)::bytea;

    -- Append the significant bytes from our buffer by building a hex string and decoding once.
    IF j <= buffer_size THEN
        DECLARE
            hex_part text := '';
        BEGIN
            FOR i IN j..buffer_size LOOP
                -- Use lpad to ensure each byte is represented by two hex digits (e.g., 'a' becomes '0a').
                hex_part := hex_part || lpad(to_hex(buffer[i]), 2, '0');
            END LOOP;
            result_bytea := result_bytea || decode(hex_part, 'hex');
        END;
    END IF;

    RETURN result_bytea;
END;
$FUNCTIONBODY$ LANGUAGE plpgsql IMMUTABLE;
            ]]>
        </sql>
    </changeSet>

    <include file="changes/002-add-p399-partition.sql" relativeToChangelogFile="true"/>
    <changeSet id="add-history-partitions" author="harry">
        <sql splitStatements="false">
            <![CDATA[
DO $$
-- This script automates the creation of partitions for the tables
-- 'sol_transfers' and 'token_transfers'.
-- It loops from partition number 350 to 398.
DECLARE
    -- The loop counter, representing the partition number (e.g., 350 for p350).
    i INT;
    -- The lower bound of the partition range.
    lower_bound BIGINT;
    -- The upper bound of the partition range.
    upper_bound BIGINT;
    -- The base value for calculating partition ranges.
    range_multiplier BIGINT := 864000;
BEGIN
    -- Loop through each partition number from 350 to 398.
    FOR i IN 350..398 LOOP
        -- Calculate the partition range based on the loop variable 'i'.
        -- The lower bound for partition 'i' is (range_multiplier * i).
        lower_bound := range_multiplier * i;
        -- The upper bound for partition 'i' is (range_multiplier * (i + 1)).
        upper_bound := range_multiplier * (i + 1);

        -- Log the action to the console for monitoring progress.
        RAISE NOTICE 'Creating partitions for p% with range [%, %)', i, lower_bound, upper_bound;

        -- Construct and execute the CREATE TABLE statement for the 'sol_transfers' table.
        -- The format() function is used to safely insert the partition number and range
        -- into the SQL command string.
        EXECUTE format(
            'CREATE TABLE sol_transfers_p%s PARTITION OF sol_transfers FOR VALUES FROM (%s) TO (%s);',
            i, lower_bound, upper_bound
        );

        -- Construct and execute the CREATE TABLE statement for the 'token_transfers' table.
        EXECUTE format(
            'CREATE TABLE token_transfers_p%s PARTITION OF token_transfers FOR VALUES FROM (%s) TO (%s);',
            i, lower_bound, upper_bound
        );
    END LOOP;

    RAISE NOTICE 'Successfully created all partitions from p350 to p398.';
END;
$$;
            ]]>
        </sql>
    </changeSet>

    <include file="changes/003-add-slot-times-table.sql" relativeToChangelogFile="true"/>
    <include file="changes/004-create-extension-pgstat.sql" relativeToChangelogFile="true"/>
    <include file="changes/005-new-events-schema.sql" relativeToChangelogFile="true"/>

    <changeSet id="create-new-events-partitions" author="harry">
        <sql splitStatements="false">
            <![CDATA[
DO $$
-- Create partitions for all new event tables (1 day per partition)
-- Partition n starts at slot 216000*n
-- Creating partitions 1400-1613
DECLARE
    i INT;
    lower_bound BIGINT;
    upper_bound BIGINT;
    range_multiplier BIGINT := 216000; -- 1 day worth of slots
BEGIN
    FOR i IN 1400..1613 LOOP
        lower_bound := range_multiplier * i;
        upper_bound := range_multiplier * (i + 1);

        RAISE NOTICE 'Creating transaction_signatures partition p% with range [%, %)', i, lower_bound, upper_bound;
        EXECUTE format(
            'CREATE TABLE transaction_signatures_p%s PARTITION OF transaction_signatures FOR VALUES FROM (%s) TO (%s);',
            i, lower_bound, upper_bound
        );

        RAISE NOTICE 'Creating sol_transfer_events partition p% with range [%, %)', i, lower_bound, upper_bound;
        EXECUTE format(
            'CREATE TABLE sol_transfer_events_p%s PARTITION OF sol_transfer_events FOR VALUES FROM (%s) TO (%s);',
            i, lower_bound, upper_bound
        );

        RAISE NOTICE 'Creating token_transfer_events partition p% with range [%, %)', i, lower_bound, upper_bound;
        EXECUTE format(
            'CREATE TABLE token_transfer_events_p%s PARTITION OF token_transfer_events FOR VALUES FROM (%s) TO (%s);',
            i, lower_bound, upper_bound
        );

        RAISE NOTICE 'Creating sol_balances partition p% with range [%, %)', i, lower_bound, upper_bound;
        EXECUTE format(
            'CREATE TABLE sol_balances_p%s PARTITION OF sol_balances FOR VALUES FROM (%s) TO (%s);',
            i, lower_bound, upper_bound
        );

        RAISE NOTICE 'Creating token_balances partition p% with range [%, %)', i, lower_bound, upper_bound;
        EXECUTE format(
            'CREATE TABLE token_balances_p%s PARTITION OF token_balances FOR VALUES FROM (%s) TO (%s);',
            i, lower_bound, upper_bound
        );
    END LOOP;

    RAISE NOTICE 'Successfully created all partitions from p1600 to p1613 for new event tables.';
END;
$$;
            ]]>
        </sql>
    </changeSet>

    <changeSet id="drop-old-transfer-tables" author="harry">
        <sql splitStatements="false">
            <![CDATA[
DO $$
DECLARE
    i INT;
    partition_name TEXT;
BEGIN
    -- Truncate partitions p350 to p404 for sol_transfers
    FOR i IN 350..404 LOOP
        partition_name := 'sol_transfers_p' || i;
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = partition_name AND table_schema = 'public') THEN
            EXECUTE 'TRUNCATE TABLE ' || partition_name;
            RAISE NOTICE 'Truncated partition: %', partition_name;
        END IF;
    END LOOP;

    -- Truncate partitions p350 to p404 for token_transfers
    FOR i IN 350..404 LOOP
        partition_name := 'token_transfers_p' || i;
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = partition_name AND table_schema = 'public') THEN
            EXECUTE 'TRUNCATE TABLE ' || partition_name;
            RAISE NOTICE 'Truncated partition: %', partition_name;
        END IF;
    END LOOP;

    -- Drop all partitions for sol_transfers
    FOR partition_name IN SELECT tablename FROM pg_tables WHERE tablename LIKE 'sol_transfers_p%' AND schemaname = 'public' LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || partition_name;
        RAISE NOTICE 'Dropped partition: %', partition_name;
    END LOOP;

    -- Drop all partitions for token_transfers
    FOR partition_name IN SELECT tablename FROM pg_tables WHERE tablename LIKE 'token_transfers_p%' AND schemaname = 'public' LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || partition_name;
        RAISE NOTICE 'Dropped partition: %', partition_name;
    END LOOP;

    -- Drop the parent tables
    DROP TABLE IF EXISTS sol_transfers CASCADE;
    DROP TABLE IF EXISTS token_transfers CASCADE;
    RAISE NOTICE 'Dropped parent tables: sol_transfers, token_transfers';
END;
$$;
            ]]>
        </sql>
    </changeSet>
    <include file="changes/006-wallet-interaction.sql" relativeToChangelogFile="true"/>
    <changeSet id="recreate-token-wallet-interaction-hash" author="harry">
        <sql splitStatements="false">
            -- Drop all partitions and parent for token_wallet_interaction_map if they exist
            DO LANGUAGE plpgsql $$
            DECLARE
                rec RECORD;
            BEGIN
                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'token_wallet_interaction_map' AND table_schema = 'public') THEN
                    -- Drop all partitions
                    FOR rec IN SELECT tablename FROM pg_tables WHERE tablename LIKE 'token_wallet_interaction_map_p%' AND schemaname = 'public' LOOP
                        EXECUTE 'DROP TABLE IF EXISTS ' || rec.tablename || ' CASCADE';
                    END LOOP;
                    EXECUTE 'DROP TABLE token_wallet_interaction_map CASCADE';
                END IF;
            END;
            $$;

            -- Create new parent table with hash partitioning
            CREATE TABLE token_wallet_interaction_map (
                token_mint BYTEA NOT NULL,
                source_wallet BYTEA NOT NULL,
                paired_wallet BYTEA NOT NULL,
                PRIMARY KEY (token_mint, source_wallet, paired_wallet)
            ) PARTITION BY HASH (token_mint);
        </sql>
        <sql splitStatements="false">
            -- Create 256 hash partitions
            DO LANGUAGE plpgsql $$
            BEGIN
                FOR i IN 0..255 LOOP
                    EXECUTE format(
                        'CREATE TABLE token_wallet_interaction_map_p%s PARTITION OF token_wallet_interaction_map FOR VALUES WITH (MODULUS 256, REMAINDER %s);',
                        i, i
                    );
                END LOOP;
            END;
            $$;
        </sql>
    </changeSet>
    <changeSet id="add-to-base58-function-xml" author="harry">
        <sql splitStatements="false">
            <![CDATA[
CREATE OR REPLACE FUNCTION to_base58(p_bytea bytea)
RETURNS text AS $FUNCTIONBODY$
DECLARE
    alphabet text := '**********************************************************';
    radix int := 58;
    bytea_len int;
    i int;

    -- Array to hold the input number in base 256.
    bytes int[];

    -- Array to hold the result digits (in base 58) in reverse order.
    base58_digits int[] := '{}';

    -- Variables for long division
    quotient int[];
    remainder int;
    carry int;

    -- Logic for handling leading zeros and loop termination
    leading_zeros int := 0;
    is_zero boolean;

    -- Final string result
    result text := '';
BEGIN
    -- 1. Handle null or empty input.
    IF p_bytea IS NULL OR length(p_bytea) = 0 THEN
        RETURN '';
    END IF;

    bytea_len := length(p_bytea);

    -- 2. Convert bytea to an array of integers and count leading zeros.
    bytes := array_fill(0, ARRAY[bytea_len]);
    FOR i IN 1..bytea_len LOOP
        bytes[i] := get_byte(p_bytea, i - 1);
    END LOOP;

    i := 1;
    WHILE i <= bytea_len AND bytes[i] = 0 LOOP
        leading_zeros := leading_zeros + 1;
        i := i + 1;
    END LOOP;

    -- 3. Perform base conversion using long division (base 256 -> base 58).
    LOOP
        -- Check if the number is zero to terminate the loop.
        is_zero := true;

        -- CORRECTED LOOP SYNTAX: Iterate by index from 1 to the array's length.
        FOR i IN 1..array_length(bytes, 1) LOOP
            IF bytes[i] <> 0 THEN
                is_zero := false;
                EXIT; -- Exits the inner FOR loop
            END IF;
        END LOOP;

        IF is_zero THEN
            EXIT; -- Exits the outer LOOP
        END IF;

        -- Long division: `quotient = bytes / 58`, `remainder = bytes % 58`
        quotient := array_fill(0, ARRAY[bytea_len]);
        carry := 0;

        FOR i IN 1..bytea_len LOOP
            carry := carry * 256 + bytes[i];
            quotient[i] := carry / radix;
            carry := carry % radix;
        END LOOP;

        remainder := carry;

        base58_digits := array_append(base58_digits, remainder);
        bytes := quotient;
    END LOOP;

    -- 4. Assemble the final Base58 string.
    result := repeat('1', leading_zeros);

    FOR i IN REVERSE array_length(base58_digits, 1)..1 LOOP
        result := result || substr(alphabet, base58_digits[i] + 1, 1);
    END LOOP;

    RETURN result;
END;
$FUNCTIONBODY$ LANGUAGE plpgsql IMMUTABLE;
            ]]>
        </sql>
    </changeSet>
    <include file="changes/007-balance-history.sql" relativeToChangelogFile="true"/>
    <include file="changes/008-multi-period-balance-history.sql" relativeToChangelogFile="true"/>
    <include file="changes/009-balance-history-index.sql" relativeToChangelogFile="true"/>
    <include file="changes/010-wallet-tx-stats.sql" relativeToChangelogFile="true"/>
</databaseChangeLog>
