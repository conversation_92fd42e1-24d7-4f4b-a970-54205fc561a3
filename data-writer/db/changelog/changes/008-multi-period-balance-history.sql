--liquibase formatted sql
--changeset author:harry:008-1
-- Create multi-period balance history tables
CREATE TABLE balance_history_5m (
    owner BYTEA NOT NULL,
    token BYTEA NOT NULL,
    balance NUMERIC(20, 0) NOT NULL,
    valid_range TSTZRANGE NOT NULL,
    PRIMARY KEY (owner, token, valid_range)
);

--changeset author:harry:008-2
CREATE INDEX idx_balance_history_5m_gist ON balance_history_5m USING GIST (owner, token, valid_range);

--changeset author:harry:008-3
CREATE TABLE balance_history_15m (
    owner BYTEA NOT NULL,
    token BYTEA NOT NULL,
    balance NUMERIC(20, 0) NOT NULL,
    valid_range TSTZRANGE NOT NULL,
    PRIMARY KEY (owner, token, valid_range)
);

--changeset author:harry:008-4
CREATE INDEX idx_balance_history_15m_gist ON balance_history_15m USING GIST (owner, token, valid_range);

--changeset author:harry:008-5
CREATE TABLE balance_history_1h (
    owner BYTEA NOT NULL,
    token BYTEA NOT NULL,
    balance NUMERIC(20, 0) NOT NULL,
    valid_range TSTZRANGE NOT NULL,
    PRIMARY KEY (owner, token, valid_range)
);

--changeset author:harry:008-6
CREATE INDEX idx_balance_history_1h_gist ON balance_history_1h USING GIST (owner, token, valid_range);

--changeset author:harry:008-7
CREATE TABLE balance_history_4h (
    owner BYTEA NOT NULL,
    token BYTEA NOT NULL,
    balance NUMERIC(20, 0) NOT NULL,
    valid_range TSTZRANGE NOT NULL,
    PRIMARY KEY (owner, token, valid_range)
);

--changeset author:harry:008-8
CREATE INDEX idx_balance_history_4h_gist ON balance_history_4h USING GIST (owner, token, valid_range);

--changeset author:harry:008-9
CREATE TABLE balance_history_1d (
    owner BYTEA NOT NULL,
    token BYTEA NOT NULL,
    balance NUMERIC(20, 0) NOT NULL,
    valid_range TSTZRANGE NOT NULL,
    PRIMARY KEY (owner, token, valid_range)
);

--changeset author:harry:008-10
CREATE INDEX idx_balance_history_1d_gist ON balance_history_1d USING GIST (owner, token, valid_range);