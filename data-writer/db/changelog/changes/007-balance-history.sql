--liquibase formatted sql
--changeset author:harry:007-0
CREATE EXTENSION IF NOT EXISTS btree_gist;

--changeset author:harry:007-1
-- Create balance_history table
CREATE TABLE balance_history (
    owner BYTEA NOT NULL,
    token BYTEA NOT NULL,
    balance NUMERIC(20, 0) NOT NULL,
    valid_range TSTZRANGE NOT NULL,
    PRIMARY KEY (owner, token, valid_range)
);

--changeset author:harry:007-2
-- Create GIST index for fast range queries
CREATE INDEX idx_balance_history_gist ON balance_history USING GIST (owner, token, valid_range);

--changeset author:harry:007-3
-- Create etl_watermark table with first_processed_slot
CREATE TABLE etl_watermark (
    process_name TEXT PRIMARY KEY,
    last_processed_slot BIGINT NOT NULL,
    first_processed_slot BIGINT NOT NULL
);