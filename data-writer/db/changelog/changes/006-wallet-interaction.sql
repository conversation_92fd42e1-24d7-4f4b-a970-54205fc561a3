--liquibase formatted sql
--changeset author:harry:006-1
-- Create sol_wallet_interaction_map as a regular table (no partitioning)
CREATE TABLE sol_wallet_interaction_map (
    source_wallet BYTEA NOT NULL,
    paired_wallet BYTEA NOT NULL,
    PRIMARY KEY (source_wallet, paired_wallet)
);

--changeset author:harry:006-2
-- Create token_wallet_interaction_map partitioned by LIST (get_byte(token_mint, 0))
CREATE TABLE token_wallet_interaction_map (
    token_mint BYTEA NOT NULL,
    source_wallet BYTEA NOT NULL,
    paired_wallet BYTEA NOT NULL
) PARTITION BY LIST ((get_byte(token_mint, 0)));