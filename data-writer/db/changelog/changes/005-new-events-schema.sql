--liquibase formatted sql
--changeset author:harry:005-1
-- Create new signature lookup table
CREATE TABLE transaction_signatures (
    slot BIGINT NOT NULL,
    tx_index INTEGER NOT NULL,
    signature BYTEA NOT NULL,
    PRIMARY KEY (slot, tx_index)
) PARTITION BY RANGE (slot);

--changeset author:harry:005-2
-- Create new sol transfer events table
CREATE TABLE sol_transfer_events (
    slot BIGINT NOT NULL,
    tx_index INTEGER NOT NULL,
    ix_index SMALLINT NOT NULL,
    from_wallet BYTEA NOT NULL,
    to_wallet BYTEA NOT NULL,
    amount NUMERIC(20, 0) NOT NULL,
    PRIMARY KEY (slot, tx_index, ix_index)
) PARTITION BY RANGE (slot);

--changeset author:005-3
-- Create new token transfer events table
CREATE TABLE token_transfer_events (
    slot BIGINT NOT NULL,
    tx_index INTEGER NOT NULL,
    ix_index SMALLINT NOT NULL,
    token_mint BYTEA NOT NULL,
    from_wallet BYTEA NOT NULL,
    to_wallet BYTEA NOT NULL,
    amount NUMERIC(20, 0) NOT NULL,
    PRIMARY KEY (slot, tx_index, ix_index)
) PARTITION BY RANGE (slot);

--changeset author:harry:005-4
-- Create sol balances table
CREATE TABLE sol_balances (
    slot BIGINT NOT NULL,
    account BYTEA NOT NULL,
    amount NUMERIC(20, 0) NOT NULL,
    PRIMARY KEY (slot, account)
) PARTITION BY RANGE (slot);

--changeset author:harry:005-5
-- Create token balances table
CREATE TABLE token_balances (
    slot BIGINT NOT NULL,
    owner BYTEA NOT NULL,
    token BYTEA NOT NULL,
    amount NUMERIC(20, 0) NOT NULL,
    PRIMARY KEY (slot, owner, token)
) PARTITION BY RANGE (slot);