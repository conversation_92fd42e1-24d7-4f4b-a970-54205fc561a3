package db

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCalculateNewPartitionInfoEdgeCases tests edge cases for the new partition calculation logic
func TestCalculateNewPartitionInfoEdgeCases(t *testing.T) {
	tests := []struct {
		name              string
		tableName         string
		slot              uint64
		expectedPartition string
		expectedStartSlot uint64
		expectedEndSlot   uint64
	}{
		{
			name:              "slot 215999 (end of first partition)",
			tableName:         "sol_transfer_events",
			slot:              215999,
			expectedPartition: "sol_transfer_events_p0",
			expectedStartSlot: 0,
			expectedEndSlot:   216000,
		},
		{
			name:              "slot 216000 (start of second partition)",
			tableName:         "token_transfer_events",
			slot:              216000,
			expectedPartition: "token_transfer_events_p1",
			expectedStartSlot: 216000,
			expectedEndSlot:   432000,
		},
		{
			name:              "slot 300000",
			tableName:         "sol_transfer_events",
			slot:              300000,
			expectedPartition: "sol_transfer_events_p1",
			expectedStartSlot: 216000,
			expectedEndSlot:   432000,
		},
		{
			name:              "slot 432000 (start of third partition)",
			tableName:         "transactions",
			slot:              432000,
			expectedPartition: "transactions_p2",
			expectedStartSlot: 432000,
			expectedEndSlot:   648000,
		},
		{
			name:              "very large slot",
			tableName:         "sol_transfer_events",
			slot:              86400000, // Test boundary slot (exactly 400 * 216000)
			expectedPartition: "sol_transfer_events_p400",
			expectedStartSlot: 86400000, // (86400000 / 216000) * 216000 = 400 * 216000
			expectedEndSlot:   86616000,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			partitionName, startSlot, endSlot := calculateNewPartitionInfo(tt.tableName, tt.slot)

			assert.Equal(t, tt.expectedPartition, partitionName,
				"Partition name mismatch for slot %d", tt.slot)
			assert.Equal(t, tt.expectedStartSlot, startSlot,
				"Start slot mismatch for slot %d", tt.slot)
			assert.Equal(t, tt.expectedEndSlot, endSlot,
				"End slot mismatch for slot %d", tt.slot)

			// Verify slot is within calculated bounds
			assert.GreaterOrEqual(t, tt.slot, startSlot,
				"Slot %d should be >= start slot %d", tt.slot, startSlot)
			assert.Less(t, tt.slot, endSlot,
				"Slot %d should be < end slot %d", tt.slot, endSlot)

			// Verify partition size is consistent
			assert.Equal(t, uint64(newPartitionSlotSize), endSlot-startSlot,
				"Partition size should always be %d", newPartitionSlotSize)
		})
	}
}

// TestCalculateNewPartitionInfoDeterministic ensures the function is deterministic
func TestCalculateNewPartitionInfoDeterministic(t *testing.T) {
	testSlots := []uint64{0, 100, 216000, 300000, 432000, 86400000}

	for _, slot := range testSlots {
		// Call the function multiple times
		name1, start1, end1 := calculateNewPartitionInfo("test_table", slot)
		name2, start2, end2 := calculateNewPartitionInfo("test_table", slot)

		assert.Equal(t, name1, name2, "Function should be deterministic for slot %d", slot)
		assert.Equal(t, start1, start2, "Function should be deterministic for slot %d", slot)
		assert.Equal(t, end1, end2, "Function should be deterministic for slot %d", slot)
	}
}

// TestEnsureNewPartitionExistsIntegration tests the partition creation logic with database
func TestEnsureNewPartitionExistsIntegration(t *testing.T) {
	// Skip if not in integration test mode
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	tests := []struct {
		name      string
		tableName string
		slot      uint64
	}{
		{
			name:      "create partition for slot 0",
			tableName: "sol_transfer_events",
			slot:      0,
		},
		{
			name:      "create partition for slot 216000",
			tableName: "sol_transfer_events",
			slot:      216000,
		},
		{
			name:      "create partition for token transfers",
			tableName: "token_transfer_events",
			slot:      100,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Ensure partition exists
			err := repo.EnsureNewPartitionExists(ctx, tt.tableName, tt.slot)
			require.NoError(t, err)

			// Verify partition was created by checking if we can query it
			partitionName, startSlot, endSlot := calculateNewPartitionInfo(tt.tableName, tt.slot)

			var exists bool
			err = repo.pool.QueryRow(ctx, `
				SELECT EXISTS (
					SELECT 1 FROM information_schema.tables 
					WHERE table_name = $1
				)`, partitionName).Scan(&exists)
			require.NoError(t, err)
			assert.True(t, exists, "Partition %s should exist", partitionName)

			// Verify partition bounds by checking partition metadata
			var actualStartSlot, actualEndSlot int64
			err = repo.pool.QueryRow(ctx, `
				SELECT 
					regexp_replace(
						pg_get_expr(c.relpartbound, c.oid), 
						'.*FROM \(''?(\d+)''?\) TO \(''?(\d+)''?\).*', 
						'\1'
					)::bigint as start_slot,
					regexp_replace(
						pg_get_expr(c.relpartbound, c.oid), 
						'.*FROM \(''?(\d+)''?\) TO \(''?(\d+)''?\).*', 
						'\2'
					)::bigint as end_slot
				FROM pg_class c
				WHERE c.relname = $1`, partitionName).Scan(&actualStartSlot, &actualEndSlot)
			require.NoError(t, err)

			assert.Equal(t, int64(startSlot), actualStartSlot, "Start slot should match")
			assert.Equal(t, int64(endSlot), actualEndSlot, "End slot should match")
		})
	}
}

// TestEnsureNewPartitionExistsIdempotent tests that creating the same partition multiple times is safe
func TestEnsureNewPartitionExistsIdempotent(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	tableName := "sol_transfer_events"
	slot := uint64(100)

	// Create partition multiple times
	for i := 0; i < 3; i++ {
		err := repo.EnsureNewPartitionExists(ctx, tableName, slot)
		require.NoError(t, err, "Attempt %d should succeed", i+1)
	}

	// Verify only one partition exists
	partitionName, _, _ := calculateNewPartitionInfo(tableName, slot)
	var count int
	err = repo.pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM information_schema.tables 
		WHERE table_name = $1`, partitionName).Scan(&count)
	require.NoError(t, err)
	assert.Equal(t, 1, count, "Only one partition should exist")
}

// TestGetCurrentPartitions tests the partition listing functionality
func TestGetCurrentPartitions(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupTestDB(t, config)
	}()

	tableName := "sol_transfer_events"

	// Create a few partitions
	testSlots := []uint64{100, 216000, 432000}
	for _, slot := range testSlots {
		err := repo.EnsureNewPartitionExists(ctx, tableName, slot)
		require.NoError(t, err)
	}

	// Get current partitions
	partitions, err := repo.GetCurrentPartitions(ctx, tableName)
	require.NoError(t, err)
	assert.GreaterOrEqual(t, len(partitions), len(testSlots), "Should have at least %d partitions", len(testSlots))

	// Verify that our expected partitions exist
	expectedPartitions := []string{"sol_transfer_events_p0", "sol_transfer_events_p1", "sol_transfer_events_p2"}
	actualPartitions := make([]string, len(partitions))
	for i, p := range partitions {
		actualPartitions[i] = p.PartitionName
	}

	for _, expected := range expectedPartitions {
		assert.Contains(t, actualPartitions, expected, "Should contain partition %s", expected)
	}
}

// Helper function for test database cleanup
func cleanupTestDB(t *testing.T, config *config) {
	// Implementation for cleaning up test database
	// This would drop test tables/partitions
}

// BenchmarkCalculateNewPartitionInfo benchmarks the partition calculation performance
func BenchmarkCalculateNewPartitionInfo(b *testing.B) {
	testSlots := []uint64{0, 100, 216000, 300000, 432000, 86400000}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		slot := testSlots[i%len(testSlots)]
		calculateNewPartitionInfo("sol_transfer_events", slot)
	}
}

// BenchmarkEnsureNewPartitionExists benchmarks partition creation performance
func BenchmarkEnsureNewPartitionExists(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	if err != nil {
		b.Skip("Database not available for benchmark")
	}
	defer repo.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		slot := uint64(i * 1000) // Different slots to avoid partition name conflicts
		repo.EnsureNewPartitionExists(ctx, "sol_transfer_events", slot)
	}
}
