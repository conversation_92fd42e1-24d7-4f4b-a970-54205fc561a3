package db

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

// ProcessSourceChunk is the main entry point for processing a new chunk of data.
// It manages the transaction and the entire cascade within a single database command.
func (r *Repo) ProcessSourceChunk(ctx context.Context, startSlot, endSlot uint64) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Phase 1: Fix up any open-ended ranges from the PREVIOUS chunk. This remains separate.
	if err := r.fixupChunkBoundary(ctx, tx, startSlot, endSlot); err != nil {
		return fmt.Errorf("failed to fixup chunk boundary: %w", err)
	}
	logger.Info(ctx, "Chunk boundary fixup complete")

	// Phase 2 & 3: Process base history and propagate rollups in a single DB operation.
	// This DO block encapsulates the entire logic to prevent round-trips.
	// We use Sprintf to inject the slot boundaries as literals, because DO blocks
	// cannot accept parameters directly. This is safe as start/endSlot are uint64.
	fullProcessQuery := fmt.Sprintf(`
DO $$
DECLARE
    -- Configuration arrays for the rollup cascade
    source_tables TEXT[] := ARRAY['balance_history', 'balance_history_5m', 'balance_history_15m', 'balance_history_1h', 'balance_history_4h'];
    target_tables TEXT[] := ARRAY['balance_history_5m', 'balance_history_15m', 'balance_history_1h', 'balance_history_4h', 'balance_history_1d'];
    target_intervals INTERVAL[] := ARRAY['5 minutes', '15 minutes', '1 hour', '4 hours', '1 day'];
    i INT;
BEGIN
    -- Create the temp tables once. They will store the *consolidated* affected time windows.
    CREATE TEMP TABLE consolidated_affected_windows (
        owner BYTEA, token BYTEA, min_start_ts TIMESTAMPTZ, max_end_ts TIMESTAMPTZ
    ) ON COMMIT DROP;
    CREATE TEMP TABLE next_consolidated_windows (
        owner BYTEA, token BYTEA, min_start_ts TIMESTAMPTZ, max_end_ts TIMESTAMPTZ
    ) ON COMMIT DROP;

    -- STEP 1: Process the raw data and insert the *consolidated* affected windows into our temp table.
    WITH minute_aligned_transactions AS (
        SELECT DISTINCT ON (owner, token, date_trunc('minute', to_timestamp(st.timestamp)))
            owner, token, amount,
            date_trunc('minute', to_timestamp(st.timestamp)) as minute_ts
        FROM token_balances tb JOIN slot_times st ON tb.slot = st.slot
        WHERE tb.slot >= %d AND tb.slot < %d
        ORDER BY owner, token, date_trunc('minute', to_timestamp(st.timestamp)), st.timestamp DESC
    ),
    balance_periods AS (
        SELECT
            owner, token, amount AS balance, minute_ts AS start_ts,
            LEAD(minute_ts, 1) OVER (PARTITION BY owner, token ORDER BY minute_ts ASC) AS next_ts
        FROM minute_aligned_transactions
    ),
    inserted_base_records AS (
        INSERT INTO balance_history (owner, token, balance, valid_range)
        SELECT owner, token, balance, tstzrange(start_ts, next_ts, '[)')
        FROM balance_periods
        ON CONFLICT (owner, token, valid_range) DO UPDATE
        SET balance = EXCLUDED.balance
        RETURNING owner, token, lower(valid_range) AS start_time, upper(valid_range) AS end_time
    )
    INSERT INTO consolidated_affected_windows (owner, token, min_start_ts, max_end_ts)
    SELECT owner, token, MIN(start_time), MAX(COALESCE(end_time, start_time))
    FROM inserted_base_records
    GROUP BY owner, token;

    -- Check if the base insertion created any changes. If not, we can exit early.
    IF NOT FOUND THEN
        RAISE NOTICE 'No new balance changes in chunk [%d, %d).';
        RETURN;
    END IF;

    -- STEP 2: Loop through the aggregation levels to propagate the changes upwards.
    FOR i IN 1..5 LOOP
        -- Exit the loop if the previous level produced no new changes to propagate.
        IF NOT EXISTS (SELECT 1 FROM consolidated_affected_windows) THEN
            RAISE NOTICE 'No more ranges to process at level %%s, exiting rollup loop.', i;
            EXIT;
        END IF;
        
        -- Truncate the destination table before use in the loop.
        TRUNCATE TABLE next_consolidated_windows;

        -- Dynamically execute the core aggregation logic for the current level.
        EXECUTE format('
            WITH newly_affected_ranges AS (
                WITH
                -- The input table is already consolidated, so we use it directly.
                boundary_points AS (
                    SELECT DISTINCT
                        cr.owner,
                        cr.token,
                        generate_series(
                            date_bin(%%L::interval, cr.min_start_ts + %%L::interval, ''2001-01-01''),
                            cr.max_end_ts + %%L::interval,
                            %%L::interval
                        ) AS boundary_ts
                    FROM consolidated_affected_windows cr
                ),
                boundary_states AS (
                    SELECT
                        bp.owner,
                        bp.token,
                        (bp.boundary_ts - ''1 second''::interval) as boundary_ts,
                        (SELECT src.balance FROM %%I src
                         WHERE src.owner = bp.owner AND src.token = bp.token AND src.valid_range @> (bp.boundary_ts - ''1 second''::interval)
                         LIMIT 1) AS true_balance
                    FROM boundary_points bp
                ),
                state_changes AS (
                    SELECT
                        owner, token, true_balance,
                        -- Use date_bin to align the timestamp to the correct interval
                        date_bin(%%L::interval, boundary_ts, ''2001-01-01'') as boundary_ts,
                        LAG(true_balance, 1) OVER (PARTITION BY owner, token ORDER BY boundary_ts) AS prev_balance
                    FROM boundary_states
                ),
                ranges_to_insert AS (
                    SELECT
                        owner,
                        token,
                        true_balance AS balance,
                        tstzrange(boundary_ts, LEAD(boundary_ts, 1) OVER (PARTITION BY owner, token ORDER BY boundary_ts), ''[)'') as valid_range
                    FROM state_changes
                    WHERE true_balance IS DISTINCT FROM prev_balance
                )
                INSERT INTO %%I (owner, token, balance, valid_range)
                SELECT owner, token, balance, valid_range
                FROM ranges_to_insert
                WHERE balance IS NOT NULL AND NOT isempty(valid_range)
                ON CONFLICT (owner, token, valid_range) DO UPDATE SET balance = EXCLUDED.balance
                RETURNING owner, token, lower(valid_range) AS start_time, upper(valid_range) AS end_time
            )
            -- Consolidate the results of this level before inserting into the next-level temp table.
            INSERT INTO next_consolidated_windows (owner, token, min_start_ts, max_end_ts)
            SELECT owner, token, MIN(start_time), MAX(COALESCE(end_time, start_time))
            FROM newly_affected_ranges
            GROUP BY owner, token;
        ',
            target_intervals[i], -- For first date_bin()
			target_intervals[i], -- For second date_bin()
			target_intervals[i], -- For third date_bin()
            target_intervals[i], -- For generate_series()
            source_tables[i],    -- For FROM %%I src
            target_intervals[i], -- For second date_bin()
            target_tables[i]     -- For INSERT INTO %%I
        );

        -- Prepare for the next iteration by cycling the temp tables.
        TRUNCATE TABLE consolidated_affected_windows;
        INSERT INTO consolidated_affected_windows SELECT * FROM next_consolidated_windows;

    END LOOP;
END;
$$;
`, startSlot, endSlot, startSlot, endSlot)

	_, err = tx.Exec(ctx, fullProcessQuery) // No parameters are passed here anymore
	if err != nil {
		return fmt.Errorf("failed executing full processing DO block for chunk [%d, %d): %w", startSlot, endSlot, err)
	}

	return tx.Commit(ctx)
}

// fixupChunkBoundary handles the "stitching" of a new chunk to the previous one.
func (r *Repo) fixupChunkBoundary(ctx context.Context, tx pgx.Tx, startSlot, endSlot uint64) error {
	query := `
        CREATE TEMP TABLE first_events_in_chunk AS
        SELECT DISTINCT ON (owner, token)
            owner,
            token,
            to_timestamp(st.timestamp) AS first_ts
        FROM token_balances tb
        JOIN slot_times st ON tb.slot = st.slot
        WHERE tb.slot >= $1 AND tb.slot < $2
        ORDER BY owner, token, tb.slot ASC;
    `
	if _, err := tx.Exec(ctx, query, startSlot, endSlot); err != nil {
		if err == pgx.ErrNoRows {
			return nil
		}
		return fmt.Errorf("failed to create temp table for fixup: %w", err)
	}

	for _, period := range PeriodConfigs {
		interval := PostgreSQLInterval(period.Duration)

		// First, delete rows where lower(target.valid_range) = date_bin('%s', fe.first_ts, '2001-01-01')
		deleteQuery := fmt.Sprintf(`
            DELETE FROM %s target
            USING first_events_in_chunk fe
            WHERE target.owner = fe.owner
              AND target.token = fe.token
              AND upper_inf(target.valid_range)
              AND lower(target.valid_range) = date_bin('%s', fe.first_ts, '2001-01-01');
        `, period.Table, interval)
		if _, err := tx.Exec(ctx, deleteQuery); err != nil {
			return fmt.Errorf("failed to delete overlapping rows for table %s: %w", period.Table, err)
		}

		// Then, update the remaining rows as before
		fixupQuery := fmt.Sprintf(`
            UPDATE %s target
            SET valid_range = tstzrange(lower(target.valid_range), date_bin('%s', fe.first_ts, '2001-01-01'), '[)')
            FROM first_events_in_chunk fe
            WHERE target.owner = fe.owner
              AND target.token = fe.token
              AND upper_inf(target.valid_range)
              AND lower(target.valid_range) < date_bin('%s', fe.first_ts, '2001-01-01');
        `, period.Table, interval, interval)
		if _, err := tx.Exec(ctx, fixupQuery); err != nil {
			return fmt.Errorf("failed to fixup boundary for table %s: %w", period.Table, err)
		}
	}

	_, err := tx.Exec(ctx, "DROP TABLE IF EXISTS first_events_in_chunk;")
	return err
}
