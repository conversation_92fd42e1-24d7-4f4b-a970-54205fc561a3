package db

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

// WalletTxStats represents wallet transaction statistics
type WalletTxStats struct {
	Wallet       []byte `json:"wallet"`
	MaxTxIn1h    int64  `json:"max_tx_in_1h"`
	TotalTxCount int64  `json:"total_tx_count"`
}

// BackfillWalletTxStats backfills wallet transaction statistics from existing data
func (r *Repo) BackfillWalletTxStats(ctx context.Context, startSlot, endSlot uint64) error {
	logger.Info(ctx, "Starting wallet transaction stats backfill for slots %d to %d", startSlot, endSlot)

	// Get all partitions that cover the slot range
	partitions, err := r.getPartitionsInRange(ctx, "transaction_signatures", startSlot, endSlot)
	if err != nil {
		return fmt.Errorf("failed to get partitions in range: %v", err)
	}

	logger.Info(ctx, "Found %d partitions to process", len(partitions))

	// Process each partition
	for _, partition := range partitions {
		logger.Info(ctx, "Processing partition %s (slots %d-%d)", partition.PartitionName, partition.StartSlot, partition.EndSlot)

		if err := r.processPartitionForWalletStats(ctx, partition.PartitionName, startSlot, endSlot); err != nil {
			return fmt.Errorf("failed to process partition %s: %v", partition.PartitionName, err)
		}
	}

	logger.Info(ctx, "Completed wallet transaction stats backfill")
	return nil
}

// getPartitionsInRange returns partitions that overlap with the given slot range
func (r *Repo) getPartitionsInRange(ctx context.Context, tableName string, startSlot, endSlot uint64) ([]PartitionInfo, error) {
	allPartitions, err := r.GetCurrentPartitions(ctx, tableName)
	if err != nil {
		return nil, err
	}

	var relevantPartitions []PartitionInfo
	for _, partition := range allPartitions {
		// Check if partition overlaps with our range
		if uint64(partition.StartSlot) < endSlot && uint64(partition.EndSlot) > startSlot {
			relevantPartitions = append(relevantPartitions, partition)
		}
	}

	return relevantPartitions, nil
}

// processPartitionForWalletStats processes a single partition to calculate wallet transaction stats
func (r *Repo) processPartitionForWalletStats(ctx context.Context, partitionName string, startSlot, endSlot uint64) error {
	// Query to calculate wallet transaction statistics for this partition
	query := fmt.Sprintf(`
		WITH hourly_tx_counts AS (
			-- Calculate hourly transaction counts for each wallet
			SELECT
				from_wallet,
				-- Convert slot to hour (approximate: 9000 slots per hour)
				(slot / 9000) as hour_bucket,
				COUNT(*) as tx_count
			FROM (
				-- Get unique signers per transaction using UNNEST
				SELECT DISTINCT
					ts.slot,
					unnest(ts.signers) as from_wallet
				FROM %s ts
				WHERE ts.slot >= $1 AND ts.slot < $2
			) wallet_tx
			GROUP BY from_wallet, hour_bucket
		),
		wallet_stats AS (
			-- Calculate max transactions in 1 hour and total transactions per wallet
			SELECT
				from_wallet,
				MAX(tx_count) as max_tx_in_1h,
				SUM(tx_count) as total_tx_count
			FROM hourly_tx_counts
			GROUP BY from_wallet
		)
		-- Insert or update wallet transaction stats
		INSERT INTO wallet_tx_stats (wallet, max_tx_in_1h, total_tx_count)
		SELECT
			from_wallet,
			max_tx_in_1h,
			total_tx_count
		FROM wallet_stats
		ON CONFLICT (wallet) DO UPDATE SET
			max_tx_in_1h = GREATEST(wallet_tx_stats.max_tx_in_1h, EXCLUDED.max_tx_in_1h),
			total_tx_count = wallet_tx_stats.total_tx_count + EXCLUDED.total_tx_count
	`, partitionName)

	result, err := r.pool.Exec(ctx, query, startSlot, endSlot)
	if err != nil {
		return fmt.Errorf("failed to execute wallet stats query: %v", err)
	}

	rowsAffected := result.RowsAffected()
	logger.Info(ctx, "Processed %d wallet records from partition %s", rowsAffected, partitionName)

	return nil
}

// GetTopWalletsByTxCount returns top wallets by transaction count
func (r *Repo) GetTopWalletsByTxCount(ctx context.Context, limit int) ([]WalletTxStats, error) {
	query := `
		SELECT wallet, max_tx_in_1h, total_tx_count
		FROM wallet_tx_stats
		ORDER BY total_tx_count DESC
		LIMIT $1
	`

	rows, err := r.pool.Query(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query top wallets by tx count: %v", err)
	}
	defer rows.Close()

	var results []WalletTxStats
	for rows.Next() {
		var stats WalletTxStats
		if err := rows.Scan(&stats.Wallet, &stats.MaxTxIn1h, &stats.TotalTxCount); err != nil {
			return nil, fmt.Errorf("failed to scan wallet stats: %v", err)
		}
		results = append(results, stats)
	}

	return results, nil
}

// GetTopWalletsByMaxTxIn1h returns top wallets by maximum transactions in 1 hour
func (r *Repo) GetTopWalletsByMaxTxIn1h(ctx context.Context, limit int) ([]WalletTxStats, error) {
	query := `
		SELECT wallet, max_tx_in_1h, total_tx_count
		FROM wallet_tx_stats
		ORDER BY max_tx_in_1h DESC
		LIMIT $1
	`

	rows, err := r.pool.Query(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query top wallets by max tx in 1h: %v", err)
	}
	defer rows.Close()

	var results []WalletTxStats
	for rows.Next() {
		var stats WalletTxStats
		if err := rows.Scan(&stats.Wallet, &stats.MaxTxIn1h, &stats.TotalTxCount); err != nil {
			return nil, fmt.Errorf("failed to scan wallet stats: %v", err)
		}
		results = append(results, stats)
	}

	return results, nil
}

// UpsertWalletTxStats upserts wallet transaction statistics
func (r *Repo) UpsertWalletTxStats(ctx context.Context, stats []WalletTxStats) error {
	if len(stats) == 0 {
		return nil
	}

	return r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
		// Create temporary table
		tempTableName := "temp_wallet_tx_stats"
		if _, err := tx.Exec(ctx, fmt.Sprintf("CREATE TEMPORARY TABLE %s (LIKE wallet_tx_stats) ON COMMIT DROP", tempTableName)); err != nil {
			return fmt.Errorf("failed to create temporary table: %v", err)
		}

		// Copy data to temporary table
		rows := make([][]interface{}, len(stats))
		for i, s := range stats {
			rows[i] = []interface{}{
				s.Wallet,
				s.MaxTxIn1h,
				s.TotalTxCount,
			}
		}

		_, err := tx.CopyFrom(ctx,
			pgx.Identifier{tempTableName},
			[]string{"wallet", "max_tx_in_1h", "total_tx_count"},
			pgx.CopyFromRows(rows))
		if err != nil {
			return fmt.Errorf("failed to copy to temporary table: %v", err)
		}

		// Upsert from temporary table
		query := fmt.Sprintf(`
			INSERT INTO wallet_tx_stats
			SELECT * FROM %s
			ON CONFLICT (wallet) DO UPDATE SET
				max_tx_in_1h = GREATEST(wallet_tx_stats.max_tx_in_1h, EXCLUDED.max_tx_in_1h),
				total_tx_count = wallet_tx_stats.total_tx_count + EXCLUDED.total_tx_count
		`, tempTableName)

		if _, err := tx.Exec(ctx, query); err != nil {
			return fmt.Errorf("failed to upsert from temporary table: %v", err)
		}

		return nil
	})
}
