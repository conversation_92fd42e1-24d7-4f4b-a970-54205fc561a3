apiVersion: apps/v1
kind: Deployment
metadata:
  name: stream-processor
  namespace: solana-data
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: stream-processor
  template:
    metadata:
      labels:
        app: stream-processor
    spec:
      serviceAccountName: default-ksa
      containers:
        - name: stream-processor
          image: asia-northeast1-docker.pkg.dev/kryptogo-wallet-data/kg-solana-data/stream-processor:latest
          workingDir: /app
          command: ["/app/stream-processor"]
          resources:
            requests:
              cpu: "0.5"
              memory: "500Mi"
            limits:
              cpu: "1"
              memory: "1Gi"
          ports:
            - containerPort: 8080
              name: health
          env:
            - name: GRPC_URL
              value: "http://10.146.0.5:10001"
            - name: GRPC_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: grpc-auth-secret
                  key: token
            - name: GRPC_TIMEOUT
              value: "30000"
            - name: HEALTH_CHECK_PORT
              value: "8080"
          livenessProbe:
            httpGet:
              path: /health
              port: health
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: health
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: data-writer
  namespace: solana-data
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      app: data-writer
  template:
    metadata:
      labels:
        app: data-writer
    spec:
      serviceAccountName: default-ksa
      containers:
        - name: data-writer
          image: asia-northeast1-docker.pkg.dev/kryptogo-wallet-data/kg-solana-data/data-writer:latest
          imagePullPolicy: Always
          workingDir: /app
          command: ["/app/data-writer"]
          resources:
            requests:
              cpu: "0.5"
              memory: "1Gi"
            limits:
              cpu: "1.0"
              memory: "3Gi"
          env:
            - name: DB_HOST
              value: "**********"
            - name: DB_PORT
              value: "5432"
            - name: DB_USER
              value: "solana_data"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: password
            - name: DB_NAME
              value: "solana_data"
            - name: DB_SSL_MODE
              value: "require"
            - name: DB_MAX_CONNS
              value: "100"
            - name: HEALTH_CHECK_PORT
              value: "8080"
            - name: PUBSUB_NUM_GOROUTINES
              value: "1"
            - name: PUBSUB_MAX_OUTSTANDING_MESSAGES
              value: "50"
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
