# Balance History Implementation

This document describes the implementation of the balance history feature for tracking token balance changes over time.

## Overview

The balance history feature provides efficient time-series aggregation of token balances, allowing queries to find the balance of any wallet/token combination at any point in time.

## Architecture

### Database Schema

#### `balance_history` Table

```sql
CREATE TABLE balance_history (
    owner       BY<PERSON>A NOT NULL,
    token       BYTEA NOT NULL,
    balance     NUMERIC(20, 0) NOT NULL,
    valid_range TSTZRANGE NOT NULL,
    PRIMARY KEY (owner, token, valid_range)
);

CREATE INDEX idx_balance_history_gist
    ON balance_history
    USING GIST (owner, token, valid_range);
```

#### `etl_watermark` Table

```sql
CREATE TABLE etl_watermark (
    process_name        TEXT PRIMARY KEY,
    last_processed_slot BIGINT NOT NULL,
    first_processed_slot BIGINT NOT NULL
);
```

### Key Features

1. **GIST Index**: Enables fast range queries using PostgreSQL's spatial indexing
2. **TSTZRANGE**: PostgreSQL's timestamp with timezone range type for efficient time range operations
3. **Watermark Tracking**: Ensures no data is missed or reprocessed
4. **Chunked Processing**: Processes data in manageable chunks (1000 slots at a time)
5. **Two-Phase Processing**: Implements precise chunk boundary handling

## Components

### 1. Backfill Process (`balance-history-backfill`)

**Purpose**: Process historical data from slot 432000*700 to a target end slot.

**Usage**:

```bash
# Set environment variable
export TARGET_END_SLOT=348720000

# Run the backfill
./balance-history-backfill
```

**Features**:

- Reads last processed slot from `etl_watermark` table
- Starts from slot 432000*700 if no watermark exists
- Processes data in 1000-slot chunks
- Updates watermark after each chunk
- Implements precise two-phase chunk processing

### 2. Delta Process (`balance-history-delta`)

**Purpose**: Process new data in real-time (runs every minute via cronjob).

**Features**:

- Processes up to (latest_slot - 10) to avoid incomplete data
- Updates previous open-ended ranges when new data arrives
- Inserts new balance history records
- Updates watermark atomically
- Implements precise two-phase chunk processing

### 3. Query Tool (`balance-history-query`)

**Purpose**: Test and query balance history data.

**Usage**:

```bash
./balance-history-query \
  -wallet "YourWalletAddress" \
  -token "TokenMintAddress" \
  -from "2024-01-01T00:00:00Z" \
  -to "2024-01-02T00:00:00Z"
```

## Deployment

### 1. Database Migration

Run the Liquibase migration to create the required tables:

```bash
# The migration is included in changelog.xml
# Tables will be created automatically when the application starts
```

### 2. Backfill Jobs

Generate and deploy backfill jobs:

```bash
# Generate jobs for a specific slot range
./scripts/generate-balance-history-backfill-jobs.sh 302400000 348720000 10

# Apply the jobs to Kubernetes
kubectl apply -f jobs/
```

### 3. Delta CronJob

Deploy the delta processing cronjob:

```bash
kubectl apply -f kubernetes/balance-history-delta-cronjob.yaml
```

## Processing Logic

### Two-Phase Chunk Processing

The implementation uses a precise two-phase approach for each chunk:

#### Phase 1: Main Processing (ProcessBalanceHistoryChunk)

For each chunk of data:

1. **Create Temporary Table**: Gather all relevant transactions in the current chunk
2. **Calculate Validity Ranges**: Use `LEAD()` window function to find next timestamps
3. **Insert Records**: Create balance history records with proper time ranges
4. **Handle Infinity**: Records without a next timestamp get unbounded ranges ending at infinity

### 1-Minute Normalization

To avoid inserting duplicated data within one minute for a wallet/token, the process normalizes all timestamps to 1-minute buckets:

- Each transaction's timestamp is truncated to the minute using `date_trunc('minute', ...)`.
- Only the latest balance per (wallet, token, minute) is kept within each chunk.
- The LEAD/fixup logic operates on these minute buckets, so only one record per wallet/token/minute is inserted into `balance_history`.

#### Example SQL for Chunk Processing

```sql
CREATE TEMP TABLE current_chunk_transactions AS
SELECT DISTINCT ON (tb.owner, tb.token, date_trunc('minute', to_timestamp(st.timestamp)))
    tb.owner,
    tb.token,
    tb.amount,
    date_trunc('minute', to_timestamp(st.timestamp)) AS minute_ts
FROM
    token_balances tb
JOIN
    slot_times st ON tb.slot = st.slot
WHERE
    tb.slot >= $1 AND tb.slot <= $2
ORDER BY
    tb.owner, tb.token, date_trunc('minute', to_timestamp(st.timestamp)), st.timestamp DESC;

WITH balance_periods AS (
    SELECT
        owner,
        token,
        amount AS balance,
        minute_ts AS start_ts,
        LEAD(minute_ts, 1, NULL) OVER (PARTITION BY owner, token ORDER BY minute_ts ASC) AS next_ts
    FROM
        current_chunk_transactions
)
INSERT INTO balance_history (owner, token, balance, valid_range)
SELECT
    owner,
    token,
    balance,
    tstzrange(start_ts, next_ts, '[)')
FROM
    balance_periods
ON CONFLICT (owner, token, valid_range) DO NOTHING;

DROP TABLE current_chunk_transactions;
```

This ensures that for any given wallet/token, there is at most one balance_history record per minute.

#### Phase 2: Fixup Processing (FixupPreviousBalanceHistory)

Before processing the next chunk:

1. **Find First Transactions**: Identify the first transaction for each wallet in the upcoming chunk
2. **Update Infinity Ranges**: Close open-ended ranges from the previous chunk
3. **Stitch Chunks**: Ensure continuity between chunks

```sql
-- Step 1: Find the very first transaction for each wallet in the upcoming chunk
CREATE TEMP TABLE first_transactions_in_next_chunk AS
SELECT DISTINCT ON (tb.owner, tb.token)
    tb.owner,
    tb.token,
    to_timestamp(st.timestamp) as first_ts
FROM
    token_balances tb
JOIN
    slot_times st ON tb.slot = st.slot
WHERE
    tb.slot >= $1 AND tb.slot <= $2
ORDER BY
    tb.owner, tb.token, tb.slot ASC;

-- Step 2: The "Fix-up" UPDATE
UPDATE
    balance_history bh
SET
    valid_range = tstzrange(lower(bh.valid_range), ft.first_ts, '[)')
FROM
    first_transactions_in_next_chunk ft
WHERE
    bh.owner = ft.owner AND bh.token = ft.token
    AND upper_inf(bh.valid_range);

-- Clean up the temporary table
DROP TABLE first_transactions_in_next_chunk;
```

### Processing Flow

1. **Start**: Read watermark to determine starting slot
2. **For each chunk**:
   - **Fixup Phase**: Update previous chunk's infinity ranges (if not first chunk)
   - **Processing Phase**: Process current chunk and create new records
   - **Update Watermark**: Record progress atomically
3. **Continue**: Move to next chunk until target is reached

## Querying Balance History

### Time Range Queries

```sql
SELECT 
    owner,
    token,
    balance,
    valid_range
FROM balance_history
WHERE owner = $1 
AND token = $2
AND valid_range && tstzrange($3, $4, '[]')
ORDER BY lower(valid_range);
```

### Point-in-Time Queries

```sql
SELECT 
    owner,
    token,
    balance
FROM balance_history
WHERE owner = $1 
AND token = $2
AND $3 <@ valid_range
LIMIT 1;
```

## Monitoring

### Watermark Status

Check processing progress:

```sql
SELECT * FROM etl_watermark WHERE process_name = 'balance_history_etl';
```

### Data Quality

Verify no gaps in time ranges:

```sql
SELECT 
    owner,
    token,
    COUNT(*) as record_count,
    MIN(lower(valid_range)) as earliest_time,
    MAX(upper(valid_range)) as latest_time
FROM balance_history
GROUP BY owner, token
ORDER BY record_count DESC;
```

### Check for Infinity Ranges

Find any remaining unbounded ranges (should be minimal):

```sql
SELECT COUNT(*) 
FROM balance_history 
WHERE upper_inf(valid_range);
```

## Performance Considerations

1. **GIST Index**: Critical for fast range queries
2. **Chunked Processing**: Prevents memory issues with large datasets
3. **Temporary Tables**: Efficient processing within each chunk
4. **Watermark Updates**: Atomic operations prevent data loss
5. **Conflict Handling**: `ON CONFLICT DO NOTHING` prevents duplicate processing
6. **Two-Phase Processing**: Ensures data continuity across chunk boundaries

## Troubleshooting

### Common Issues

1. **Missing slot_times data**: Ensure slot_times table is populated
2. **Watermark corruption**: Check etl_watermark table for inconsistencies
3. **Memory issues**: Reduce chunk size if processing large datasets
4. **Time zone issues**: Ensure all timestamps are in UTC
5. **Infinity ranges**: Check for unbounded ranges that weren't properly closed

### Debug Commands

```bash
# Check watermark status
kubectl exec -it <pod-name> -- ./balance-history-query -wallet <wallet> -token <token> -from <time> -to <time>

# Check latest processed slot
kubectl logs <pod-name> | grep "Processing balance history"

# Check for infinity ranges
kubectl exec -it <pod-name> -- psql -c "SELECT COUNT(*) FROM balance_history WHERE upper_inf(valid_range);"
```

### Validation Queries

```sql
-- Check for gaps in time ranges
WITH gaps AS (
    SELECT 
        owner,
        token,
        upper(valid_range) as range_end,
        LEAD(lower(valid_range)) OVER (
            PARTITION BY owner, token 
            ORDER BY lower(valid_range)
        ) as next_start
    FROM balance_history
    WHERE NOT upper_inf(valid_range)
)
SELECT COUNT(*) as gap_count
FROM gaps
WHERE range_end < next_start;
```
