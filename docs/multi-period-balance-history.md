# Multi-Period Balance History

This document describes the multi-period balance history feature that maintains token balance history across different time granularities (1m, 5m, 15m, 1h, 4h, 1d) with efficient GIST indexes for range queries.

## Overview

The multi-period balance history system maintains 6 separate tables, each optimized for a specific time granularity:

- `balance_history` (1 minute)
- `balance_history_5m` (5 minutes)
- `balance_history_15m` (15 minutes)
- `balance_history_1h` (1 hour)
- `balance_history_4h` (4 hours)
- `balance_history_1d` (1 day)

Each table uses PostgreSQL's `TSTZRANGE` type to store time-valid ranges and GIST indexes for efficient range queries.

## Architecture

### Cascading ETL Strategy

The system uses a **cascading ETL pipeline** instead of parallel processing to maximize efficiency:

1. **Base Processing**: Only the 1-minute table reads from `token_balances`
2. **Cascading Rollups**: Each higher-level table reads from the previous level
3. **Transactional Consistency**: All operations within a chunk are wrapped in a single transaction

This approach dramatically reduces database load by eliminating redundant scans of the large `token_balances` table.

### Processing Flow

```
token_balances → balance_history (1m) → balance_history_5m → balance_history_15m → balance_history_1h → balance_history_4h → balance_history_1d
```

Each step processes significantly less data than the previous one, resulting in exponential performance improvements.

## Schema

### Table Structure

All balance history tables follow the same structure:

```sql
CREATE TABLE balance_history_<period> (
    owner       BYTEA NOT NULL,
    token       BYTEA NOT NULL,
    balance     NUMERIC(20, 0) NOT NULL,
    valid_range TSTZRANGE NOT NULL,
    PRIMARY KEY (owner, token, valid_range)
);

CREATE INDEX idx_balance_history_<period>_gist
    ON balance_history_<period>
    USING GIST (owner, token, valid_range);
```

### Key Features

1. **Time Range Storage**: Uses `TSTZRANGE` to store when each balance was valid
2. **GIST Indexes**: Efficient range queries for time-based lookups
3. **Period Alignment**: Timestamps are truncated to the appropriate period boundary
4. **Zero-Range Cleanup**: Automatically removes records with zero time ranges

## Processing Logic

### Chunk Processing

The system processes balance history in chunks to handle large datasets efficiently:

1. **Base Processing**: Process 1-minute data from `token_balances`
2. **Cascading Rollups**: Propagate changes up the aggregation chain
3. **Fixup Phase**: Update previous chunk's open-ended ranges with actual end times
4. **Zero-Range Cleanup**: Remove records where start time equals end time

### Cascading ETL Implementation

```go
func (r *Repo) ProcessBalanceHistoryChunk(ctx context.Context, startSlot, endSlot uint64) error {
    return r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
        // 1. Process the base 1m table (only function that reads from token_balances)
        newlyAffectedRanges, err := r.processBaseHistory(ctx, tx, startSlot, endSlot)
        if err != nil { return err }

        // 2. Cascade changes up the chain. Each function reads from its predecessor.
        for i := 1; i < len(PeriodConfigs); i++ {
            sourceTable := PeriodConfigs[i-1].Table
            targetConfig := PeriodConfigs[i]
            
            newlyAffectedRanges, err = r.propagateToRollup(ctx, tx, newlyAffectedRanges, sourceTable, targetConfig)
            if err != nil { return err }
        }

        return nil
    })
}
```

### Period Alignment

Timestamps are aligned to period boundaries using PostgreSQL's `date_trunc` function:

```sql
date_trunc('5m', to_timestamp(st.timestamp)) AS period_ts
```

This ensures consistent period boundaries across all records.

### Zero-Range Handling

When the previous chunk and current chunk have the same record (owner/token/aligned time), the system:

1. Updates the previous record to have a zero range `[t, t)`
2. Automatically deletes zero-range records during fixup

```sql
-- Delete zero-range records
DELETE FROM balance_history_<period> 
WHERE lower(valid_range) = upper(valid_range);
```

## Query Performance

### Database-Optimized LOCF Logic

The `GetBalanceHistoryPoints` function uses database-side "Last Observation Carried Forward" (LOCF) logic for optimal performance:

```sql
-- Step 1: Generate the series of timestamps for our chart's x-axis.
WITH points_in_time AS (
    SELECT generate_series($3::timestamptz, $4::timestamptz, $5::interval) AS point
)
-- Step 2: For each generated point, efficiently find the correct balance.
SELECT
    p.point,
    -- This subquery uses the GIST index to perform a highly optimized
    -- lookup for the state at that exact point in time.
    (SELECT bh.balance
     FROM balance_history_5m bh
     WHERE bh.owner = $1
       AND bh.token = $2
       AND bh.valid_range @> p.point -- The magic is here!
     LIMIT 1) AS balance
FROM
    points_in_time p
ORDER BY
    p.point;
```

This approach:

- Eliminates the need to fetch large amounts of data into application memory
- Leverages GIST indexes for efficient range containment queries
- Handles missing data gracefully by returning NULL for periods without data

## Commands

### Backfill Command

Processes historical balance data using cascading ETL:

```bash
# Process all periods
TARGET_END_SLOT=432000000 ./balance-history-backfill

# Process specific periods
TARGET_END_SLOT=432000000 PERIODS="1m,5m,1h" ./balance-history-backfill
```

### Delta Command

Processes new balance data in real-time:

```bash
# Process all periods every 30 seconds
./balance-history-delta

# Process specific periods every 60 seconds
PERIODS="1m,5m" INTERVAL=60 ./balance-history-delta
```

### Query Command

Retrieves balance history for specific periods:

```bash
# Get balance points at 5-minute intervals
./balance-history-query \
  -wallet "YourWalletAddress" \
  -token "TokenMintAddress" \
  -from "2024-01-01T00:00:00Z" \
  -to "2024-01-02T00:00:00Z" \
  -period "5m"
```

## Supported Periods

- `1m`: 1 minute (balance_history)
- `5m`: 5 minutes (balance_history_5m)
- `15m`: 15 minutes (balance_history_15m)
- `1h`: 1 hour (balance_history_1h)
- `4h`: 4 hours (balance_history_4h)
- `1d`: 1 day (balance_history_1d)

## Performance Considerations

### Cascading ETL Benefits

- **Reduced I/O**: Each level processes significantly less data than the previous
- **Eliminated Redundancy**: No parallel scanning of large source tables
- **Transactional Consistency**: All operations within a chunk are atomic
- **Scalability**: Performance scales with data size, not number of periods

### GIST Indexes

GIST indexes on `(owner, token, valid_range)` provide efficient range queries:

```sql
-- Efficient query using GIST index
SELECT * FROM balance_history_5m
WHERE owner = $1 
  AND token = $2
  AND valid_range @> $3::timestamptz;  -- Contains operator
```

### Memory Usage

- Database-side LOCF eliminates large memory allocations
- Chunk-based processing limits memory usage
- Zero-range cleanup prevents accumulation of invalid records

## Monitoring

### Watermark Table

The system tracks processing progress using the `etl_watermark` table:

```sql
SELECT * FROM etl_watermark WHERE process_name = 'balance_history_etl';
```

### Logging

All commands provide detailed logging:

```
INFO: Processing balance history chunk: slots [432000000, 432001000)
INFO: Propagating to rollup 5m: 150 affected ranges
INFO: Fixup previous balance history for period 1h: slots [432001000, 432002000)
INFO: Deleted 5 zero-range records for period 15m
```

## Migration

The multi-period balance history feature is deployed via Liquibase migration:

```sql
-- Migration: 008-multi-period-balance-history.sql
-- Creates all 6 tables with GIST indexes
```

To deploy:

```bash
# Run migration
./scripts/deploy-db.sh
```

## Troubleshooting

### Common Issues

1. **Zero-range records not deleted**: Check if fixup phase is running correctly
2. **Period alignment issues**: Verify timestamp truncation logic
3. **Performance issues**: Check GIST index usage with `EXPLAIN ANALYZE`
4. **Cascading failures**: Verify transaction boundaries and error handling

### Debugging

Enable debug logging:

```bash
LOG_LEVEL=debug ./balance-history-delta
```

Check table statistics:

```sql
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del
FROM pg_stat_user_tables 
WHERE tablename LIKE 'balance_history%';
```

### Performance Analysis

Analyze query performance:

```sql
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM balance_history_5m
WHERE owner = $1 
  AND token = $2
  AND valid_range @> $3::timestamptz;
```
