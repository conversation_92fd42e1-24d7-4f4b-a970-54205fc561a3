# Data Writer V2 Implementation

## Overview

This document describes the implementation of the new V2 event processing system for the data writer, which supports the updated schema while maintaining backward compatibility with existing events.

## Key Features

### 1. Backward Compatibility

- **Legacy Events**: Continue to support `SolTransferEvent`, `TokenTransferEvent`, and `SlotTime`
- **New V2 Events**: Support `SolTransferEventV2`, `TokenTransferEventV2`, `TransactionEvent`, `SolBalanceEvent`, and `TokenBalanceEvent`
- **Mixed Processing**: Can handle both old and new event types in the same message

### 2. Generic Event Processing

- **`processEventsByPartition[T]`**: Generic function that handles partitioning and parallel processing for any event type
- **`groupEventsByPartition[T]`**: Generic function for grouping events by their target partition
- **Reduced Code Duplication**: Single implementation for both legacy and new events

### 3. New Schema Support

- **1-Day Partitions**: New events use 216,000 slots per partition (1 day)
- **Legacy Partitions**: Old events continue using 864,000 slots per partition (4 days)
- **Partition Calculation**: Automatic partition calculation based on event type

## Implementation Details

### New Models (`models/transfer.go`)

```go
// V2 Event Models
type SolTransferEventV2 struct {
    Slot       uint64
    TxIndex    uint32
    IxIndex    uint16
    FromWallet []byte
    ToWallet   []byte
    Amount     uint64
}

type TokenTransferEventV2 struct {
    Slot       uint64
    TxIndex    uint32
    IxIndex    uint16
    TokenMint  []byte
    FromWallet []byte
    ToWallet   []byte
    Amount     uint64
}

type TransactionEvent struct {
    Slot      uint64
    TxIndex   uint32
    Signature []byte
}

type SolBalanceEvent struct {
    Slot    uint64
    Account []byte
    Amount  uint64
}

type TokenBalanceEvent struct {
    Slot   uint64
    Owner  []byte
    Token  []byte
    Amount uint64
}
```

### Generic Processing Functions (`db/events_v2.go`)

#### `processEventsByPartition[T]`

- Groups events by partition using the provided `getSlot` function
- Processes each partition in parallel
- Handles batching within partitions
- Supports both legacy and new schema partitioning

#### `groupEventsByPartition[T]`

- Generic function for grouping events by partition
- Uses different partition calculation based on `useNewSchema` flag
- Returns map of partition name to event slice

### New Processing Methods

```go
// V2 Event Processing
func (r *Repo) ProcessSolTransferEventsV2(ctx context.Context, events []*pb.SolTransferEventV2) error
func (r *Repo) ProcessTokenTransferEventsV2(ctx context.Context, events []*pb.TokenTransferEventV2) error
func (r *Repo) ProcessTransactionEvents(ctx context.Context, events []*pb.TransactionEvent) error
func (r *Repo) ProcessSolBalanceEvents(ctx context.Context, events []*pb.SolBalanceEvent) error
func (r *Repo) ProcessTokenBalanceEvents(ctx context.Context, events []*pb.TokenBalanceEvent) error
```

### Updated Main Processing (`main.go`)

The main message processing loop now handles both legacy and new events:

```go
// Legacy Events (backward compatibility)
solTransfers := make([]*pb.SolTransferEvent, 0)
tokenTransfers := make([]*pb.TokenTransferEvent, 0)
slotTimes := make([]*pb.SlotTime, 0)

// New V2 Events
solTransfersV2 := make([]*pb.SolTransferEventV2, 0)
tokenTransfersV2 := make([]*pb.TokenTransferEventV2, 0)
transactions := make([]*pb.TransactionEvent, 0)
solBalances := make([]*pb.SolBalanceEvent, 0)
tokenBalances := make([]*pb.TokenBalanceEvent, 0)
```

### Enhanced Metrics

The metrics system now tracks both legacy and new event types:

```go
type messageRecord struct {
    // Legacy metrics
    solCount   int
    tokenCount int
    slotCount  int
    
    // New V2 metrics
    solV2Count      int
    tokenV2Count    int
    transactionCount int
    solBalanceCount  int
    tokenBalanceCount int
}
```

## Database Schema Support

### New Tables

- `transaction_signatures` - Links slot/tx_index to signature
- `sol_transfer_events` - V2 SOL transfers without signature
- `token_transfer_events` - V2 token transfers without signature
- `sol_balances` - SOL balance updates
- `token_balances` - Token balance updates

### Partitioning Strategy

- **Legacy**: 4 days per partition (864,000 slots)
- **New V2**: 1 day per partition (216,000 slots)
- **Automatic**: Partition calculation based on event type

## Usage Examples

### Processing Mixed Events

```go
// A single message can contain both legacy and new events
batchedEvent := &pb.BatchedSolanaEvent{
    Events: []*pb.SolanaEvent{
        {Event: &pb.SolanaEvent_SolTransfer{...}},      // Legacy
        {Event: &pb.SolanaEvent_SolTransferV2{...}},    // New V2
        {Event: &pb.SolanaEvent_Transaction{...}},      // New V2
    },
}
```

### Generic Processing

```go
// Legacy events use old partitioning
processEventsByPartition(
    ctx, transfers, "sol_transfers", false,
    func(t models.SolTransfer) uint64 { return t.Slot },
    r.insertSolBatchToPartition,
)

// New events use new partitioning
processEventsByPartition(
    ctx, transfersV2, "sol_transfer_events", true,
    func(t models.SolTransferEventV2) uint64 { return t.Slot },
    r.insertSolTransferEventV2Batch,
)
```

## Performance Benefits

1. **Generic Functions**: Reduced code duplication and maintenance overhead
2. **Parallel Processing**: All event types processed concurrently within messages
3. **Efficient Partitioning**: Automatic partition calculation and grouping
4. **Batch Operations**: Efficient bulk inserts using temporary tables
5. **Retry Logic**: Built-in retry mechanism for database operations

## Migration Strategy

1. **Gradual Rollout**: New events can be introduced alongside existing ones
2. **Backward Compatibility**: Existing consumers continue to work unchanged
3. **Performance Monitoring**: Enhanced metrics track both old and new event processing
4. **Database Migration**: New tables and partitions created via Liquibase

## Configuration

The system uses the same configuration as before, with no additional settings required. The partitioning strategy is automatically determined based on the event type being processed.
