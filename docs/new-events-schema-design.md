# New Events Schema Design

## Overview

This document describes the new schema design for Solana events that improves upon the previous design with better partitioning strategy and normalized data structure.

## Key Changes

### 1. Table Structure Changes

#### Old Tables (Deprecated)

- `sol_transfers` - stored signature, ix_index, slot, from_wallet, to_wallet, amount
- `token_transfers` - stored signature, ix_index, slot, token_mint, from_wallet, to_wallet, amount

#### New Tables

- `transaction_signatures` - stores slot, tx_index, signature (PK: slot, tx_index)
- `sol_transfer_events` - stores slot, tx_index, ix_index, from_wallet, to_wallet, amount (PK: slot, tx_index, ix_index)
- `token_transfer_events` - stores slot, tx_index, ix_index, token_mint, from_wallet, to_wallet, amount (PK: slot, tx_index, ix_index)
- `sol_balances` - stores slot, account, amount (PK: slot, account)
- `token_balances` - stores slot, owner, token, amount (PK: slot, owner, token)

### 2. Partitioning Strategy

#### Old Strategy

- 4 days per partition (864,000 slots)
- Partition n included epochs 2n and 2n+1

#### New Strategy

- 1 day per partition (216,000 slots)
- Partition n starts at slot 216,000 * n
- Pre-created partitions 1600-1613 (slots 345,600,000-348,480,000)

### 3. Benefits of New Design

1. **Normalized Data**: Signatures are stored separately, reducing redundancy
2. **Better Partitioning**: Smaller partitions for more efficient queries
3. **Balance Tracking**: New tables for tracking SOL and token balances over time
4. **Simplified Indexing**: Removed wallet-specific indexes as requested
5. **Future-Proof**: Easier to extend with new event types

## Proto Message Changes

### New Message Types

1. **SolTransferEventV2**: Updated transfer event without signature
   - Uses `bytes` for wallet addresses (from_wallet, to_wallet)
   - Uses `uint64` for amount in lamports
2. **TokenTransferEventV2**: Updated token transfer event without signature
   - Uses `bytes` for wallet addresses and token mint
   - Uses `string` for amount (for precision)
3. **TransactionEvent**: Links slot/tx_index to signature
   - Uses `bytes` for signature
4. **SolBalanceEvent**: SOL balance updates
   - Uses `bytes` for account address
   - Uses `uint64` for amount in lamports
5. **TokenBalanceEvent**: Token balance updates
   - Uses `bytes` for owner and token addresses
   - Uses `string` for amount (for precision)

### Updated SolanaEvent Oneof

The `SolanaEvent` message now includes:

- `sol_transfer_v2` (field 4)
- `token_transfer_v2` (field 5)
- `transaction` (field 6)
- `sol_balance` (field 7)
- `token_balance` (field 8)

### Data Type Consistency

- **Wallet addresses**: Stored as `bytes` in both database (BYTEA) and proto messages
- **Token mints**: Stored as `bytes` in both database (BYTEA) and proto messages
- **Signatures**: Stored as `bytes` in both database (BYTEA) and proto messages
- **SOL amounts**: Stored as `uint64` (lamports) in proto, `NUMERIC(20,0)` in database
- **Token amounts**: Stored as `string` in proto, `NUMERIC(20,0)` in database for precision

## Migration Notes

1. **Backward Compatibility**: Old message types are preserved for existing consumers
2. **Gradual Migration**: New events can be sent alongside old ones
3. **Data Migration**: Existing data can be migrated using the signature lookup table
4. **Partition Management**: New partitions are pre-created for immediate use

## Usage Examples

### Querying Transfer Events

```sql
-- Get SOL transfers for a specific slot range
SELECT * FROM sol_transfer_events 
WHERE slot BETWEEN ********* AND *********;

-- Get token transfers with signature lookup
SELECT te.*, ts.signature 
FROM token_transfer_events te
JOIN transaction_signatures ts ON te.slot = ts.slot AND te.tx_index = ts.tx_index
WHERE te.slot = *********;
```

### Querying Balances

```sql
-- Get SOL balance for an account at a specific slot
SELECT amount FROM sol_balances 
WHERE account = '\x...' AND slot = *********;

-- Get token balance history
SELECT * FROM token_balances 
WHERE owner = '\x...' AND token = '\x...'
ORDER BY slot DESC LIMIT 10;
```

## Implementation Notes

1. **Indexes**: Only slot-based indexes are created for performance
2. **Data Types**: All wallet addresses and tokens stored as BYTEA for efficiency
3. **Amounts**: SOL amounts in lamports, token amounts as strings for precision
4. **Partitioning**: All tables use the same partitioning strategy for consistency
5. **Proto Efficiency**: Using `bytes` instead of `string` for addresses reduces serialization overhead
